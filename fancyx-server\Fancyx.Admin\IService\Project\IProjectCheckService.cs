using Fancyx.Admin.IService.Project.Dtos;
using Fancyx.Admin.IService.Mobile.Dtos;
using Fancyx.Shared.Models;
using System.Threading.Tasks;

namespace Fancyx.Admin.IService.Project
{
    /// <summary>
    /// 项目检查服务接口
    /// </summary>
    public interface IProjectCheckService
    {
        /// <summary>
        /// 获取项目检查分页列表
        /// </summary>
        Task<PagedResult<ProjectCheckListDto>> GetProjectCheckListAsync(ProjectCheckQueryDto queryDto);

        /// <summary>
        /// 根据ID获取项目检查详情
        /// </summary>
        Task<ProjectCheckDto> GetProjectCheckByIdAsync(long id);

        /// <summary>
        /// 创建项目检查
        /// </summary>
        Task<long> CreateProjectCheckAsync(CreateProjectCheckDto createDto);

        /// <summary>
        /// 更新项目检查
        /// </summary>
        Task<bool> UpdateProjectCheckAsync(long id, UpdateProjectCheckDto updateDto);

        /// <summary>
        /// 删除项目检查
        /// </summary>
        Task<bool> DeleteProjectCheckAsync(long id);

        /// <summary>
        /// 根据项目生成检查任务
        /// </summary>
        Task<long> GenerateCheckTaskAsync(long projectId, string checkType, string secondaryCategory);

        // ========== Mobile端专用方法 ==========

        /// <summary>
        /// 获取移动端检查任务列表
        /// </summary>
        Task<PagedResult<MobileCheckTaskDto>> GetMobileCheckTasksAsync(MobileCheckQueryDto queryDto);

        /// <summary>
        /// 创建移动端检查任务
        /// </summary>
        Task<long> CreateMobileCheckTaskAsync(MobileCreateCheckDto createDto);

        /// <summary>
        /// 更新移动端检查任务
        /// </summary>
        Task<bool> UpdateMobileCheckTaskAsync(long id, MobileUpdateCheckDto updateDto);

        /// <summary>
        /// 检查是否有隐患
        /// </summary>
        /// <param name="checkId">检查ID</param>
        /// <returns>是否有隐患</returns>
        Task<bool> HasDangersAsync(long checkId);

        /// <summary>
        /// 删除移动端检查任务
        /// </summary>
        /// <param name="id">检查任务ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteMobileCheckTaskAsync(long id);

        /// <summary>
        /// 恢复移动端检查任务
        /// </summary>
        /// <param name="id">检查任务ID</param>
        /// <returns>是否成功</returns>
        Task<bool> ResumeMobileCheckTaskAsync(long id);

        /// <summary>
        /// 获取移动端检查详情
        /// </summary>
        /// <param name="id">检查任务ID</param>
        /// <returns>检查详情</returns>
        Task<MobileCheckDetailDto> GetMobileCheckDetailAsync(long id);

        /// <summary>
        /// 批量操作移动端检查
        /// </summary>
        /// <param name="operationDto">操作信息</param>
        /// <returns>操作结果</returns>
        Task<MobileBatchOperationResultDto> BatchOperationMobileChecksAsync(MobileBatchOperationDto operationDto);

        /// <summary>
        /// 检查是否可以开始检查
        /// </summary>
        Task<(bool CanStart, string Reason)> CanStartCheckAsync(long id);

        /// <summary>
        /// 开始移动端检查任务
        /// </summary>
        Task<MobileCheckDetailDto> StartMobileCheckTaskAsync(long id, MobileStartCheckDto startDto);

        /// <summary>
        /// 暂停移动端检查任务
        /// </summary>
        Task<bool> PauseMobileCheckTaskAsync(long id, MobilePauseCheckDto pauseDto);

        /// <summary>
        /// 完成移动端检查任务
        /// </summary>
        Task<bool> CompleteMobileCheckTaskAsync(long id, MobileCheckCompleteDto completeDto);

        /// <summary>
        /// 保存移动端检查进度
        /// </summary>
        Task<bool> SaveMobileCheckProgressAsync(long id, MobileCheckProgressDto progressDto);

        /// <summary>
        /// 批量操作移动端检查任务
        /// </summary>
        Task<MobileBatchOperationResultDto> BatchOperateMobileCheckTasksAsync(MobileBatchCheckOperationDto operationDto);

        /// <summary>
        /// 同步移动端检查数据
        /// </summary>
        Task<MobileSyncResultDto> SyncMobileCheckDataAsync(MobileSyncDataDto syncDto);

        /// <summary>
        /// 导出移动端检查报告
        /// </summary>
        Task<MobileExportResultDto> ExportMobileCheckReportAsync(long id, string format);
    }
}