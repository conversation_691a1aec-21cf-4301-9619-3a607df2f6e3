using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端位置信息DTO
/// </summary>
public class MobileLocationDto
{
    /// <summary>
    /// 纬度
    /// </summary>
    public double Latitude { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double Longitude { get; set; }

    /// <summary>
    /// 海拔高度
    /// </summary>
    public double? Altitude { get; set; }

    /// <summary>
    /// 精度（米）
    /// </summary>
    public double? Accuracy { get; set; }

    /// <summary>
    /// 地址描述
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 获取位置的时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 移动端设备信息DTO
/// </summary>
public class MobileDeviceInfoDto
{
    /// <summary>
    /// 设备类型（iOS/Android）
    /// </summary>
    public string DeviceType { get; set; } = string.Empty;

    /// <summary>
    /// 设备型号
    /// </summary>
    public string DeviceModel { get; set; } = string.Empty;

    /// <summary>
    /// 操作系统版本
    /// </summary>
    public string OSVersion { get; set; } = string.Empty;

    /// <summary>
    /// 应用版本
    /// </summary>
    public string AppVersion { get; set; } = string.Empty;

    /// <summary>
    /// 设备唯一标识
    /// </summary>
    public string DeviceId { get; set; } = string.Empty;

    /// <summary>
    /// 网络类型
    /// </summary>
    public string? NetworkType { get; set; }

    /// <summary>
    /// 电池电量
    /// </summary>
    public int? BatteryLevel { get; set; }
}

/// <summary>
/// 移动端快速检查DTO
/// </summary>
public class MobileQuickCheckDto
{
    /// <summary>
    /// 检查类型
    /// </summary>
    [Required]
    public string CheckType { get; set; } = string.Empty;

    /// <summary>
    /// 二级分类
    /// </summary>
    [Required]
    public string SecondaryCategory { get; set; } = string.Empty;

    /// <summary>
    /// 检查组长
    /// </summary>
    [Required]
    public string CheckLeader { get; set; } = string.Empty;

    /// <summary>
    /// 检查组员
    /// </summary>
    public string? CheckMembers { get; set; }

    /// <summary>
    /// 工程进度
    /// </summary>
    public string? ProjectProgress { get; set; }

    /// <summary>
    /// 检查内容
    /// </summary>
    public string? CheckContent { get; set; }

    /// <summary>
    /// 位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端项目简单信息DTO
/// </summary>
public class MobileProjectSimpleDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 项目编号
    /// </summary>
    public string ProjectNo { get; set; } = string.Empty;

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// 项目地址
    /// </summary>
    public string ProjectAddress { get; set; } = string.Empty;

    /// <summary>
    /// 现场负责人
    /// </summary>
    public string SiteManager { get; set; } = string.Empty;

    /// <summary>
    /// 负责人电话
    /// </summary>
    public string ManagerPhone { get; set; } = string.Empty;
}

/// <summary>
/// 移动端分页查询基类
/// </summary>
public class MobilePagedQueryDto
{
    /// <summary>
    /// 当前页码
    /// </summary>
    public int Current { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 搜索关键词
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// 排序字段
    /// </summary>
    public string? SortField { get; set; }

    /// <summary>
    /// 排序方向（asc/desc）
    /// </summary>
    public string? SortOrder { get; set; } = "desc";
}

/// <summary>
/// 移动端操作结果DTO
/// </summary>
public class MobileOperationResultDto
{
    /// <summary>
    /// 操作是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}
