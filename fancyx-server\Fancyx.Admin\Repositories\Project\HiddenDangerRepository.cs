using Fancyx.Admin.Entities.Project;
using Fancyx.Core.Interfaces;
using Fancyx.Repository;

namespace Fancyx.Admin.Repositories.Project
{
    /// <summary>
    /// 隐患明细仓储实现
    /// </summary>
    public class HiddenDangerRepository : IHiddenDangerRepository, IScopedDependency
    {
        private readonly IRepository<HiddenDangerDO> _repository;

        public HiddenDangerRepository(IRepository<HiddenDangerDO> repository)
        {
            _repository = repository;
        }

        public IRepository<HiddenDangerDO> GetRepository()
        {
            return _repository;
        }
    }
}
