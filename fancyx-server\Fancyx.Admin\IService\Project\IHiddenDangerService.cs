using Fancyx.Admin.IService.Project.Dtos;
using Fancyx.Admin.IService.Mobile.Dtos;
using Fancyx.Shared.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Fancyx.Admin.IService.Project
{
    /// <summary>
    /// 隐患明细服务接口
    /// </summary>
    public interface IHiddenDangerService
    {
        /// <summary>
        /// 获取隐患明细分页列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<HiddenDangerListDto>> GetHiddenDangerListAsync(HiddenDangerQueryDto queryDto);

        /// <summary>
        /// 根据ID获取隐患明细详情
        /// </summary>
        /// <param name="id">隐患明细ID</param>
        /// <returns>隐患明细信息</returns>
        Task<HiddenDangerDto> GetHiddenDangerByIdAsync(Guid id);

        /// <summary>
        /// 创建隐患明细
        /// </summary>
        /// <param name="createDto">创建信息</param>
        /// <returns>隐患明细ID</returns>
        Task<Guid> CreateHiddenDangerAsync(CreateHiddenDangerDto createDto);

        /// <summary>
        /// 更新隐患明细
        /// </summary>
        /// <param name="id">隐患明细ID</param>
        /// <param name="updateDto">更新信息</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateHiddenDangerAsync(Guid id, UpdateHiddenDangerDto updateDto);

        /// <summary>
        /// 删除隐患明细
        /// </summary>
        /// <param name="id">隐患明细ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteHiddenDangerAsync(Guid id);

        /// <summary>
        /// 确认隐患明细（将待确认状态转为正式记录）
        /// </summary>
        /// <param name="id">隐患明细ID</param>
        /// <returns>是否确认成功</returns>
        Task<bool> ConfirmHiddenDangerAsync(Guid id);


        /// <summary>
        /// 根据检查ID获取隐患明细列表
        /// </summary>
        /// <param name="projectCheckId">检查ID</param>
        /// <returns>隐患明细列表</returns>
        Task<List<HiddenDangerDto>> GetHiddenDangersByCheckIdAsync(long projectCheckId);

        // ========== Mobile端专用方法 ==========

        /// <summary>
        /// 获取移动端隐患列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>隐患列表</returns>
        Task<PagedResult<MobileHiddenDangerListDto>> GetMobileHiddenDangersAsync(MobileDangerQueryDto queryDto);

        /// <summary>
        /// 创建移动端隐患
        /// </summary>
        /// <param name="createDto">创建信息</param>
        /// <returns>隐患ID</returns>
        Task<long> CreateMobileHiddenDangerAsync(MobileHiddenDangerCreateDto createDto);

        /// <summary>
        /// 更新移动端隐患
        /// </summary>
        /// <param name="id">隐患ID</param>
        /// <param name="updateDto">更新信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateMobileHiddenDangerAsync(long id, MobileHiddenDangerUpdateDto updateDto);

        /// <summary>
        /// 获取隐患状态
        /// </summary>
        /// <param name="id">隐患ID</param>
        /// <returns>状态信息</returns>
        Task<MobileDangerStatusDto> GetDangerStatusAsync(long id);

        /// <summary>
        /// 删除移动端隐患
        /// </summary>
        /// <param name="id">隐患ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteMobileHiddenDangerAsync(long id);

        /// <summary>
        /// 获取移动端隐患详情
        /// </summary>
        /// <param name="id">隐患ID</param>
        /// <returns>隐患详情</returns>
        Task<MobileHiddenDangerDetailDto> GetMobileHiddenDangerDetailAsync(long id);

        /// <summary>
        /// 确认移动端隐患
        /// </summary>
        /// <param name="id">隐患ID</param>
        /// <param name="confirmDto">确认信息</param>
        /// <returns>是否成功</returns>
        Task<bool> ConfirmMobileHiddenDangerAsync(long id, MobileHiddenDangerConfirmDto confirmDto);

        /// <summary>
        /// 添加移动端整改记录
        /// </summary>
        /// <param name="recordDto">整改记录</param>
        /// <returns>是否成功</returns>
        Task<bool> AddMobileRectificationRecordAsync(MobileRectificationRecordDto recordDto);

        /// <summary>
        /// 批量上传多媒体文件
        /// </summary>
        /// <param name="uploadDto">上传信息</param>
        /// <returns>上传结果</returns>
        Task<MobileBatchUploadResultDto> BatchUploadMultimediaAsync(MobileMultimediaUploadDto uploadDto);

        /// <summary>
        /// 批量操作移动端隐患
        /// </summary>
        /// <param name="operationDto">操作信息</param>
        /// <returns>操作结果</returns>
        Task<MobileBatchOperationResultDto> BatchOperationMobileHiddenDangersAsync(MobileBatchOperationDto operationDto);

        /// <summary>
        /// 导出移动端隐患报告
        /// </summary>
        /// <param name="exportDto">导出参数</param>
        /// <returns>报告文件</returns>
        Task<MobileExportResultDto> ExportMobileHiddenDangerReportAsync(MobileExportRequestDto exportDto);

        /// <summary>
        /// 获取移动端隐患统计
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>统计结果</returns>
        Task<MobileHiddenDangerStatisticsDto> GetMobileHiddenDangerStatisticsAsync(MobileStatisticsQueryDto queryDto);

        /// <summary>
        /// 获取移动端隐患模板
        /// </summary>
        /// <param name="category">隐患类别</param>
        /// <returns>模板列表</returns>
        Task<List<MobileDangerTemplateDto>> GetMobileDangerTemplatesAsync(string? category = null);

        /// <summary>
        /// 同步移动端隐患数据
        /// </summary>
        /// <param name="syncDto">同步数据</param>
        /// <returns>同步结果</returns>
        Task<MobileSyncResultDto> SyncMobileHiddenDangerDataAsync(MobileSyncDataDto syncDto);
    }
}