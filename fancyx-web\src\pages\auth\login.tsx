import { Card, Form, Input, Button, Checkbox } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import './style/login.scss';
import { type LoginDto } from '@/api/auth.ts';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';
import { useAuthProvider } from '@/components/AuthProvider';

const LoginPage = () => {
  const [loading, setLoading] = useState(false);
  const [pageLoaded, setPageLoaded] = useState(false);
  const navigate = useNavigate();
  const { pwdLogin } = useAuthProvider();

  // 页面加载动画
  useEffect(() => {
    const timer = setTimeout(() => {
      setPageLoaded(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const onFinish = useCallback((values: LoginDto) => {
    setLoading(true);
    pwdLogin!(values)
      .then(() => {
        setLoading(false);
        // 添加成功动画效果
        const container = document.querySelector('.login-container');
        container?.classList.add('ai-login-success');
        setTimeout(() => {
          navigate('/');
        }, 500);
      })
      .catch((error) => {
        setLoading(false);
        // 错误处理已由AuthProvider处理，这里可以添加额外的视觉反馈
        const formContainer = document.querySelector('.form-container');
        formContainer?.classList.add('ai-error-shake');
        setTimeout(() => {
          formContainer?.classList.remove('ai-error-shake');
        }, 600);
      });
  }, [pwdLogin, navigate]);

  return (
    <div className={`login-container ${pageLoaded ? 'ai-page-loaded' : ''}`}>
      <div className="login-container-main ai-animate-fade-in">
        <Card className="login-card" variant="borderless">
          <div className="login-layout">
            {/* 左侧AI科技背景 */}
            <div className="login-bg-side ai-brand-section">
              <div className="ai-particles-container">
                {/* 粒子动画背景 - 优化性能 */}
                {useMemo(() =>
                  Array.from({ length: 20 }).map((_, index) => (
                    <div
                      key={index}
                      className="ai-particle"
                      style={{
                        left: `${Math.random() * 100}%`,
                        animationDelay: `${Math.random() * 20}s`,
                        animationDuration: `${15 + Math.random() * 10}s`
                      }}
                    />
                  )), []
                )}
              </div>
              <div className="bg-overlay ai-brand-content">
                <div className="ai-system-logo">
                  <div className="ai-logo-icon">🤖</div>
                </div>
                <h2 className="ai-system-title">工程现场安全隐患识别与报告生成系统</h2>
                <p className="ai-system-subtitle">AI-Powered Risk Detection Platform</p>
                <p className="ai-system-description">基于深度学习的智能隐患识别与风险评估平台</p>
                <div className="ai-features">
                  <div className="ai-feature-item">
                    <span className="ai-feature-icon">🔍</span>
                    <span>智能图像识别</span>
                  </div>
                  <div className="ai-feature-item">
                    <span className="ai-feature-icon">⚡</span>
                    <span>实时风险评估</span>
                  </div>
                  <div className="ai-feature-item">
                    <span className="ai-feature-icon">🛡️</span>
                    <span>预测性维护</span>
                  </div>
                  <div className="ai-feature-item">
                    <span className="ai-feature-icon">📊</span>
                    <span>数据驱动决策</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧登录表单 */}
            <div className="login-form-side">
              <div className="form-container">
                <h3 className="form-title ai-form-title">
                  <span className="ai-title-icon">🔐</span>
                  系统登录
                </h3>

                <Form name="login_form" initialValues={{ remember: true }} onFinish={onFinish} size="large" className="ai-login-form">
                  <Form.Item name="userName" rules={[{ required: true, message: '请输入账号' }]}>
                    <Input
                      prefix={<UserOutlined className="ai-input-icon" />}
                      placeholder="请输入用户账号"
                      className="ai-input"
                      autoComplete="username"
                    />
                  </Form.Item>

                  <Form.Item name="password" rules={[{ required: true, message: '请输入密码' }]}>
                    <Input.Password
                      prefix={<LockOutlined className="ai-input-icon" />}
                      placeholder="请输入登录密码"
                      className="ai-input"
                      autoComplete="current-password"
                    />
                  </Form.Item>

                  <Form.Item>
                    <Form.Item name="remember" valuePropName="checked" noStyle>
                      <Checkbox>记住密码</Checkbox>
                    </Form.Item>
                  </Form.Item>

                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      block
                      loading={loading}
                      className="ai-login-button"
                    >
                      {loading ? '正在验证...' : '立即登录'}
                    </Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        </Card>

        <div className="login-footer ai-footer">
          <div className="ai-footer-content">
            <span>工程现场安全隐患识别与报告生成系统 © {new Date().getFullYear()}</span>
            <span className="ai-footer-separator">|</span>
            <span>AI驱动的智能安全管理平台</span>
          </div>
          <div className="ai-footer-links">
            
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
