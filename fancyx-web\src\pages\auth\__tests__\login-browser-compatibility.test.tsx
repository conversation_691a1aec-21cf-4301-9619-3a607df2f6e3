import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from '@/store';
import LoginPage from '../login';
import { AuthProvider } from '@/components/AuthProvider';

// Mock AuthProvider
jest.mock('@/components/AuthProvider', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuthProvider: () => ({
    pwdLogin: jest.fn(),
  }),
}));

const renderLoginPage = () => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      </BrowserRouter>
    </Provider>
  );
};

// Mock different browser environments
const mockUserAgent = (userAgent: string) => {
  Object.defineProperty(window.navigator, 'userAgent', {
    writable: true,
    value: userAgent,
  });
};

describe('LoginPage Browser Compatibility', () => {
  beforeEach(() => {
    // Reset to default
    mockUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
  });

  test('works in Chrome', () => {
    mockUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    renderLoginPage();
    
    expect(screen.getByText('工程现场安全隐患识别与报告生成系统')).toBeInTheDocument();
    expect(screen.getByText('系统登录')).toBeInTheDocument();
  });

  test('works in Firefox', () => {
    mockUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0');
    
    renderLoginPage();
    
    expect(screen.getByText('工程现场安全隐患识别与报告生成系统')).toBeInTheDocument();
    expect(screen.getByText('系统登录')).toBeInTheDocument();
  });

  test('works in Safari', () => {
    mockUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15');
    
    renderLoginPage();
    
    expect(screen.getByText('工程现场安全隐患识别与报告生成系统')).toBeInTheDocument();
    expect(screen.getByText('系统登录')).toBeInTheDocument();
  });

  test('works in Edge', () => {
    mockUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59');
    
    renderLoginPage();
    
    expect(screen.getByText('工程现场安全隐患识别与报告生成系统')).toBeInTheDocument();
    expect(screen.getByText('系统登录')).toBeInTheDocument();
  });

  test('handles CSS Grid fallback', () => {
    // Mock lack of CSS Grid support
    const originalSupports = CSS.supports;
    CSS.supports = jest.fn().mockImplementation((property, value) => {
      if (property === 'display' && value === 'grid') {
        return false;
      }
      return originalSupports.call(CSS, property, value);
    });

    renderLoginPage();
    
    const featuresContainer = document.querySelector('.ai-features');
    expect(featuresContainer).toBeInTheDocument();

    // Restore original CSS.supports
    CSS.supports = originalSupports;
  });

  test('handles Flexbox support', () => {
    renderLoginPage();
    
    const loginLayout = document.querySelector('.login-layout');
    const formSide = document.querySelector('.login-form-side');
    
    expect(loginLayout).toBeInTheDocument();
    expect(formSide).toBeInTheDocument();
  });

  test('handles backdrop-filter fallback', () => {
    renderLoginPage();
    
    const loginCard = document.querySelector('.login-card');
    const formSide = document.querySelector('.login-form-side');
    
    expect(loginCard).toBeInTheDocument();
    expect(formSide).toBeInTheDocument();
  });

  test('handles CSS custom properties fallback', () => {
    renderLoginPage();
    
    // 验证即使不支持CSS自定义属性，页面仍能正常显示
    const container = document.querySelector('.login-container');
    expect(container).toBeInTheDocument();
  });

  test('handles animation support detection', () => {
    renderLoginPage();
    
    const particles = document.querySelectorAll('.ai-particle');
    expect(particles).toHaveLength(20);
  });

  test('provides graceful degradation for older browsers', () => {
    // Mock older browser
    mockUserAgent('Mozilla/5.0 (Windows NT 6.1; WOW64; Trident/7.0; rv:11.0) like Gecko');
    
    renderLoginPage();
    
    // 基本功能应该仍然可用
    expect(screen.getByText('工程现场安全隐患识别与报告生成系统')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('请输入用户账号')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('请输入登录密码')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '立即登录' })).toBeInTheDocument();
  });
});