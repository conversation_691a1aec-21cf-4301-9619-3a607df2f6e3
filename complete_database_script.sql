/*
 完整数据库创建脚本 - 包含原有表和新增表
 
 Source Server Type    : PostgreSQL
 Target Server Version : 160009 (160009)
 File Encoding         : 65001
 
 Date: 2025/07/24
 
 说明：
 1. 此脚本包含原有的所有表结构
 2. 新增了项目管理相关的8个表
 3. 建议在执行前备份现有数据库
*/

-- ========================================
-- 新增表结构 - 项目管理模块
-- ========================================

-- ----------------------------
-- Table structure for sys_project
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_project";
CREATE TABLE "public"."sys_project" (
  "id" int8 NOT NULL,
  "creator_id" uuid NOT NULL,
  "creation_time" timestamp(6) NOT NULL,
  "last_modification_time" timestamp(6),
  "last_modifier_id" uuid,
  "is_deleted" bool NOT NULL DEFAULT false,
  "deleter_id" uuid,
  "deletion_time" timestamp(6),
  "project_no" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "project_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "project_address" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
  "region" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "project_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "related_contract" varchar(100) COLLATE "pg_catalog"."default",
  "site_manager" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "manager_phone" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "manager_position" varchar(50) COLLATE "pg_catalog"."default",
  "start_date" timestamp(6),
  "end_date" timestamp(6),
  "project_status" int4 NOT NULL DEFAULT 1,
  "description" varchar(1000) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default"
);

-- 项目表注释
COMMENT ON COLUMN "public"."sys_project"."id" IS '项目ID';
COMMENT ON COLUMN "public"."sys_project"."project_no" IS '项目编号';
COMMENT ON COLUMN "public"."sys_project"."project_name" IS '项目名称';
COMMENT ON COLUMN "public"."sys_project"."project_address" IS '项目地址';
COMMENT ON COLUMN "public"."sys_project"."region" IS '所属区域';
COMMENT ON COLUMN "public"."sys_project"."project_type" IS '项目类型（生产安全、质量安全）';
COMMENT ON COLUMN "public"."sys_project"."related_contract" IS '关联合同';
COMMENT ON COLUMN "public"."sys_project"."site_manager" IS '现场负责人';
COMMENT ON COLUMN "public"."sys_project"."manager_phone" IS '负责人电话';
COMMENT ON COLUMN "public"."sys_project"."manager_position" IS '负责人职务';
COMMENT ON COLUMN "public"."sys_project"."start_date" IS '开工日期';
COMMENT ON COLUMN "public"."sys_project"."end_date" IS '竣工日期';
COMMENT ON COLUMN "public"."sys_project"."project_status" IS '项目状态';
COMMENT ON COLUMN "public"."sys_project"."description" IS '项目描述';
COMMENT ON COLUMN "public"."sys_project"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."sys_project" IS '项目表';

-- ----------------------------
-- Table structure for sys_project_check
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_project_check";
CREATE TABLE "public"."sys_project_check" (
  "id" int8 NOT NULL,
  "creator_id" uuid NOT NULL,
  "creation_time" timestamp(6) NOT NULL,
  "last_modification_time" timestamp(6),
  "last_modifier_id" uuid,
  "is_deleted" bool NOT NULL DEFAULT false,
  "deleter_id" uuid,
  "deletion_time" timestamp(6),
  "project_id" int8 NOT NULL,
  "check_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "secondary_category" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "check_date" timestamp(6) NOT NULL,
  "client_unit" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "check_leader" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "check_members" varchar(500) COLLATE "pg_catalog"."default",
  "project_progress" varchar(100) COLLATE "pg_catalog"."default",
  "check_content" varchar(2000) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default"
);

-- 项目检查表注释
COMMENT ON COLUMN "public"."sys_project_check"."id" IS '检查ID';
COMMENT ON COLUMN "public"."sys_project_check"."project_id" IS '项目ID';
COMMENT ON COLUMN "public"."sys_project_check"."check_type" IS '检查类型（生产安全、质量安全）';
COMMENT ON COLUMN "public"."sys_project_check"."secondary_category" IS '二级分类';
COMMENT ON COLUMN "public"."sys_project_check"."check_date" IS '检查日期';
COMMENT ON COLUMN "public"."sys_project_check"."client_unit" IS '委托单位';
COMMENT ON COLUMN "public"."sys_project_check"."check_leader" IS '检查组长';
COMMENT ON COLUMN "public"."sys_project_check"."check_members" IS '检查组员';
COMMENT ON COLUMN "public"."sys_project_check"."project_progress" IS '工程进度';
COMMENT ON COLUMN "public"."sys_project_check"."check_content" IS '检查内容';
COMMENT ON COLUMN "public"."sys_project_check"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."sys_project_check" IS '项目检查表';
