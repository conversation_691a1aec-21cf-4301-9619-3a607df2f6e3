import httpClient from '@/utils/httpClient';

// 隐患明细列表查询
export function getHiddenDangerList(params: any) {
  return httpClient.get('/api/hidden-danger/list', { params });
}

// 获取隐患明细详情
export function getHiddenDangerById(id: string) {
  return httpClient.get(`/api/hidden-danger/${id}`);
}

// 创建隐患明细
export function createHiddenDanger(data: any) {
  return httpClient.post('/api/hidden-danger', data);
}

// 更新隐患明细
export function updateHiddenDanger(id: string, data: any) {
  return httpClient.put(`/api/hidden-danger/${id}`, data);
}

// 删除隐患明细
export function deleteHiddenDanger(id: string) {
  return httpClient.delete(`/api/hidden-danger/${id}`);
}

// 确认隐患明细
export function confirmHiddenDanger(id: string) {
  return httpClient.post(`/api/hidden-danger/${id}/confirm`);
}

// 根据检查ID获取隐患明细列表
export function getHiddenDangersByCheckId(projectCheckId: string) {
  return httpClient.get(`/api/hidden-danger/by-check/${projectCheckId}`);
}

// AI识别生成隐患明细
export function aiRecognitionHiddenDanger(formData: FormData) {
  return httpClient.post('/api/hidden-danger/ai-recognition', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
} 