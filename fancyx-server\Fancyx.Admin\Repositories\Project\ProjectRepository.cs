using Fancyx.Admin.Entities.Project;
using Fancyx.Core.Interfaces;
using Fancyx.Repository;

namespace Fancyx.Admin.Repositories.Project
{
    /// <summary>
    /// 项目仓储实现
    /// </summary>
    public class ProjectRepository : IProjectRepository, IScopedDependency
    {
        private readonly IRepository<ProjectDO> _repository;

        public ProjectRepository(IRepository<ProjectDO> repository)
        {
            _repository = repository;
        }

        public IRepository<ProjectDO> GetRepository()
        {
            return _repository;
        }
    }
}
