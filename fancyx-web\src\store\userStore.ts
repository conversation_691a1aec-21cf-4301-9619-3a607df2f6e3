import dayjs from 'dayjs';
import type { FrontendMenu } from '@/api/auth';
import { makeAutoObservable, runInAction } from 'mobx';

export type TokenInfo = {
  sessionId: string;
  accessToken: string;
  refreshToken: string;
  expiredTime: Date;
};

export type UserAuthInfo = {
  userId: string;
  userName: string;
  nickName?: string;
  avatar: string;
  sex: number;
  permissions: string[];
  menus: FrontendMenu[];
};

const USER_TOKEN_KEY = 'user_token';
const USER_INFO_KEY = 'user_info';
const MENU_LIST_KEY = 'menu_list';

class UserStore {
  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
    this.initState();
  }

  userInfo: UserAuthInfo | null = null;
  menuList: FrontendMenu[] = [];
  private _token: TokenInfo | null = null;

  private initState() {
    const userInfoStorage = localStorage.getItem(USER_INFO_KEY);
    if (userInfoStorage) {
      this.userInfo = JSON.parse(userInfoStorage);
    }
    const menuListStorage = localStorage.getItem(MENU_LIST_KEY);
    if (menuListStorage) {
      this.menuList = JSON.parse(menuListStorage);
    }
    const tokenStorage = localStorage.getItem(USER_TOKEN_KEY);
    if (tokenStorage) {
      this._token = JSON.parse(tokenStorage);
    }
  }

  get token(): TokenInfo | null {
    return this._token;
  }

  /**
   * 是否登录
   */
  isAuthenticated(): boolean {
    const tokenInfo = this.token;
    if (!tokenInfo || !tokenInfo.accessToken) return false;
    
    // 检查 token 是否过期（提前5分钟判断为过期）
    const now = new Date();
    const expiredTime = new Date(tokenInfo.expiredTime);
    const bufferTime = 5 * 60 * 1000; // 5分钟缓冲时间
    
    return expiredTime.getTime() > (now.getTime() + bufferTime);
  }

  /**
   * 设置token
   */
  setToken(token: TokenInfo) {
    localStorage.setItem(USER_TOKEN_KEY, JSON.stringify(token));
    runInAction(() => {
      this._token = token;
    });
  }

  /**
   * 刷新token
   * @param accessToken
   * @param refreshToken
   * @param expiredTime
   */
  refreshToken(accessToken: string, refreshToken: string, expiredTime: Date) {
    const tokenInfo = this.token;
    if (tokenInfo) {
      const newTokenInfo = {
        ...tokenInfo,
        accessToken,
        refreshToken,
        expiredTime
      };
      this.setToken(newTokenInfo);
    }
  }

  setUserInfo(userInfo: UserAuthInfo) {
    if (userInfo) {
      const _menuList = this.flattenTreeDFS(userInfo.menus);
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
      localStorage.setItem(MENU_LIST_KEY, JSON.stringify(_menuList));
      runInAction(() => {
        this.userInfo = userInfo;
        this.menuList = _menuList;
      });
    }
  }

  logout() {
    localStorage.removeItem(USER_INFO_KEY);
    localStorage.removeItem(USER_TOKEN_KEY);
    localStorage.removeItem(MENU_LIST_KEY);
    runInAction(() => {
      this.userInfo = null;
      this.menuList = [];
      this._token = null;
    });
  }

  flattenTreeDFS(menus: FrontendMenu[]): FrontendMenu[] {
    const result: FrontendMenu[] = [];

    function traverse(node: FrontendMenu) {
      if (!node) return;

      result.push(node); // 前序遍历
      // 遍历所有子节点
      node.children?.forEach((child) => traverse(child));
    }

    menus.forEach((item) => {
      traverse(item);
    });
    return result;
  }
}

export default new UserStore();
