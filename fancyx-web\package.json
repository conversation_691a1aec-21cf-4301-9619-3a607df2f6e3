{"name": "hidden-danger-recognition-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development --host 0.0.0.0", "build": "tsc -b && vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@reduxjs/toolkit": "^2.6.1", "@tanstack/react-query": "^5.81.5", "antd": "^5.25.4", "axios": "^1.9.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-redux": "^9.2.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.6.2", "redux-persist": "^6.0.0", "styled-components": "^6.1.18", "use-deep-compare-effect": "^1.8.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@iconify/react": "^6.0.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/axios": "^0.14.4", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.17", "@types/node": "^24.0.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.5.3", "sass": "^1.89.2", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}