// @ts-nocheck
import React, { useEffect } from 'react';
import { Form, Input, Button, message, DatePicker, Select, Card, Row, Col } from 'antd';
import { createProject, updateProject, getProjectById, generateProjectNo } from '@/api/project';
import { useNavigate, useParams } from 'react-router-dom';

const { RangePicker } = DatePicker;
const { Option } = Select;

// 上海16区选项
const SHANGHAI_DISTRICTS = [
  '黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区',
  '浦东新区', '闵行区', '宝山区', '嘉定区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'
];

// 项目类型选项
const PROJECT_TYPES = [
  { value: '生产安全', label: '生产安全' },
  { value: '质量安全', label: '质量安全' }
];

// 工程类型选项
const ENGINEERING_TYPES = [
  '房屋建筑工程', '市政公用工程', '铁路工程', '公路工程', 
  '水运工程', '水利水电工程', '电力工程', '矿山工程',
  '冶炼工程', '石油化工工程', '机电工程', '通信工程'
];

// 工程类别选项
const ENGINEERING_CATEGORIES = [
  '新建', '扩建', '改建', '技术改造', '修缮', '拆除'
];

const ProjectEdit: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = !!id;

  useEffect(() => {
    if (isEdit) {
      getProjectById(Number(id)).then(res => {
        const data = res.data;
        form.setFieldsValue({
          ...data,
          dateRange: [data.startDate ? data.startDate : null, data.endDate ? data.endDate : null],
        });
      });
    } else {
      generateProjectNo().then(res => {
        form.setFieldsValue({ projectNo: res.data });
      });
    }
  }, [id, isEdit, form]);

  const onFinish = async (values: any) => {
    const submitData = {
      ...values,
      startDate: values.dateRange?.[0],
      endDate: values.dateRange?.[1],
    };
    delete submitData.dateRange;
    if (isEdit) {
      await updateProject(Number(id), submitData);
      message.success('编辑成功');
    } else {
      await createProject(submitData);
      message.success('新增成功');
    }
    navigate('/project/list');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title={isEdit ? '编辑项目' : '新增项目'} bordered={false}>
        <Form form={form} layout="vertical" onFinish={onFinish}>
          {/* 基本信息 */}
          <Card type="inner" title="基本信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="projectNo" label="项目编号" rules={[{ required: true, message: '请输入项目编号' }]}>
                  <Input disabled={isEdit} placeholder="系统自动生成" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="projectName" label="项目名称" rules={[{ required: true, message: '请输入项目名称' }]}>
                  <Input placeholder="请输入项目名称" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="projectAddress" label="项目地址" rules={[{ required: true, message: '请输入项目地址' }]}>
                  <Input placeholder="请输入详细地址" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="region" label="所属区域" rules={[{ required: true, message: '请选择所属区域' }]}>
                  <Select placeholder="请选择所属区域" showSearch>
                    {SHANGHAI_DISTRICTS.map(district => (
                      <Option key={district} value={district}>{district}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="projectType" label="项目类型" rules={[{ required: true, message: '请选择项目类型' }]}>
                  <Select placeholder="请选择项目类型">
                    {PROJECT_TYPES.map(type => (
                      <Option key={type.value} value={type.value}>{type.label}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="engineeringType" label="工程类型" rules={[{ required: true, message: '请选择工程类型' }]}>
                  <Select placeholder="请选择工程类型" showSearch>
                    {ENGINEERING_TYPES.map(type => (
                      <Option key={type} value={type}>{type}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="engineeringCategory" label="工程类别" rules={[{ required: true, message: '请选择工程类别' }]}>
                  <Select placeholder="请选择工程类别">
                    {ENGINEERING_CATEGORIES.map(category => (
                      <Option key={category} value={category}>{category}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="dateRange" label="工期" rules={[{ required: true, message: '请选择工期' }]}>
                  <RangePicker 
                    style={{ width: '100%' }} 
                    placeholder={['开工日期', '竣工日期']}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 责任人信息 */}
          <Card type="inner" title="现场责任人" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="siteManager" label="现场负责人" rules={[{ required: true, message: '请输入现场负责人' }]}>
                  <Input placeholder="请输入负责人姓名" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  name="managerPhone" 
                  label="负责人电话" 
                  rules={[
                    { required: true, message: '请输入负责人电话' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                  ]}
                >
                  <Input placeholder="请输入11位手机号码" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="managerPosition" label="负责人职位">
                  <Input placeholder="请输入职位" />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 建设单位信息 */}
          <Card type="inner" title="建设单位信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="constructionUnit" label="建设单位" rules={[{ required: true, message: '请输入建设单位' }]}>
                  <Input placeholder="请输入建设单位名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="constructionCompany" label="施工单位" rules={[{ required: true, message: '请输入施工单位' }]}>
                  <Input placeholder="请输入施工单位名称" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="supervisionUnit" label="监理单位">
                  <Input placeholder="请输入监理单位名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="designUnit" label="设计单位">
                  <Input placeholder="请输入设计单位名称" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="constructionPermitNo" label="施工许可证号">
                  <Input placeholder="请输入施工许可证号" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="relatedContract" label="关联合同">
                  <Input placeholder="请输入关联合同信息" />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 其他信息 */}
          <Card type="inner" title="其他信息" style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="hazardousChemicalWorkers" label="危化品从业人数">
                  <Input 
                    type="number" 
                    placeholder="请输入人数" 
                    min={0}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                {/* 预留扩展字段 */}
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="remarks" label="备注">
                  <Input.TextArea 
                    rows={3} 
                    placeholder="请输入项目备注信息"
                    maxLength={500}
                    showCount
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 操作按钮 */}
          <Form.Item style={{ textAlign: 'center' }}>
            <Button type="primary" htmlType="submit" size="large" style={{ marginRight: 16 }}>
              {isEdit ? '保存修改' : '创建项目'}
            </Button>
            <Button size="large" onClick={() => navigate('/project/list')}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ProjectEdit; 