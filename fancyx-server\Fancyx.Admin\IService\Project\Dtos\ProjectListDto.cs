using System;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 项目列表 DTO
    /// </summary>
    public class ProjectListDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 项目编号
        /// </summary>
        public string ProjectNo { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 项目地址
        /// </summary>
        public string ProjectAddress { get; set; }

        /// <summary>
        /// 所属区域
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// 项目类型
        /// </summary>
        public string ProjectType { get; set; }

        /// <summary>
        /// 现场负责人
        /// </summary>
        public string SiteManager { get; set; }

        /// <summary>
        /// 负责人电话
        /// </summary>
        public string ManagerPhone { get; set; }

        /// <summary>
        /// 开工日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 竣工日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 工程类型
        /// </summary>
        public string EngineeringType { get; set; }

        /// <summary>
        /// 建设单位
        /// </summary>
        public string ConstructionUnit { get; set; }

        /// <summary>
        /// 施工单位
        /// </summary>
        public string ConstructionCompany { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatorName { get; set; }
    }
}