-- 项目菜单配置SQL脚本（最终版）
-- 请在执行前替换以下三个参数：
-- 1. your_menu_table_name -> 你的实际菜单表名
-- 2. your_current_user_id -> 你的用户ID
-- 3. your_tenant_id -> 你的租户ID

DO $$
DECLARE
    project_folder_id UUID := gen_random_uuid();
    current_user_id UUID := 'your_current_user_id'::UUID; -- 替换为实际用户ID
    current_tenant_id VARCHAR(18) := 'your_tenant_id'; -- 替换为实际租户ID
BEGIN
    -- 1. 插入项目管理文件夹
    INSERT INTO "your_menu_table_name" (
        id, creator_id, creation_time, 
        title, icon, path, component, menu_type, permission, 
        parent_id, sort, display, tenant_id
    ) VALUES (
        project_folder_id,
        current_user_id,
        NOW(),
        '项目管理',
        'project',
        '/project',
        NULL,
        1, -- 文件夹类型
        '', -- permission不能为NULL
        NULL,
        10,
        true,
        current_tenant_id
    );

    -- 2. 插入项目列表菜单
    INSERT INTO "your_menu_table_name" (
        id, creator_id, creation_time,
        title, icon, path, component, menu_type, permission, 
        parent_id, sort, display, tenant_id
    ) VALUES (
        gen_random_uuid(),
        current_user_id,
        NOW(),
        '项目列表',
        'list',
        '/project/list',
        'pages/project/list',
        2, -- 菜单类型
        'Project.List',
        project_folder_id,
        1,
        true,
        current_tenant_id
    );

    -- 3. 插入项目编辑菜单（解决404的关键！）
    INSERT INTO "your_menu_table_name" (
        id, creator_id, creation_time,
        title, icon, path, component, menu_type, permission, 
        parent_id, sort, display, tenant_id
    ) VALUES (
        gen_random_uuid(),
        current_user_id,
        NOW(),
        '项目编辑',
        'edit',
        '/project/edit',
        'pages/project/edit',
        2, -- 菜单类型
        'Project.Edit',
        project_folder_id,
        2,
        false, -- 不显示在侧边栏
        current_tenant_id
    );

    -- 4. 插入项目详情菜单
    INSERT INTO "your_menu_table_name" (
        id, creator_id, creation_time,
        title, icon, path, component, menu_type, permission, 
        parent_id, sort, display, tenant_id
    ) VALUES (
        gen_random_uuid(),
        current_user_id,
        NOW(),
        '项目详情',
        'detail',
        '/project/detail/:id',
        'pages/project/detail',
        2, -- 菜单类型
        'Project.Detail',
        project_folder_id,
        3,
        false, -- 不显示在侧边栏
        current_tenant_id
    );

    RAISE NOTICE '✅ 项目菜单配置添加完成！';
    RAISE NOTICE '📁 项目管理文件夹ID: %', project_folder_id;
END $$;

-- 验证插入结果（可选执行）
-- SELECT title, path, menu_type, display 
-- FROM "your_menu_table_name" 
-- WHERE title LIKE '%项目%' 
-- ORDER BY sort;