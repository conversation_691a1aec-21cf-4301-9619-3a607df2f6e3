using System;
using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 更新项目检查 DTO
    /// </summary>
    public class UpdateProjectCheckDto
    {
        /// <summary>
        /// 检查类型
        /// </summary>
        [Required(ErrorMessage = "检查类型不能为空")]
        [MaxLength(20, ErrorMessage = "检查类型不能超过20个字符")]
        public string CheckType { get; set; }

        /// <summary>
        /// 二级分类
        /// </summary>
        [Required(ErrorMessage = "二级分类不能为空")]
        [MaxLength(50, ErrorMessage = "二级分类不能超过50个字符")]
        public string SecondaryCategory { get; set; }

        /// <summary>
        /// 检查日期
        /// </summary>
        [Required(ErrorMessage = "检查日期不能为空")]
        public DateTime CheckDate { get; set; }

        /// <summary>
        /// 委托单位
        /// </summary>
        [Required(ErrorMessage = "委托单位不能为空")]
        [MaxLength(200, ErrorMessage = "委托单位不能超过200个字符")]
        public string ClientUnit { get; set; }

        /// <summary>
        /// 检查组长
        /// </summary>
        [Required(ErrorMessage = "检查组长不能为空")]
        [MaxLength(50, ErrorMessage = "检查组长不能超过50个字符")]
        public string CheckLeader { get; set; }

        /// <summary>
        /// 检查组员
        /// </summary>
        [MaxLength(500, ErrorMessage = "检查组员不能超过500个字符")]
        public string CheckMembers { get; set; }

        /// <summary>
        /// 工程进度
        /// </summary>
        [MaxLength(100, ErrorMessage = "工程进度不能超过100个字符")]
        public string ProjectProgress { get; set; }

        /// <summary>
        /// 检查内容
        /// </summary>
        [MaxLength(2000, ErrorMessage = "检查内容不能超过2000个字符")]
        public string CheckContent { get; set; }
    }
}