using Fancyx.Admin.Entities.Project;
using Fancyx.Core.Interfaces;
using Fancyx.Repository;

namespace Fancyx.Admin.Repositories.Project
{
    /// <summary>
    /// 项目检查仓储实现
    /// </summary>
    public class ProjectCheckRepository : IProjectCheckRepository, IScopedDependency
    {
        private readonly IRepository<ProjectCheckDO> _repository;

        public ProjectCheckRepository(IRepository<ProjectCheckDO> repository)
        {
            _repository = repository;
        }

        public IRepository<ProjectCheckDO> GetRepository()
        {
            return _repository;
        }
    }
}
