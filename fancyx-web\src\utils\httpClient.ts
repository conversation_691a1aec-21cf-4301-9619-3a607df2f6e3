import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios';
import UserStore from '@/store/userStore';
import { ErrorCode, StaticRoutes } from '@/utils/globalValue.ts';
import dayjs from 'dayjs';
import { refreshToken } from '@/api/auth.ts';

// 全局消息处理函数，避免直接使用 antd message 静态方法
let globalMessageError: ((content: string, duration?: number, onClose?: () => void) => void) | null = null;

export const setGlobalMessage = (messageError: (content: string, duration?: number, onClose?: () => void) => void) => {
  globalMessageError = messageError;
};

const showError = (msg: string, duration?: number, onClose?: () => void) => {
  if (globalMessageError) {
    globalMessageError(msg, duration, onClose);
  } else {
    console.error('HTTP Error:', msg);
  }
};

class HttpClient {
  private readonly instance: AxiosInstance;
  allowAnonymousApis: string[] = ['/api/account/login']; //允许匿名访问接口
  refreshTokenWhiteApis: string[] = ['/api/account/refreshtoken', '/api/account/signout']; //不需要刷新token接口

  constructor(config?: AxiosRequestConfig) {
    this.instance = axios.create(config);

    // 请求拦截器
    this.instance.interceptors.request.use(
      async (config) => {
        // 匿名接口直接放行
        if (config.url && this.allowAnonymousApis.includes(config.url)) {
          return config;
        }
        
        // 获取token信息
        const token = UserStore.token?.accessToken;
        const expired = UserStore.token?.expiredTime;
        const now = new Date();
        
        // 检查token是否存在
        if (!token) {
          console.error('请求需要token但token不存在:', config.url);
          return Promise.reject(new Error('未登录或登录已过期'));
        }
        
        // 检查token是否过期
        if (!expired || !dayjs(expired).isAfter(now)) {
          console.error('Token已过期:', config.url, 'expired:', expired, 'now:', now);
          return Promise.reject(new Error('登录已过期'));
        }
        
        // 添加Authorization头
        config.headers.Authorization = `Bearer ${token}`;
        
        // 过期时间小于10分钟进行刷新token
        if (dayjs(expired).subtract(10, 'minute').isBefore(now)) {
          const refreshTokenValue = UserStore.token?.refreshToken;
          if (
            !this.refreshTokenWhiteApis.some(
              (x) => config.url !== null && config.url!.toLowerCase().indexOf(x) >= 0,
            ) &&
            refreshTokenValue
          ) {
            try {
              const refreshTokenRes = await refreshToken(refreshTokenValue);
              if (refreshTokenRes.data) {
                const refreshTokenData = refreshTokenRes.data;
                UserStore.refreshToken(
                  refreshTokenData.accessToken,
                  refreshTokenData.refreshToken,
                  refreshTokenData.expiredTime,
                );
                // 更新请求头中的token
                config.headers.Authorization = `Bearer ${refreshTokenData.accessToken}`;
              }
            } catch (refreshError) {
              console.error('刷新token失败:', refreshError);
            }
          }
        }
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      },
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        /** 统一返回结果响应码不等于成功，中断请求；这样做是确保.then()中是响应成功 */
        if (response.data.code && response.data.code !== ErrorCode.Success) {
          const errMsg = response.data.message ?? '请求失败';
          showError(errMsg);
          return Promise.reject(errMsg);
        }
        return response.data;
      },
      (error) => {
        let msg = '异常错误，请联系管理员';
        let jumpLogin = false;
        
        if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
          // 请求超时，静默处理
          return Promise.reject(error);
        }
        
        if (error.code === 'ERR_NETWORK') {
          msg = '网络错误，请联系管理员';
        } else if (error.response) {
          switch (error.response.status) {
            case 401:
              msg = '身份信息过期，请重新登录';
              UserStore.logout();
              jumpLogin = true;
              break;
            case 404:
              msg = '请求接口不存在';
              break;
            case 405:
              msg = '请求方法错误';
              break;
          }
        }
        showError(msg, 3, () => {
          if (jumpLogin) {
            // 使用路由跳转而不是强制刷新页面
            if (window.location.pathname !== StaticRoutes.Login) {
              window.location.href = StaticRoutes.Login;
            }
          }
        });
        return Promise.reject(error);
      },
    );
  }

  // GET请求
  public get<TRequest = any, TResponse = any>(url: string, config?: AxiosRequestConfig): Promise<TResponse> {
    return this.instance.get<TRequest, TResponse>(url, config);
  }

  // POST请求
  public post<TRequest = any, TResponse = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<TResponse> {
    return this.instance.post<TRequest, TResponse>(url, data, config);
  }

  // PUT请求
  public put<TRequest = any, TResponse = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<TResponse> {
    return this.instance.put<TRequest, TResponse>(url, data, config);
  }

  // DELETE请求
  public delete<TRequest = any, TResponse = any>(url: string, config?: AxiosRequestConfig): Promise<TResponse> {
    return this.instance.delete<TRequest, TResponse>(url, config);
  }

  // PATCH请求
  public patch<TRequest = any, TResponse = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<TResponse> {
    return this.instance.patch<TRequest, TResponse>(url, data, config);
  }

  // 获取原始Axios实例
  public getInstance(): AxiosInstance {
    return this.instance;
  }
}

// 默认配置
const defaultConfig: AxiosRequestConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
};

// 创建默认实例
const httpClient = new HttpClient(defaultConfig);

export default httpClient;
