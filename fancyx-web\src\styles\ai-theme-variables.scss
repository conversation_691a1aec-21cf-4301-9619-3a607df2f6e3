// 关键CSS变量 - 内联到HTML头部以提升首屏渲染速度
:root {
  --ai-primary: #7E57C2;
  --ai-secondary: #9575CD;
  --ai-accent: #673AB7;
  --ai-bg-start: #1a1a2e;
  --ai-bg-middle: #16213e;
  --ai-bg-end: #0f3460;
  --ai-text-primary: #ffffff;
  --ai-text-dark: #4A4A4A;
  --ai-transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --ai-border-radius-md: 8px;
  --ai-spacing-md: 16px;
  --ai-spacing-lg: 24px;
  --ai-font-md: 16px;
  --ai-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
}

// 关键登录页面样式 - 优先加载
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, var(--ai-bg-start) 0%, var(--ai-bg-middle) 50%, var(--ai-bg-end) 100%);
  height: 100vh;
  box-sizing: border-box;
}

.login-card {
  width: 900px;
  border-radius: var(--ai-border-radius-md);
  overflow: hidden;
  box-shadow: var(--ai-shadow-lg);
}

.login-layout {
  display: flex;
  height: 500px;
}

.login-bg-side {
  width: 50%;
  background: linear-gradient(135deg, var(--ai-bg-start) 0%, var(--ai-bg-middle) 50%, var(--ai-bg-end) 100%);
}

.login-form-side {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.98);
}