using Fancyx.Shared.Consts;
using System.Text.Json.Serialization;

namespace Fancyx.Shared.Models
{
    public class AppResponse<T>
    {
        [JsonPropertyOrder(1)]
        public string Code { get; set; } = null!;

        [JsonPropertyOrder(2)]
        public string? Message { get; set; }

        [JsonPropertyOrder(3)]
        public T? Data { get; set; }

        public AppResponse()
        { }

        public AppResponse(string code, string? msg)
        {
            Code = code;
            Message = msg;
        }

        public AppResponse(T? data)
        {
            Code = ErrorCode.Success;
            Data = data;
        }

        public bool EnsureSuccess()
        {
            return Code == ErrorCode.Success;
        }

        public AppResponse<T> SetData(T? data)
        {
            Data = data;
            return this;
        }

        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="message">消息</param>
        /// <returns>成功响应</returns>
        public static AppResponse<T> Success(T? data, string? message = null)
        {
            return new AppResponse<T>
            {
                Code = ErrorCode.Success,
                Message = message,
                Data = data
            };
        }

        /// <summary>
        /// 创建错误响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="code">错误代码</param>
        /// <returns>错误响应</returns>
        public static AppResponse<T> Error(string? message = null, string? code = null)
        {
            return new AppResponse<T>
            {
                Code = code ?? ErrorCode.Fail,
                Message = message,
                Data = default(T)
            };
        }
    }

    public static class Result
    {
        public static AppResponse<bool> Ok(string? msg = default)
        {
            return new AppResponse<bool>
            {
                Code = ErrorCode.Success,
                Message = msg,
                Data = true
            };
        }

        public static AppResponse<bool> Fail(string? msg = default)
        {
            return new AppResponse<bool>
            {
                Code = ErrorCode.Fail,
                Message = msg,
                Data = false
            };
        }

        public static AppResponse<T> Fail<T>(string? msg = default)
        {
            return new AppResponse<T>
            {
                Code = ErrorCode.Fail,
                Message = msg,
                Data = default(T)
            };
        }

        public static AppResponse<T> Data<T>(T? data)
        {
            return new AppResponse<T>(data);
        }
    }
}