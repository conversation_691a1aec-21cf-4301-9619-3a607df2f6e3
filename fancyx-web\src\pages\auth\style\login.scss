// 使用现代化的 @use 语法替代 @import
@use '../../../styles/ai-theme.scss' as *;
@use '../../../styles/ai-animations.scss' as *;

// 性能优化：使用will-change属性提示浏览器优化
.ai-particle {
  will-change: transform, opacity;
  // 使用transform3d启用硬件加速
  transform: translate3d(0, 0, 0);
}

.ai-login-button {
  will-change: transform, box-shadow;
}

// 预加载关键字体
@font-face {
  font-family: 'AI-System';
  font-display: swap; // 字体交换策略，提升首屏渲染
}

.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: $ai-gradient-primary;
  height: 100dvh;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  will-change: transform, opacity;
  
  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: $ai-gradient-primary;
    animation: ai-gradient-shift 8s ease infinite;
    z-index: 0;
  }

  // 页面加载动画
  &.ai-page-loaded {
    .login-container-main {
      animation: ai-fade-in 0.8s ease-out;
    }

    .login-bg-side {
      .ai-brand-content {
        animation: ai-slide-in-left 1s ease-out 0.3s both;
      }
    }

    .login-form-side {
      .form-container {
        animation: ai-slide-in-right 1s ease-out 0.5s both;
      }
    }
  }

  // 登录成功过渡动画
  &.ai-login-success {
    .login-card {
      animation: ai-scale-in 0.5s ease-out;
      transform: scale(1.05);
      opacity: 0.8;
    }
  }

  .login-container-main {
    padding: $ai-spacing-lg;
    position: relative;
    z-index: 1;

    .login-card {
      width: 900px;
      border-radius: $ai-border-radius-xl;
      overflow: hidden;
      box-shadow: $ai-shadow-xl;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);

      .ant-card-body {
        padding: 0;
        height: 500px;
        background: rgba(255, 255, 255, 0.95);
      }
    }

    .login-layout {
      display: flex;
      height: 100%;
    }

    .login-bg-side {
      width: 50%;
      background: $ai-gradient-primary;
      position: relative;
      overflow: hidden;

      // 粒子动画容器
      .ai-particles-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
        z-index: 1;

        .ai-particle {
          position: absolute;
          width: $ai-particle-size-min;
          height: $ai-particle-size-min;
          background: $ai-neon-blue;
          border-radius: 50%;
          animation: ai-particle-float linear infinite;
          box-shadow: 0 0 6px $ai-neon-blue;

          &:nth-child(3n) {
            background: $ai-electric-purple;
            box-shadow: 0 0 6px $ai-electric-purple;
            width: $ai-particle-size-max;
            height: $ai-particle-size-max;
          }

          &:nth-child(5n) {
            background: $ai-glow-pink;
            box-shadow: 0 0 6px $ai-glow-pink;
            width: 2px;
            height: 2px;
          }
        }
      }

      .bg-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: $ai-gradient-overlay;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: $ai-spacing-xxl;
        color: $ai-text-primary;
        z-index: 2;
      }

      // 添加更多装饰元素
      &::before {
        content: '';
        position: absolute;
        top: 20%;
        left: 10%;
        width: 100px;
        height: 100px;
        border: 2px solid rgba(0, 245, 255, 0.3);
        border-radius: 50%;
        animation: ai-rotate 20s linear infinite;
        z-index: 1;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 20%;
        right: 15%;
        width: 60px;
        height: 60px;
        border: 1px solid rgba(124, 77, 255, 0.4);
        transform: rotate(45deg);
        animation: ai-rotate 15s linear infinite reverse;
        z-index: 1;
      }
    }

    // AI品牌内容样式
    .ai-brand-content {
      text-align: center;
      animation: ai-fade-in 1s ease-out;

      .ai-system-logo {
        margin-bottom: $ai-spacing-lg;

        .ai-logo-icon {
          font-size: 48px;
          margin-bottom: $ai-spacing-md;
          animation: ai-glow-pulse 3s ease-in-out infinite;
          filter: drop-shadow(0 0 10px rgba(0, 245, 255, 0.5));
        }
      }

      .ai-system-title {
        font-size: $ai-font-title;
        font-weight: 700;
        margin-bottom: $ai-spacing-sm;
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        animation: ai-slide-in-left 0.8s ease-out 0.2s both;
      }

      .ai-system-subtitle {
        font-size: $ai-font-lg;
        color: $ai-text-secondary;
        margin-bottom: $ai-spacing-md;
        font-style: italic;
        animation: ai-slide-in-left 0.8s ease-out 0.4s both;
      }

      .ai-system-description {
        font-size: $ai-font-md;
        color: $ai-text-secondary;
        margin-bottom: $ai-spacing-xl;
        line-height: 1.6;
        animation: ai-slide-in-left 0.8s ease-out 0.6s both;
      }

      .ai-features {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: $ai-spacing-md;
        margin-top: $ai-spacing-lg;

        .ai-feature-item {
          display: flex;
          align-items: center;
          gap: $ai-spacing-sm;
          font-size: $ai-font-sm;
          color: $ai-text-secondary;
          animation: ai-scale-in 0.5s ease-out calc(0.8s + var(--delay, 0s)) both;

          &:nth-child(1) { --delay: 0.1s; }
          &:nth-child(2) { --delay: 0.2s; }
          &:nth-child(3) { --delay: 0.3s; }
          &:nth-child(4) { --delay: 0.4s; }

          .ai-feature-icon {
            font-size: $ai-font-md;
            filter: drop-shadow(0 0 5px rgba(0, 245, 255, 0.3));
          }
        }
      }
    }

    .login-form-side {
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(10px);

      .form-container {
        width: 80%;
        max-width: 360px;
        animation: ai-slide-in-right 0.8s ease-out;

        .ai-form-title {
          text-align: center;
          margin-bottom: $ai-spacing-xl;
          color: $ai-text-dark;
          font-size: $ai-font-xl;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: $ai-spacing-sm;

          .ai-title-icon {
            font-size: $ai-font-lg;
            color: $ai-primary;
            filter: drop-shadow(0 0 5px rgba(126, 87, 194, 0.3));
          }
        }

        .ai-login-form {
          .ant-form-item {
            margin-bottom: $ai-spacing-lg;
          }

          .ai-input {
            &.ant-input-affix-wrapper {
              padding: $ai-spacing-md $ai-spacing-md;
              border-radius: $ai-border-radius-md;
              border: 2px solid #e8e8e8;
              background: rgba(255, 255, 255, 0.9);
              transition: all $ai-transition-normal;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

              &:hover {
                border-color: $ai-secondary;
                box-shadow: 0 4px 12px rgba(126, 87, 194, 0.1);
              }

              &:focus-within {
                border-color: $ai-primary;
                box-shadow: $ai-glow-primary, 0 4px 16px rgba(126, 87, 194, 0.15);
                transform: translateY(-1px);
              }

              .ant-input {
                background: transparent;
                border: none;
                font-size: $ai-font-md;
                color: $ai-text-dark;

                &::placeholder {
                  color: rgba(74, 74, 74, 0.5);
                }
              }

              .ai-input-icon {
                color: $ai-primary;
                font-size: $ai-font-md;
                transition: color $ai-transition-normal;
              }

              &:focus-within .ai-input-icon {
                color: $ai-accent;
                filter: drop-shadow(0 0 3px rgba(126, 87, 194, 0.4));
              }
            }
          }

          .ant-checkbox-wrapper {
            color: $ai-text-dark;
            font-size: $ai-font-sm;

            .ant-checkbox {
              .ant-checkbox-inner {
                border-color: $ai-primary;
                border-radius: $ai-border-radius-sm;
              }

              &.ant-checkbox-checked .ant-checkbox-inner {
                background-color: $ai-primary;
                border-color: $ai-primary;
              }
            }

            &:hover .ant-checkbox .ant-checkbox-inner {
              border-color: $ai-secondary;
            }
          }

          .ai-login-button {
            &.ant-btn-primary {
              height: 48px;
              font-size: $ai-font-md;
              font-weight: 600;
              border-radius: $ai-border-radius-md;
              background: $ai-gradient-button;
              border: none;
              box-shadow: 0 4px 16px rgba(126, 87, 194, 0.3);
              transition: all $ai-transition-normal;
              position: relative;
              overflow: hidden;

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
              }

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 24px rgba(126, 87, 194, 0.4);

                &::before {
                  left: 100%;
                }
              }

              &:active {
                transform: translateY(0);
                box-shadow: 0 4px 16px rgba(126, 87, 194, 0.3);
              }

              &.ant-btn-loading {
                background: $ai-secondary;
                
                .ant-btn-loading-icon {
                  color: white;
                }
              }
            }
          }
        }
      }
    }

    // 平板设备适配
    @media (max-width: 1024px) {
      .login-card {
        width: 95%;
        max-width: 800px;
      }

      .login-bg-side {
        .ai-brand-content {
          padding: $ai-spacing-lg;

          .ai-system-title {
            font-size: $ai-font-xl;
          }

          .ai-features {
            grid-template-columns: 1fr;
            gap: $ai-spacing-sm;
          }
        }
      }
    }

    // 移动设备适配
    @media (max-width: 768px) {
      padding: $ai-spacing-md;

      .login-card {
        width: 100%;
        height: auto;
        border-radius: $ai-border-radius-lg;

        .ant-card-body {
          height: auto;
        }
      }

      .login-layout {
        flex-direction: column;
      }

      .login-bg-side {
        width: 100%;
        height: 200px;
        min-height: 200px;

        .ai-particles-container {
          .ai-particle {
            display: none; // 移动端隐藏粒子动画以提升性能
          }
        }

        .ai-brand-content {
          padding: $ai-spacing-lg $ai-spacing-md;

          .ai-system-logo .ai-logo-icon {
            font-size: 32px;
          }

          .ai-system-title {
            font-size: $ai-font-lg;
            margin-bottom: $ai-spacing-xs;
          }

          .ai-system-subtitle {
            font-size: $ai-font-sm;
            margin-bottom: $ai-spacing-sm;
          }

          .ai-system-description {
            font-size: $ai-font-sm;
            margin-bottom: $ai-spacing-md;
          }

          .ai-features {
            display: none; // 移动端隐藏功能列表
          }
        }
      }

      .login-form-side {
        width: 100%;
        padding: $ai-spacing-xl $ai-spacing-md;
        min-height: 300px;

        .form-container {
          width: 100%;
          max-width: 100%;

          .ai-form-title {
            font-size: $ai-font-lg;
            margin-bottom: $ai-spacing-lg;
          }

          .ai-login-form {
            .ai-input.ant-input-affix-wrapper {
              padding: $ai-spacing-md;
              font-size: $ai-font-md;
            }

            .ai-login-button.ant-btn-primary {
              height: 44px;
              font-size: $ai-font-md;
            }
          }
        }
      }
    }

    // 小屏手机适配
    @media (max-width: 480px) {
      padding: $ai-spacing-sm;

      .login-card {
        border-radius: $ai-border-radius-md;
      }

      .login-bg-side {
        height: 160px;

        .ai-brand-content {
          padding: $ai-spacing-md;

          .ai-system-title {
            font-size: $ai-font-md;
          }

          .ai-system-subtitle {
            font-size: $ai-font-xs;
          }

          .ai-system-description {
            display: none;
          }
        }
      }

      .login-form-side {
        padding: $ai-spacing-lg $ai-spacing-md;

        .form-container {
          .ai-form-title {
            font-size: $ai-font-md;
          }

          .ai-login-form {
            .ant-form-item {
              margin-bottom: $ai-spacing-md;
            }
          }
        }
      }
    }

    .login-footer {
      text-align: center;
      position: relative;
      top: 60px;

      &.ai-footer {
        .ai-footer-content {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: $ai-spacing-sm;
          margin-bottom: $ai-spacing-sm;
          color: $ai-text-secondary;
          font-size: $ai-font-sm;

          .ai-footer-separator {
            color: $ai-text-muted;
          }
        }

        .ai-footer-links {
          display: flex;
          justify-content: center;
          gap: $ai-spacing-md;

          .ai-footer-link {
            color: $ai-primary;
            text-decoration: none;
            font-size: $ai-font-sm;
            transition: color $ai-transition-normal;

            &:hover {
              color: $ai-secondary;
            }
          }
        }
      }
    }
  }

  // 加载状态动画
  .ai-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);

    .ai-loading-content {
      text-align: center;
      color: $ai-primary;

      .ai-loading-icon {
        font-size: 32px;
        margin-bottom: $ai-spacing-md;
        animation: ai-rotate 1s linear infinite;
      }

      .ai-loading-text {
        font-size: $ai-font-md;
        font-weight: 500;
      }
    }
  }
}

// 科技网格背景
.ai-tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
  opacity: 0.3;
}

// 性能优化相关样式

// 减少重绘和回流
.ai-brand-content * {
  backface-visibility: hidden;
  perspective: 1000px;
}

// 优化动画性能
.ai-particle {
  contain: layout style paint;
  pointer-events: none;
}

// 图片懒加载占位符
.ai-image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: ai-loading-shimmer 1.5s infinite;
}

@keyframes ai-loading-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

// 错误处理样式
.ai-error-shake {
  animation: ai-error-shake 0.6s ease-in-out;
}

@keyframes ai-error-shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

// 表单验证错误样式增强
.ant-form-item-has-error {
  .ai-input.ant-input-affix-wrapper {
    border-color: #FF7043 !important;
    box-shadow: 0 0 0 2px rgba(255, 112, 67, 0.2) !important;
    animation: ai-error-pulse 0.3s ease-out;

    .ai-input-icon {
      color: #FF7043 !important;
    }
  }
}

@keyframes ai-error-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

// 低性能设备优化
@media (prefers-reduced-motion: reduce) {
  .ai-particle,
  .ai-animate-glow-pulse,
  .ai-animate-neon-flicker,
  .ai-animate-gradient-shift,
  .ai-animate-rotate {
    animation: none !important;
  }

  .ai-hover-glow,
  .ai-hover-scale,
  .ai-hover-lift {
    transition: none !important;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .login-bg-side {
    background: #000000;
  }

  .ai-system-title,
  .ai-system-subtitle,
  .ai-system-description {
    color: #ffffff;
    text-shadow: none;
  }

  .ai-input.ant-input-affix-wrapper {
    border: 2px solid #000000;
    background: #ffffff;
  }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
  .login-form-side {
    background: rgba(30, 30, 30, 0.95);
  }

  .ai-form-title {
    color: #ffffff;
  }

  .ai-input.ant-input-affix-wrapper {
    background: rgba(50, 50, 50, 0.8);
    border-color: #555555;
    color: #ffffff;

    .ant-input {
      color: #ffffff;
      background: transparent;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}