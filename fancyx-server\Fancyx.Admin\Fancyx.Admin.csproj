﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <UserSecretsId>01209eb2-a139-4e3a-b3fc-5c56b5a81471</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Fancyx.Cap\Fancyx.Cap.csproj" />
    <ProjectReference Include="..\Fancyx.Core\Fancyx.Core.csproj" />
    <ProjectReference Include="..\Fancyx.Logger\Fancyx.Logger.csproj" />
    <ProjectReference Include="..\Fancyx.ObjectStorage\Fancyx.ObjectStorage.csproj" />
    <ProjectReference Include="..\Fancyx.Redis\Fancyx.Redis.csproj" />
    <ProjectReference Include="..\Fancyx.Repository\Fancyx.Repository.csproj" />
    <ProjectReference Include="..\Fancyx.Shared\Fancyx.Shared.csproj" />
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="Coravel" Version="5.0.4" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
		<PackageReference Include="MQTTnet" Version="5.0.1.1416" />
		<PackageReference Include="MQTTnet.Server" Version="5.0.1.1416" />
		<PackageReference Include="MQTTnet.AspNetCore" Version="5.0.1.1416" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="wwwroot\" />
	</ItemGroup>

</Project>
