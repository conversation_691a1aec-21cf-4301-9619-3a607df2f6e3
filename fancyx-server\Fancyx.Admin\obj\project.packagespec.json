﻿"restore":{"projectUniqueName":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\Fancyx.Admin.csproj","projectName":"Fancyx.Admin","projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\Fancyx.Admin.csproj","outputPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj"},"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"},"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Logger\\Fancyx.Logger.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Logger\\Fancyx.Logger.csproj"},"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\Fancyx.ObjectStorage.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\Fancyx.ObjectStorage.csproj"},"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Redis\\Fancyx.Redis.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Redis\\Fancyx.Redis.csproj"},"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj"},"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Shared\\Fancyx.Shared.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Shared\\Fancyx.Shared.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Coravel":{"target":"Package","version":"[5.0.4, )"},"MQTTnet":{"target":"Package","version":"[5.0.1.1416, )"},"MQTTnet.AspNetCore":{"target":"Package","version":"[5.0.1.1416, )"},"MQTTnet.Server":{"target":"Package","version":"[5.0.1.1416, )"},"Swashbuckle.AspNetCore":{"target":"Package","version":"[8.1.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}