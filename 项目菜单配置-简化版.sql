-- 项目菜单配置SQL脚本（PostgreSQL版本）
-- 根据你提供的表结构定制

-- 注意：请将 "your_menu_table_name" 替换为你的实际菜单表名
-- 表结构字段：id, creator_id, creation_time, title, icon, path, component, menu_type, permission, parent_id, sort, display, tenant_id

-- 生成UUID的函数（PostgreSQL）
-- 1. 先插入项目管理文件夹
DO $$
DECLARE
    project_folder_id UUID := gen_random_uuid();
    current_user_id UUID := 'your_current_user_id'::UUID; -- 请替换为当前用户ID
    current_tenant_id VARCHAR(18) := 'your_tenant_id'; -- 请替换为租户ID
BEGIN
    -- 插入项目管理文件夹
    INSERT INTO "your_menu_table_name" (
        id, creator_id, creation_time, last_modification_time, last_modifier_id,
        title, icon, path, component, menu_type, permission, 
        parent_id, sort, display, tenant_id
    ) VALUES (
        project_folder_id,
        current_user_id,
        NOW(),
        NULL,
        NULL,
        '项目管理',
        'project',
        '/project',
        NULL,
        1, -- 文件夹类型
        '', -- permission字段不能为NULL，设为空字符串
        NULL,
        10,
        true,
        current_tenant_id
    );

    -- 插入项目列表菜单
    INSERT INTO "your_menu_table_name" (
        id, creator_id, creation_time, last_modification_time, last_modifier_id,
        title, icon, path, component, menu_type, permission, 
        parent_id, sort, display, tenant_id
    ) VALUES (
        gen_random_uuid(),
        current_user_id,
        NOW(),
        NULL,
        NULL,
        '项目列表',
        'list',
        '/project/list',
        'pages/project/list',
        2, -- 菜单类型
        'Project.List',
        project_folder_id,
        1,
        true,
        current_tenant_id
    );

    -- 插入项目编辑菜单（重要！解决404问题）
    INSERT INTO "your_menu_table_name" (
        id, creator_id, creation_time, last_modification_time, last_modifier_id,
        title, icon, path, component, menu_type, permission, 
        parent_id, sort, display, tenant_id
    ) VALUES (
        gen_random_uuid(),
        current_user_id,
        NOW(),
        NULL,
        NULL,
        '项目编辑',
        'edit',
        '/project/edit',
        'pages/project/edit',
        2, -- 菜单类型
        'Project.Edit',
        project_folder_id,
        2,
        false, -- 不在侧边栏显示
        current_tenant_id
    );

    -- 插入项目详情菜单
    INSERT INTO "your_menu_table_name" (
        id, creator_id, creation_time, last_modification_time, last_modifier_id,
        title, icon, path, component, menu_type, permission, 
        parent_id, sort, display, tenant_id
    ) VALUES (
        gen_random_uuid(),
        current_user_id,
        NOW(),
        NULL,
        NULL,
        '项目详情',
        'detail',
        '/project/detail/:id',
        'pages/project/detail',
        2, -- 菜单类型
        'Project.Detail',
        project_folder_id,
        3,
        false, -- 不在侧边栏显示
        current_tenant_id
    );

    RAISE NOTICE '项目菜单配置添加完成！项目管理文件夹ID: %', project_folder_id;
END $$;

-- 执行完成后，记得：
-- 1. 在角色管理中给相应角色分配这些菜单权限
-- 2. 用户重新登录以获取最新的菜单数据
-- 3. 检查前端是否能正确访问 /project/edit 路由

-- 查询验证SQL（可选执行）
-- SELECT id, title, path, menu_type, display, parent_id 
-- FROM "your_menu_table_name" 
-- WHERE title LIKE '%项目%' 
-- ORDER BY sort;
