import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from '@/store';
import LoginPage from '../login';
import { AuthProvider } from '@/components/AuthProvider';

// Mock AuthProvider
jest.mock('@/components/AuthProvider', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuthProvider: () => ({
    pwdLogin: jest.fn(),
  }),
}));

const renderLoginPage = () => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('LoginPage Accessibility', () => {
  test('has proper ARIA labels and roles', () => {
    renderLoginPage();
    
    // 检查表单元素的可访问性
    const usernameInput = screen.getByPlaceholderText('请输入用户账号');
    const passwordInput = screen.getByPlaceholderText('请输入登录密码');
    const submitButton = screen.getByRole('button', { name: '立即登录' });
    
    expect(usernameInput).toBeInTheDocument();
    expect(passwordInput).toBeInTheDocument();
    expect(submitButton).toBeInTheDocument();
  });

  test('supports keyboard navigation', () => {
    renderLoginPage();
    
    const usernameInput = screen.getByPlaceholderText('请输入用户账号');
    const passwordInput = screen.getByPlaceholderText('请输入登录密码');
    const submitButton = screen.getByRole('button', { name: '立即登录' });
    
    // 验证元素可以通过Tab键导航
    expect(usernameInput).toHaveAttribute('type', 'text');
    expect(passwordInput).toHaveAttribute('type', 'password');
    expect(submitButton).toHaveAttribute('type', 'submit');
  });

  test('has proper heading structure', () => {
    renderLoginPage();
    
    // 检查标题层级结构
    const mainTitle = screen.getByText('工程现场安全隐患识别与报告生成系统');
    const formTitle = screen.getByText('系统登录');
    
    expect(mainTitle).toBeInTheDocument();
    expect(formTitle).toBeInTheDocument();
  });

  test('provides alternative text for decorative elements', () => {
    renderLoginPage();
    
    // 检查装饰性元素不会干扰屏幕阅读器
    const particles = document.querySelectorAll('.ai-particle');
    particles.forEach(particle => {
      expect(particle).toHaveStyle('pointer-events: none');
    });
  });

  test('maintains focus management', () => {
    renderLoginPage();
    
    const usernameInput = screen.getByPlaceholderText('请输入用户账号');
    
    // 验证焦点管理
    usernameInput.focus();
    expect(document.activeElement).toBe(usernameInput);
  });
});