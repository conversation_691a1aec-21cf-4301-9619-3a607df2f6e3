using System;
using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 创建项目 DTO
    /// </summary>
    public class CreateProjectDto
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        [Required(ErrorMessage = "项目名称不能为空")]
        [MaxLength(200, ErrorMessage = "项目名称不能超过200个字符")]
        public string ProjectName { get; set; }

        /// <summary>
        /// 项目地址
        /// </summary>
        [Required(ErrorMessage = "项目地址不能为空")]
        [MaxLength(500, ErrorMessage = "项目地址不能超过500个字符")]
        public string ProjectAddress { get; set; }

        /// <summary>
        /// 所属区域
        /// </summary>
        [Required(ErrorMessage = "所属区域不能为空")]
        [MaxLength(50, ErrorMessage = "所属区域不能超过50个字符")]
        public string Region { get; set; }

        /// <summary>
        /// 项目类型（生产安全、质量安全）
        /// </summary>
        [Required(ErrorMessage = "项目类型不能为空")]
        [MaxLength(20, ErrorMessage = "项目类型不能超过20个字符")]
        public string ProjectType { get; set; }

        /// <summary>
        /// 关联合同
        /// </summary>
        [MaxLength(100, ErrorMessage = "关联合同不能超过100个字符")]
        public string RelatedContract { get; set; }

        /// <summary>
        /// 现场负责人
        /// </summary>
        [Required(ErrorMessage = "现场负责人不能为空")]
        [MaxLength(50, ErrorMessage = "现场负责人不能超过50个字符")]
        public string SiteManager { get; set; }

        /// <summary>
        /// 负责人电话
        /// </summary>
        [Required(ErrorMessage = "负责人电话不能为空")]
        [MaxLength(20, ErrorMessage = "负责人电话不能超过20个字符")]
        [RegularExpression(@"^1[3-9]\d{9}$|^(\d{3,4}-)?\d{7,8}$", ErrorMessage = "请输入有效的电话号码")]
        public string ManagerPhone { get; set; }

        /// <summary>
        /// 负责人职务
        /// </summary>
        [MaxLength(50, ErrorMessage = "负责人职务不能超过50个字符")]
        public string ManagerPosition { get; set; }

        /// <summary>
        /// 开工日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 竣工日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 工程类型
        /// </summary>
        [Required(ErrorMessage = "工程类型不能为空")]
        [MaxLength(20, ErrorMessage = "工程类型不能超过20个字符")]
        public string EngineeringType { get; set; }

        /// <summary>
        /// 工程类别
        /// </summary>
        [Required(ErrorMessage = "工程类别不能为空")]
        [MaxLength(20, ErrorMessage = "工程类别不能超过20个字符")]
        public string EngineeringCategory { get; set; }

        /// <summary>
        /// 建设单位
        /// </summary>
        [Required(ErrorMessage = "建设单位不能为空")]
        [MaxLength(200, ErrorMessage = "建设单位不能超过200个字符")]
        public string ConstructionUnit { get; set; }

        /// <summary>
        /// 监理单位
        /// </summary>
        [Required(ErrorMessage = "监理单位不能为空")]
        [MaxLength(200, ErrorMessage = "监理单位不能超过200个字符")]
        public string SupervisionUnit { get; set; }

        /// <summary>
        /// 设计单位
        /// </summary>
        [Required(ErrorMessage = "设计单位不能为空")]
        [MaxLength(200, ErrorMessage = "设计单位不能超过200个字符")]
        public string DesignUnit { get; set; }

        /// <summary>
        /// 施工单位
        /// </summary>
        [Required(ErrorMessage = "施工单位不能为空")]
        [MaxLength(200, ErrorMessage = "施工单位不能超过200个字符")]
        public string ConstructionCompany { get; set; }

        /// <summary>
        /// 施工许可证号
        /// </summary>
        [MaxLength(100, ErrorMessage = "施工许可证号不能超过100个字符")]
        public string ConstructionPermitNo { get; set; }

        /// <summary>
        /// 危险化学品从业人数
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "危险化学品从业人数必须为非负整数")]
        public int? HazardousChemicalWorkers { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(1000, ErrorMessage = "备注不能超过1000个字符")]
        public string Remarks { get; set; }
    }
}