using Fancyx.Admin.IService.Project;
using Fancyx.Admin.IService.Project.Dtos;
using Fancyx.Shared.Models;
using Result = Fancyx.Shared.Models.Result;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Fancyx.Admin.Service.Project;
using Microsoft.AspNetCore.Authorization;

namespace Fancyx.Admin.Controllers.Project
{
    /// <summary>
    /// 项目管理控制器
    /// </summary>
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class ProjectController : ControllerBase
    {
        private readonly IProjectService _projectService;
        
        private readonly IProjectCheckService  _projectCheckService;

        public ProjectController(IProjectService projectService, IProjectCheckService  projectCheckService)
        {
            _projectService = projectService;
            _projectCheckService= projectCheckService;;
        }

        /// <summary>
        /// 获取项目列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>项目列表</returns>
        [HttpGet]
        [Route("list")]
        public async Task<AppResponse<PagedResult<ProjectListDto>>> GetProjectList([FromQuery] ProjectQueryDto queryDto)
        {
            var result = await _projectService.GetProjectListAsync(queryDto);
            return Result.Data(result);
        }

        /// <summary>
        /// 根据ID获取项目详情
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <returns>项目详情</returns>
        [HttpGet]
        [Route("{id}")]
        public async Task<AppResponse<ProjectDto?>> GetProjectById(long id)
        {
            var project = await _projectService.GetProjectByIdAsync(id);
            if (project == null)
            {
                return Result.Fail<ProjectDto?>("项目不存在");
            }
            return Result.Data(project);
        }

        /// <summary>
        /// 创建项目
        /// </summary>
        /// <param name="createDto">项目信息</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<AppResponse<long>> CreateProject([FromBody] CreateProjectDto createDto)
        {
            var projectId = await _projectService.CreateProjectAsync(createDto);
            return Result.Data(projectId);
        }

        /// <summary>
        /// 更新项目
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <param name="updateDto">更新信息</param>
        /// <returns>更新结果</returns>
        [HttpPut]
        [Route("{id}")]
        public async Task<AppResponse<bool>> UpdateProject(long id, [FromBody] UpdateProjectDto updateDto)
        {
            var result = await _projectService.UpdateProjectAsync(id, updateDto);
            return result switch
            {
                ProjectOperationResult.Success => Result.Ok(),
                ProjectOperationResult.ProjectNotFound => Result.Fail("项目不存在"),
                _ => Result.Fail("更新项目失败")
            };
        }

        /// <summary>
        /// 删除项目
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete]
        [Route("{id}")]
        public async Task<AppResponse<bool>> DeleteProject(long id)
        {
            var result = await _projectService.DeleteProjectAsync(id);
            return result switch
            {
                Fancyx.Admin.IService.Project.Dtos.ProjectOperationResult.Success => Result.Ok(),
                Fancyx.Admin.IService.Project.Dtos.ProjectOperationResult.ProjectNotFound => Result.Fail("项目不存在"),
                _ => Result.Fail("删除项目失败")
            };
        }

        /// <summary>
        /// 生成项目编号
        /// </summary>
        /// <returns>项目编号</returns>
        [HttpGet]
        [Route("generate-no")]
        public async Task<AppResponse<string>> GenerateProjectNo()
        {
            var projectNo = await _projectService.GenerateProjectNoAsync();
            return Result.Data(projectNo);
        }

        /// <summary>
        /// 根据项目生成检查任务
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <param name="checkType">检查类型</param>
        /// <param name="secondaryCategory">二级分类</param>
        /// <returns>检查任务ID</returns>
        [HttpPost]
        [Route("{projectId}/generate-check")]
        public async Task<AppResponse<long>> GenerateCheckTask(long projectId, [FromQuery] string checkType, [FromQuery] string secondaryCategory)
        {
            var checkId = await _projectCheckService.GenerateCheckTaskAsync(projectId, checkType, secondaryCategory);
            return Result.Data(checkId);
        }
    }
}