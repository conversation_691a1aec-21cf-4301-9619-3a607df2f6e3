using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// AI识别请求 DTO
    /// </summary>
    public class AIRecognitionRequestDto
    {
        /// <summary>
        /// 检查ID
        /// </summary>
        [Required(ErrorMessage = "检查ID不能为空")]
        public long ProjectCheckId { get; set; }

        /// <summary>
        /// 照片文件
        /// </summary>
        [Required(ErrorMessage = "照片不能为空")]
        public IFormFile Photo { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string Remarks { get; set; }
    }
}