using Fancyx.Repository.BaseEntity;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Fancyx.Core.Interfaces;

namespace Fancyx.Admin.Entities.Project
{
    [Table("sys_hidden_danger")]
    public class HiddenDangerDO : FullAuditedEntity, ITenant
    {
        /// <summary>
        /// 检查ID
        /// </summary>
        [Required]
        public long ProjectCheckId { get; set; }

        /// <summary>
        /// 隐患大类
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string DangerCategory { get; set; }

        /// <summary>
        /// 标准规范ID
        /// </summary>
        public long? StandardId { get; set; }

        /// <summary>
        /// 标准规范名称
        /// </summary>
        [MaxLength(200)]
        public string StandardName { get; set; }

        /// <summary>
        /// 问题等级（一般、较大、重大）
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string ProblemLevel { get; set; }

        /// <summary>
        /// 安全隐患
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string SafetyHazard { get; set; }

        /// <summary>
        /// 法规条款
        /// </summary>
        [MaxLength(500)]
        public string RegulationClause { get; set; }

        /// <summary>
        /// 具体描述
        /// </summary>
        [Required]
        [MaxLength(2000)]
        public string Description { get; set; }

        /// <summary>
        /// 整改要求
        /// </summary>
        [Required]
        [MaxLength(2000)]
        public string RectificationRequirement { get; set; }

        /// <summary>
        /// 可能产生后果
        /// </summary>
        [MaxLength(1000)]
        public string PossibleConsequences { get; set; }

        /// <summary>
        /// 整改建议
        /// </summary>
        [MaxLength(500)]
        public string RectificationSuggestion { get; set; }

        /// <summary>
        /// 是否需要复查
        /// </summary>
        [Required]
        public bool NeedReview { get; set; }

        /// <summary>
        /// 现场照片（JSON格式存储多个照片路径）
        /// </summary>
        [MaxLength(2000)]
        public string Photos { get; set; }

        /// <summary>
        /// 状态（0待确认，1正式记录）
        /// </summary>
        public int Status { get; set; } = 0;

        /// <summary>
        /// 租户ID
        /// </summary>
        public string? TenantId { get; set; }
    }
}