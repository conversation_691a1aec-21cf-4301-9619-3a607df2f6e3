using System;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 项目检查列表 DTO
    /// </summary>
    public class ProjectCheckListDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 检查类型
        /// </summary>
        public string CheckType { get; set; }

        /// <summary>
        /// 二级分类
        /// </summary>
        public string SecondaryCategory { get; set; }

        /// <summary>
        /// 检查日期
        /// </summary>
        public DateTime CheckDate { get; set; }

        /// <summary>
        /// 检查组长
        /// </summary>
        public string CheckLeader { get; set; }

        /// <summary>
        /// 委托单位
        /// </summary>
        public string ClientUnit { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatorName { get; set; }
    }
}