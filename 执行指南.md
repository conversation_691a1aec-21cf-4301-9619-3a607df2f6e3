# 菜单配置执行指南

## 🎯 问题解决方案总结

你遇到的两个问题：
1. ✅ **API Region字段错误** - 已通过修改前端代码解决
2. ⏳ **新增按钮404错误** - 需要执行下面的SQL脚本解决

## 📋 执行步骤

### 第一步：获取必要信息
在执行SQL前，你需要获取以下信息：

1. **菜单表名** - 查看你的数据库，找到实际的菜单表名
2. **用户ID** - 你当前登录用户的UUID
3. **租户ID** - 你的租户标识

### 第二步：修改SQL脚本
打开 `项目菜单配置-最终版.sql` 文件，替换以下三个参数：

```sql
-- 替换这三个参数
your_menu_table_name  -> 实际的菜单表名（如：menu 或 sys_menu）
your_current_user_id  -> 你的用户UUID
your_tenant_id        -> 你的租户ID
```

### 第三步：执行SQL脚本
在你的PostgreSQL数据库中执行修改后的SQL脚本。

### 第四步：分配权限
1. 登录系统管理后台
2. 进入角色管理
3. 给相应角色分配新添加的菜单权限：
   - Project.List
   - Project.Edit
   - Project.Detail

### 第五步：验证结果
1. 用户重新登录系统
2. 测试点击"新增项目"按钮
3. 检查是否还会出现404错误

## 🔍 如何获取必要信息

### 获取菜单表名
```sql
-- 查看所有表名，找到菜单相关的表
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%menu%';
```

### 获取用户ID
```sql
-- 查看用户表，找到你的用户ID
SELECT id, user_name 
FROM your_user_table_name 
WHERE user_name = '你的用户名';
```

### 获取租户ID
```sql
-- 查看现有菜单记录的租户ID
SELECT DISTINCT tenant_id 
FROM your_menu_table_name 
LIMIT 5;
```

## ⚠️ 注意事项

1. **备份数据库** - 执行前建议备份相关表
2. **权限检查** - 确保有数据库写入权限
3. **测试环境** - 建议先在测试环境执行
4. **重新登录** - 执行完成后用户需要重新登录

## 🚀 预期结果

执行成功后：
- ✅ 新增按钮不再出现404错误
- ✅ 可以正常访问 `/project/edit` 页面
- ✅ 项目编辑功能正常工作
- ✅ API Region字段错误已解决

## 📞 如果遇到问题

如果执行过程中遇到问题，请提供：
1. 具体的错误信息
2. 你的菜单表结构
3. 执行的SQL语句

这样我可以帮你进一步调试和解决问题。