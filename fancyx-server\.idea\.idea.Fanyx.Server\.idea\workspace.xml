<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="Fancyx.Admin">Fancyx.Admin/Fancyx.Admin.csproj</projectFile>
    <projectFile pubXmlPath="Fancyx.Admin/Properties/PublishProfiles/FolderProfile.pubxml">Fancyx.Admin/Fancyx.Admin.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f2b5492a-d06e-4066-b6da-74c28c3745f4" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/appsettings.Development.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/appsettings.Development.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/DotNetCore.CAP.PostgreSql.dll" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.deps.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.deps.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.exe" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.exe" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.staticwebassets.runtime.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Admin.staticwebassets.runtime.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Cap.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Cap.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Cap.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Cap.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Core.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Core.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Logger.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Logger.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Logger.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Logger.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.ObjectStorage.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.ObjectStorage.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.ObjectStorage.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.ObjectStorage.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Redis.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Redis.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Redis.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Redis.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Repository.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Repository.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Repository.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Repository.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Shared.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Shared.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Shared.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Fancyx.Shared.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/FreeSql.Provider.PostgreSQL.dll" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Microsoft.Bcl.AsyncInterfaces.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Microsoft.Bcl.AsyncInterfaces.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/NetTopologySuite.IO.PostGis.dll" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/NetTopologySuite.dll" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Npgsql.LegacyPostgis.dll" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Npgsql.NetTopologySuite.dll" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/Npgsql.dll" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/appsettings.Development.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/bin/Debug/net9.0/appsettings.Development.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.AssemblyInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.AssemblyInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.AssemblyInfoInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.AssemblyInfoInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.GeneratedMSBuildEditorConfig.editorconfig" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.GeneratedMSBuildEditorConfig.editorconfig" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.assets.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.assets.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.csproj.AssemblyReference.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.csproj.AssemblyReference.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.csproj.CoreCompileInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.csproj.CoreCompileInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.csproj.FileListAbsolute.txt" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.csproj.FileListAbsolute.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.genruntimeconfig.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.genruntimeconfig.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/Fancyx.Admin.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/apphost.exe" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/apphost.exe" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/ref/Fancyx.Admin.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/ref/Fancyx.Admin.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/refint/Fancyx.Admin.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/refint/Fancyx.Admin.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/rjimswa.dswa.cache.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/rjimswa.dswa.cache.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/rjsmcshtml.dswa.cache.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/rjsmcshtml.dswa.cache.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/rjsmrazor.dswa.cache.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/rjsmrazor.dswa.cache.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/rpswa.dswa.cache.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/rpswa.dswa.cache.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/staticwebassets.build.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/staticwebassets.build.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/staticwebassets.build.json.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/staticwebassets.build.json.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/staticwebassets.development.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Debug/net9.0/staticwebassets.development.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/Fancyx.Admin.csproj.nuget.dgspec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/Fancyx.Admin.csproj.nuget.dgspec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/project.assets.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/project.assets.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/project.nuget.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/project.nuget.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/project.packagespec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/project.packagespec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/rider.project.model.nuget.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/rider.project.model.nuget.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Admin/obj/rider.project.restore.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Admin/obj/rider.project.restore.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/Fancyx.Cap.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/Fancyx.Cap.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/FancyxCapModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/FancyxCapModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Cap.deps.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Cap.deps.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Cap.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Cap.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Cap.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Cap.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Core.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/bin/Debug/net9.0/Fancyx.Core.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.AssemblyInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.AssemblyInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.AssemblyInfoInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.AssemblyInfoInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.GeneratedMSBuildEditorConfig.editorconfig" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.GeneratedMSBuildEditorConfig.editorconfig" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.assets.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.assets.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.csproj.AssemblyReference.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.csproj.AssemblyReference.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.csproj.CoreCompileInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.csproj.CoreCompileInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.csproj.FileListAbsolute.txt" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.csproj.FileListAbsolute.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/Fancyx.Cap.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/ref/Fancyx.Cap.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/ref/Fancyx.Cap.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/refint/Fancyx.Cap.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Debug/net9.0/refint/Fancyx.Cap.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/Fancyx.Cap.csproj.nuget.dgspec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/Fancyx.Cap.csproj.nuget.dgspec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/project.assets.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/project.assets.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/project.nuget.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/project.nuget.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/project.packagespec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/project.packagespec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/rider.project.model.nuget.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/rider.project.model.nuget.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Cap/obj/rider.project.restore.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Cap/obj/rider.project.restore.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/bin/Debug/net9.0/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/bin/Debug/net9.0/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/bin/Debug/net9.0/Fancyx.Core.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/bin/Debug/net9.0/Fancyx.Core.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.AssemblyInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.AssemblyInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.AssemblyInfoInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.AssemblyInfoInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.GeneratedMSBuildEditorConfig.editorconfig" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.GeneratedMSBuildEditorConfig.editorconfig" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.assets.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.assets.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.csproj.FileListAbsolute.txt" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.csproj.FileListAbsolute.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/Fancyx.Core.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/ref/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/ref/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/refint/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Debug/net9.0/refint/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/Fancyx.Core.csproj.nuget.dgspec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/Fancyx.Core.csproj.nuget.dgspec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/project.assets.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/project.assets.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/project.nuget.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/project.nuget.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/project.packagespec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/project.packagespec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/rider.project.model.nuget.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/rider.project.model.nuget.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Core/obj/rider.project.restore.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Core/obj/rider.project.restore.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Cap.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Cap.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Cap.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Cap.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Core.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Core.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Logger.deps.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Logger.deps.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Logger.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Logger.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Logger.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Logger.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Repository.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Repository.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Repository.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/bin/Debug/net9.0/Fancyx.Repository.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.AssemblyInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.AssemblyInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.AssemblyInfoInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.AssemblyInfoInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.GeneratedMSBuildEditorConfig.editorconfig" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.GeneratedMSBuildEditorConfig.editorconfig" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.assets.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.assets.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.csproj.AssemblyReference.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.csproj.AssemblyReference.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.csproj.CoreCompileInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.csproj.CoreCompileInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.csproj.FileListAbsolute.txt" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.csproj.FileListAbsolute.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/Fancyx.Logger.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/ref/Fancyx.Logger.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/ref/Fancyx.Logger.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/refint/Fancyx.Logger.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Debug/net9.0/refint/Fancyx.Logger.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/Fancyx.Logger.csproj.nuget.dgspec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/Fancyx.Logger.csproj.nuget.dgspec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/project.assets.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/project.assets.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/project.nuget.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/project.nuget.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/project.packagespec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/project.packagespec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/rider.project.model.nuget.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/rider.project.model.nuget.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Logger/obj/rider.project.restore.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Logger/obj/rider.project.restore.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/bin/Debug/net9.0/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/bin/Debug/net9.0/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/bin/Debug/net9.0/Fancyx.Core.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/bin/Debug/net9.0/Fancyx.Core.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/bin/Debug/net9.0/Fancyx.ObjectStorage.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/bin/Debug/net9.0/Fancyx.ObjectStorage.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/bin/Debug/net9.0/Fancyx.ObjectStorage.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/bin/Debug/net9.0/Fancyx.ObjectStorage.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.AssemblyInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.AssemblyInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.AssemblyInfoInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.AssemblyInfoInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.GeneratedMSBuildEditorConfig.editorconfig" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.GeneratedMSBuildEditorConfig.editorconfig" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.assets.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.assets.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.csproj.AssemblyReference.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.csproj.AssemblyReference.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.csproj.CoreCompileInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.csproj.CoreCompileInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.csproj.FileListAbsolute.txt" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.csproj.FileListAbsolute.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/Fancyx.ObjectStorage.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/ref/Fancyx.ObjectStorage.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/ref/Fancyx.ObjectStorage.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/refint/Fancyx.ObjectStorage.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Debug/net9.0/refint/Fancyx.ObjectStorage.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Fancyx.ObjectStorage.csproj.nuget.dgspec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/Fancyx.ObjectStorage.csproj.nuget.dgspec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/project.assets.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/project.assets.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/project.nuget.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/project.nuget.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/project.packagespec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/project.packagespec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/rider.project.model.nuget.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/rider.project.model.nuget.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/rider.project.restore.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.ObjectStorage/obj/rider.project.restore.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/bin/Debug/net9.0/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/bin/Debug/net9.0/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/bin/Debug/net9.0/Fancyx.Core.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/bin/Debug/net9.0/Fancyx.Core.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/bin/Debug/net9.0/Fancyx.Redis.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/bin/Debug/net9.0/Fancyx.Redis.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/bin/Debug/net9.0/Fancyx.Redis.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/bin/Debug/net9.0/Fancyx.Redis.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.AssemblyInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.AssemblyInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.AssemblyInfoInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.AssemblyInfoInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.GeneratedMSBuildEditorConfig.editorconfig" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.GeneratedMSBuildEditorConfig.editorconfig" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.assets.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.assets.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.csproj.AssemblyReference.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.csproj.AssemblyReference.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.csproj.CoreCompileInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.csproj.CoreCompileInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.csproj.FileListAbsolute.txt" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.csproj.FileListAbsolute.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/Fancyx.Redis.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/ref/Fancyx.Redis.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/ref/Fancyx.Redis.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/refint/Fancyx.Redis.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Debug/net9.0/refint/Fancyx.Redis.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/Fancyx.Redis.csproj.nuget.dgspec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/Fancyx.Redis.csproj.nuget.dgspec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/project.assets.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/project.assets.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/project.nuget.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/project.nuget.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/project.packagespec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/project.packagespec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/rider.project.model.nuget.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/rider.project.model.nuget.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Redis/obj/rider.project.restore.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Redis/obj/rider.project.restore.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/Fancyx.Repository.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/Fancyx.Repository.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/FancyxRepositoryModule.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/FancyxRepositoryModule.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Core.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Core.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Core.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Core.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Repository.deps.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Repository.deps.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Repository.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Repository.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Repository.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/bin/Debug/net9.0/Fancyx.Repository.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.AssemblyInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.AssemblyInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.AssemblyInfoInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.AssemblyInfoInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.GeneratedMSBuildEditorConfig.editorconfig" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.GeneratedMSBuildEditorConfig.editorconfig" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.assets.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.assets.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.csproj.AssemblyReference.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.csproj.AssemblyReference.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.csproj.CoreCompileInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.csproj.CoreCompileInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.csproj.FileListAbsolute.txt" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.csproj.FileListAbsolute.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/Fancyx.Repository.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/ref/Fancyx.Repository.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/ref/Fancyx.Repository.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/refint/Fancyx.Repository.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Debug/net9.0/refint/Fancyx.Repository.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/Fancyx.Repository.csproj.nuget.dgspec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/Fancyx.Repository.csproj.nuget.dgspec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/project.assets.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/project.assets.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/project.nuget.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/project.nuget.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/project.packagespec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/project.packagespec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/rider.project.model.nuget.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/rider.project.model.nuget.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Repository/obj/rider.project.restore.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Repository/obj/rider.project.restore.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/bin/Debug/net9.0/Fancyx.Shared.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/bin/Debug/net9.0/Fancyx.Shared.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/bin/Debug/net9.0/Fancyx.Shared.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/bin/Debug/net9.0/Fancyx.Shared.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.AssemblyInfo.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.AssemblyInfo.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.AssemblyInfoInputs.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.AssemblyInfoInputs.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.GeneratedMSBuildEditorConfig.editorconfig" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.GeneratedMSBuildEditorConfig.editorconfig" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.assets.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.assets.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.csproj.FileListAbsolute.txt" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.csproj.FileListAbsolute.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.pdb" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/Fancyx.Shared.pdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/ref/Fancyx.Shared.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/ref/Fancyx.Shared.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/refint/Fancyx.Shared.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Debug/net9.0/refint/Fancyx.Shared.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/Fancyx.Shared.csproj.nuget.dgspec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/Fancyx.Shared.csproj.nuget.dgspec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/project.assets.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/project.assets.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/project.nuget.cache" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/project.nuget.cache" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/project.packagespec.json" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/project.packagespec.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/rider.project.model.nuget.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/rider.project.model.nuget.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Fancyx.Shared/obj/rider.project.restore.info" beforeDir="false" afterPath="$PROJECT_DIR$/Fancyx.Shared/obj/rider.project.restore.info" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../fancyx-web/src/pages/project/detail.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../fancyx-web/src/pages/project/detail.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../fancyx-web/src/pages/project/edit.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../fancyx-web/src/pages/project/edit.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../fancyx-web/src/pages/project/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../fancyx-web/src/pages/project/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../fancyx-web/src/pages/project/list.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../fancyx-web/src/pages/project/list.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../fancyx-web/src/router/dynamic.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/../fancyx-web/src/router/dynamic.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/3c298d9f00936d651cc47d221762474e25277672/src/coreclr/nativeaot/Runtime.Base/src/System/Runtime/StackFrameIterator.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zu87yuEsqVjocqB3IG1LZAMXC2" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET 启动设置配置文件.Fancyx.Admin.executor&quot;: &quot;Debug&quot;,
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected=".NET 启动设置配置文件.Fancyx.Admin">
    <configuration name="Fancyx.Admin: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/Fancyx.Admin/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="-4540979528881716688" uuid_low="-7543484206781561135" />
      <method v="2" />
    </configuration>
    <configuration name="Fancyx.Admin" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Fancyx.Admin/Fancyx.Admin.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
      <option name="LAUNCH_PROFILE_NAME" value="Fancyx.Admin" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f2b5492a-d06e-4066-b6da-74c28c3745f4" name="更改" comment="" />
      <created>1752569436392</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752569436392</updated>
      <workItem from="1752569439464" duration="3468000" />
      <workItem from="1752575812930" duration="170000" />
      <workItem from="1752576007152" duration="1201000" />
      <workItem from="1753097844814" duration="1899000" />
      <workItem from="1753269621405" duration="15503000" />
      <workItem from="1753444349905" duration="2323000" />
      <workItem from="1753450423804" duration="4823000" />
      <workItem from="1753786380620" duration="6099000" />
      <workItem from="1753799864089" duration="1021000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>