using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 更新隐患明细 DTO
    /// </summary>
    public class UpdateHiddenDangerDto
    {
        /// <summary>
        /// 隐患大类
        /// </summary>
        [Required(ErrorMessage = "隐患大类不能为空")]
        [MaxLength(100, ErrorMessage = "隐患大类不能超过100个字符")]
        public string DangerCategory { get; set; }

        /// <summary>
        /// 标准规范ID
        /// </summary>
        public long? StandardId { get; set; }

        /// <summary>
        /// 问题等级
        /// </summary>
        [Required(ErrorMessage = "问题等级不能为空")]
        [MaxLength(20, ErrorMessage = "问题等级不能超过20个字符")]
        public string ProblemLevel { get; set; }

        /// <summary>
        /// 安全隐患
        /// </summary>
        [Required(ErrorMessage = "安全隐患不能为空")]
        [MaxLength(500, ErrorMessage = "安全隐患不能超过500个字符")]
        public string SafetyHazard { get; set; }

        /// <summary>
        /// 法规条款
        /// </summary>
        [MaxLength(500, ErrorMessage = "法规条款不能超过500个字符")]
        public string RegulationClause { get; set; }

        /// <summary>
        /// 具体描述
        /// </summary>
        [Required(ErrorMessage = "具体描述不能为空")]
        [MaxLength(2000, ErrorMessage = "具体描述不能超过2000个字符")]
        public string Description { get; set; }

        /// <summary>
        /// 整改要求
        /// </summary>
        [Required(ErrorMessage = "整改要求不能为空")]
        [MaxLength(2000, ErrorMessage = "整改要求不能超过2000个字符")]
        public string RectificationRequirement { get; set; }

        /// <summary>
        /// 可能产生后果
        /// </summary>
        [MaxLength(1000, ErrorMessage = "可能产生后果不能超过1000个字符")]
        public string PossibleConsequences { get; set; }

        /// <summary>
        /// 整改建议
        /// </summary>
        [MaxLength(500, ErrorMessage = "整改建议不能超过500个字符")]
        public string RectificationSuggestion { get; set; }

        /// <summary>
        /// 是否需要复查
        /// </summary>
        [Required(ErrorMessage = "是否需要复查不能为空")]
        public bool NeedReview { get; set; }

        /// <summary>
        /// 照片列表
        /// </summary>
        public List<string> Photos { get; set; } = new List<string>();

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
    }
}