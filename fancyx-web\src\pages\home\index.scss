.home-page {
  .dashboard-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    border: none;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      min-height: auto;
      padding: 0 20px;
      background-color: rgba(126, 87, 194, 0.05);

      .ant-card-head-title {
        padding: 16px 0;
      }
    }

    .ant-card-body {
      padding: 20px;
    }
  }

  .project-features {
    padding-left: 20px;
    margin-top: 10px;

    li {
      margin-bottom: 8px;
      position: relative;

      &::before {
        content: "•";
        position: absolute;
        left: -15px;
        color: #7E57C2;
        font-weight: bold;
      }
    }
  }

  .repo-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 10px 0;

    .repo-button {
      background: rgba(126, 87, 194, 0.1);
      color: #7E57C2;
      border: none;
      transition: all 0.3s ease;

      &:hover {
        background: #7E57C2;
        color: white;
      }
    }
  }

  .repo-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 10px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 8px;
  }

  .tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .tech-tag {
      background: rgba(126, 87, 194, 0.1);
      color: #7E57C2;
      border: none;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 14px;
      display: flex;
      align-items: center;
      transition: all 0.3s ease;

      &:hover {
        background: #7E57C2;
        color: white;
      }

      .anticon {
        margin-right: 6px;
      }
    }
  }

  .changelog-item {
    padding: 10px 0;

    .changelog-list {
      padding-left: 20px;
      margin-top: 8px;

      li {
        margin-bottom: 6px;
        position: relative;

        &::before {
          content: "•";
          position: absolute;
          left: -15px;
          color: #7E57C2;
          font-weight: bold;
        }
      }
    }
  }

  .donation-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .qrcode-placeholder {
      width: 160px;
      height: 160px;
      background: rgba(126, 87, 194, 0.05);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;

      .qrcode-icon {
        font-size: 48px;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .main-content {
      .ant-col-md-16, .ant-col-md-8 {
        max-width: 100%;
        flex: 0 0 100%;
      }
    }
  }
}