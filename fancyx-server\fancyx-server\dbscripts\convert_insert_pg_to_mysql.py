#!/usr/bin/env python3
"""
convert_insert_pg_to_mysql.py
--------------------------------
批量将 PostgreSQL 导出脚本中的 INSERT 语句，转换为 MySQL 8.0 可执行的 INSERT 语句。
使用方式：
    python convert_insert_pg_to_mysql.py <input_pg_sql_file> <output_mysql_sql_file>
若不指定参数，默认读取 table_struct_data.sql，输出为 table_struct_data_mysql_inserts.sql。

转换规则：
1. 删除 schema 前缀 "public".
2. 将所有双引号(")的标识符改为 MySQL 反引号(`)。
3. 将布尔值 't' / 'f' 替换为 1 / 0（注意这里只替换单引号包裹的 t/f）。
4. 仅处理以 INSERT INTO 开头的行，其他行忽略。
5. 结果文件可直接在 MySQL 8.0+ 中执行。

Author : AI Converter
Date   : 2025-07-15
"""

import re
import sys
from pathlib import Path

# Regex 预编译
SCHEMA_PATTERN = re.compile(r'"public"\.')
DOUBLE_QUOTE_PATTERN = re.compile(r'"')
BOOL_TRUE_PATTERN = re.compile(r"'t'")
BOOL_FALSE_PATTERN = re.compile(r"'f'")


def convert_line(line: str) -> str | None:
    """若是 INSERT 语句则转换后返回，否则返回 None"""
    if not line.lstrip().lower().startswith("insert into"):
        return None

    # 移除 schema 前缀
    line = SCHEMA_PATTERN.sub('', line)
    # Identifier 改为反引号
    line = DOUBLE_QUOTE_PATTERN.sub('`', line)
    # 布尔值映射
    line = BOOL_TRUE_PATTERN.sub('1', line)
    line = BOOL_FALSE_PATTERN.sub('0', line)

    return line


def main():
    # 参数解析
    input_path = Path(sys.argv[1]) if len(sys.argv) > 1 else Path("table_struct_data.sql")
    output_path = Path(sys.argv[2]) if len(sys.argv) > 2 else Path("table_struct_data_mysql_inserts.sql")

    if not input_path.exists():
        sys.stderr.write(f"Input file {input_path} not found!\n")
        sys.exit(1)

    with input_path.open('r', encoding='utf-8') as fin, output_path.open('w', encoding='utf-8') as fout:
        for line in fin:
            converted = convert_line(line)
            if converted:
                fout.write(converted)

    print(f"✅ 转换完成，输出文件：{output_path}")


if __name__ == '__main__':
    main() 