{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "App": {"CorsOrigins": "http://localhost:8080", "DemonstrationMode": false, "AccountManyLogin": true}, "ConnectionStrings": {"Default": "Server=*************,2997;Database=ris_db;User Id=sqladmin;Password=***********$;TrustServerCertificate=true;Encrypt=false;"}, "Redis": {"Connection": "127.0.0.1:6379,password=123456"}, "Cap": {"RedisConnection": "127.0.0.1:6379,password=123456,defaultDatabase=1"}, "Jwt": {"ClockSkew": 300, "ValidAudience": "api", "ValidIssuer": "fancyx-admin", "IssuerSigningKey": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, "Oss": {"Bucket": "D:\\oss", "Domain": "http://localhost:5000/", "Aliyun": {"AccessKey": "", "AccessKeySecret": "", "Endpoint": "", "Bucket": "", "Timeout": 60000, "Domain": ""}}, "Snowflake": {"WorkerId": 1, "DataCenterId": 4}, "Mqtt": {"Port": 1883, "UserName": "admin", "Password": "123qwe*"}}