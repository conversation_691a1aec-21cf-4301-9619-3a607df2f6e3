using Fancyx.Repository.BaseEntity;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Fancyx.Core.Interfaces;

namespace Fancyx.Admin.Entities.Project
{
    [Table("sys_standard")]
    public class StandardDO : FullAuditedEntity, ITenant
    {
        /// <summary>
        /// 标准名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string Name { get; set; }

        /// <summary>
        /// 标准类型
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Type { get; set; }

        /// <summary>
        /// 隐患大类
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string DangerCategory { get; set; }

        /// <summary>
        /// 法规条款
        /// </summary>
        [MaxLength(500)]
        public string RegulationClause { get; set; }

        /// <summary>
        /// 具体要求
        /// </summary>
        [MaxLength(2000)]
        public string Requirements { get; set; }

        /// <summary>
        /// 状态（0启用，1停用）
        /// </summary>
        public int Status { get; set; } = 0;

        /// <summary>
        /// 租户ID
        /// </summary>
        public string? TenantId { get; set; }
    }
}