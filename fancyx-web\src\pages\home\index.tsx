import { <PERSON>, Row, Col, Typo<PERSON>, Timeline, Button, Space, Tooltip, Tag, Divider, Image } from 'antd';
import {
  GithubOutlined,
  BookOutlined,
  CodeOutlined,
  HistoryOutlined,
  GiftOutlined,
  StarOutlined,
  ForkOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import './index.scss';
import AliPay from '@/assets/alipay.jpg';


const HomePage = () => {
  // 更新日志数据
  const changelog = [

  ];

  // 技术栈数据
  const techStack = [

  ];

  // 仓库地址
  const repoLinks = [

  ];

  return (
    <div className="home-page">
      {/* 主要内容区域 */}
      
    </div>
  );
};

export default HomePage;
