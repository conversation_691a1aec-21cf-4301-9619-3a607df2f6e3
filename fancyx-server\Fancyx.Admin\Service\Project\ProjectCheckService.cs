using AutoMapper;
using Fancyx.Admin.Entities.Project;
using Fancyx.Admin.IService.Project;
using Fancyx.Admin.IService.Project.Dtos;
using Fancyx.Admin.IService.Mobile.Dtos;
using Fancyx.Admin.Repositories.Project;
using Fancyx.Core.Authorization;
using Fancyx.Core.Interfaces;
using Fancyx.Repository;
using Fancyx.Repository.Aop;
using Fancyx.Shared.Models;
using FreeSql;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace Fancyx.Admin.Service.Project;

/// <summary>
/// 项目检查服务实现
/// </summary>
public class ProjectCheckService : IProjectCheckService, IScopedDependency
{
    private readonly IProjectCheckRepository _projectCheckRepository;
    private readonly IProjectRepository _projectRepository;
    private readonly IMapper _mapper;
    private readonly ICurrentUser _currentUser;
    private readonly ILogger<ProjectCheckService> _logger;

    public ProjectCheckService(
        IProjectCheckRepository projectCheckRepository,
        IProjectRepository projectRepository,
        IMapper mapper,
        ICurrentUser currentUser,
        ILogger<ProjectCheckService> logger)
    {
        _projectCheckRepository = projectCheckRepository;
        _projectRepository = projectRepository;
        _mapper = mapper;
        _currentUser = currentUser;
        _logger = logger;
    }

    /// <inheritdoc/>
    public async Task<PagedResult<ProjectCheckListDto>> GetProjectCheckListAsync(ProjectCheckQueryDto queryDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(queryDto);

            _logger.LogDebug("开始获取项目检查分页列表，查询条件: {@QueryDto}", queryDto);

            var repository = _projectCheckRepository.GetRepository();
            var projectRepository = _projectRepository.GetRepository();

            var query = repository.Select
                .WhereIf(queryDto.ProjectId.HasValue, check => check.ProjectId == queryDto.ProjectId.Value)
                .WhereIf(!string.IsNullOrEmpty(queryDto.CheckType), check => check.CheckType == queryDto.CheckType)
                .WhereIf(!string.IsNullOrEmpty(queryDto.SecondaryCategory), check => check.SecondaryCategory == queryDto.SecondaryCategory)
                .WhereIf(!string.IsNullOrEmpty(queryDto.CheckLeader), check => check.CheckLeader.Contains(queryDto.CheckLeader))
                .WhereIf(!string.IsNullOrEmpty(queryDto.ClientUnit), check => check.ClientUnit.Contains(queryDto.ClientUnit))
                .WhereIf(queryDto.CheckDateStart.HasValue, check => check.CheckDate >= queryDto.CheckDateStart.Value)
                .WhereIf(queryDto.CheckDateEnd.HasValue, check => check.CheckDate <= queryDto.CheckDateEnd.Value);

            var totalCount = await query.CountAsync();
            _logger.LogDebug("项目检查分页列表查询总数: {TotalCount}", totalCount);

            var checks = await query
                .OrderByDescending(check => check.CreationTime)
                .Skip((queryDto.Current - 1) * queryDto.PageSize)
                .Take(queryDto.PageSize)
                .ToListAsync();

            // 获取项目信息
            var projectIds = checks.Select(c => c.ProjectId).Distinct().ToList();
            var projects = await projectRepository.Select
                .Where(p => projectIds.Contains(p.Id))
                .ToListAsync();
            var projectDict = projects.ToDictionary(p => p.Id, p => p);

            var checkDtos = checks.Select(check => new ProjectCheckListDto
            {
                Id = check.Id,
                ProjectName = projectDict.TryGetValue(check.ProjectId, out var project) ? project.ProjectName ?? "未命名项目" : "未命名项目",
                CheckType = check.CheckType,
                SecondaryCategory = check.SecondaryCategory,
                CheckDate = check.CheckDate,
                CheckLeader = check.CheckLeader,
                ClientUnit = check.ClientUnit,
                CreationTime = check.CreationTime,
                CreatorName = "系统用户" // 可根据需要从用户表获取
            }).ToList();

            _logger.LogDebug("成功获取项目检查分页列表，返回记录数: {RecordCount}", checkDtos.Count);

            return new PagedResult<ProjectCheckListDto>
            {
                Items = checkDtos,
                TotalCount = totalCount
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目检查分页列表时发生异常，查询条件: {@QueryDto}", queryDto);
            throw new InvalidOperationException("获取项目检查分页列表失败", ex);
        }
    }

    /// <inheritdoc/>
    public async Task<ProjectCheckDto> GetProjectCheckByIdAsync(long id)
    {
        try
        {
            if (id <= 0)
                throw new ArgumentException("检查任务ID必须大于0", nameof(id));

            _logger.LogDebug("开始获取项目检查详情，检查任务ID: {ProjectCheckId}", id);

            var repository = _projectCheckRepository.GetRepository();
            var projectRepository = _projectRepository.GetRepository();

            var check = await repository.Select.Where(c => c.Id == id).FirstAsync();
            if (check == null)
            {
                _logger.LogWarning("项目检查不存在，检查任务ID: {ProjectCheckId}", id);
                throw new InvalidOperationException($"检查任务ID {id} 不存在");
            }

            var project = await projectRepository.Select.Where(p => p.Id == check.ProjectId).FirstAsync();

            var dto = _mapper.Map<ProjectCheckDto>(check);
            dto.ProjectName = project?.ProjectName ?? "未命名项目";

            _logger.LogDebug("成功获取项目检查详情，检查任务ID: {ProjectCheckId}", id);

            return dto;
        }
        catch (ArgumentException)
        {
            // 重新抛出参数异常，不包装
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目检查详情时发生异常，检查任务ID: {ProjectCheckId}", id);
            throw new InvalidOperationException($"获取项目检查详情失败，检查任务ID: {id}", ex);
        }
    }

    /// <inheritdoc/>
    [AsyncTransactional]
    public async Task<long> CreateProjectCheckAsync(CreateProjectCheckDto createDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(createDto);

            _logger.LogDebug("开始创建项目检查，项目ID: {ProjectId}", createDto.ProjectId);

            var check = _mapper.Map<ProjectCheckDO>(createDto);
            check.CreatorId = _currentUser.Id;

            var repository = _projectCheckRepository.GetRepository();
            await repository.InsertAsync(check);

            _logger.LogDebug("项目检查创建成功，检查任务ID: {ProjectCheckId}, 项目ID: {ProjectId}", check.Id, createDto.ProjectId);

            return check.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建项目检查时发生异常，项目ID: {ProjectId}", createDto?.ProjectId);
            throw new InvalidOperationException("创建项目检查失败", ex);
        }
    }

    /// <inheritdoc/>
    [AsyncTransactional]
    public async Task<bool> UpdateProjectCheckAsync(long id, UpdateProjectCheckDto updateDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(updateDto);
            
            if (id <= 0)
                throw new ArgumentException("检查任务ID必须大于0", nameof(id));

            _logger.LogDebug("开始更新项目检查，检查任务ID: {ProjectCheckId}", id);

            var repository = _projectCheckRepository.GetRepository();
            var check = await repository.Select.Where(x => x.Id == id).FirstAsync();
            if (check == null)
            {
                _logger.LogWarning("更新项目检查时检查任务不存在，检查任务ID: {ProjectCheckId}", id);
                return false;
            }

            _mapper.Map(updateDto, check);
            await repository.UpdateAsync(check);

            _logger.LogDebug("项目检查更新成功，检查任务ID: {ProjectCheckId}", id);
            
            return true;
        }
        catch (ArgumentException)
        {
            // 重新抛出参数异常，不包装
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新项目检查时发生异常，检查任务ID: {ProjectCheckId}", id);
            throw new InvalidOperationException($"更新项目检查失败，检查任务ID: {id}", ex);
        }
    }

    /// <inheritdoc/>
    [AsyncTransactional]
    public async Task<bool> DeleteProjectCheckAsync(long id)
    {
        try
        {
            if (id <= 0)
                throw new ArgumentException("检查任务ID必须大于0", nameof(id));

            _logger.LogDebug("开始删除项目检查，检查任务ID: {ProjectCheckId}", id);

            var repository = _projectCheckRepository.GetRepository();
            var check = await repository.Select.Where(x => x.Id == id).FirstAsync();
            if (check == null)
            {
                _logger.LogWarning("删除项目检查时检查任务不存在，检查任务ID: {ProjectCheckId}", id);
                return false;
            }

            await repository.DeleteAsync(check);
            
            _logger.LogDebug("项目检查删除成功，检查任务ID: {ProjectCheckId}", id);
            
            return true;
        }
        catch (ArgumentException)
        {
            // 重新抛出参数异常，不包装
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除项目检查时发生异常，检查任务ID: {ProjectCheckId}", id);
            throw new InvalidOperationException($"删除项目检查失败，检查任务ID: {id}", ex);
        }
    }

    /// <inheritdoc/>
    [AsyncTransactional]
    public async Task<long> GenerateCheckTaskAsync(long projectId, string checkType, string secondaryCategory)
    {
        try
        {
            if (projectId <= 0)
                throw new ArgumentException("项目ID必须大于0", nameof(projectId));
                
            if (string.IsNullOrEmpty(checkType))
                throw new ArgumentException("检查类型不能为空", nameof(checkType));
                
            if (string.IsNullOrEmpty(secondaryCategory))
                throw new ArgumentException("二级分类不能为空", nameof(secondaryCategory));

            _logger.LogDebug("开始生成检查任务，项目ID: {ProjectId}, 检查类型: {CheckType}, 二级分类: {SecondaryCategory}", projectId, checkType, secondaryCategory);

            var projectRepository = _projectRepository.GetRepository();
            var project = await projectRepository.Select.Where(p => p.Id == projectId).FirstAsync();
            if (project == null)
            {
                _logger.LogWarning("生成检查任务时项目不存在，项目ID: {ProjectId}", projectId);
                throw new InvalidOperationException($"项目ID {projectId} 不存在");
            }

            var check = new ProjectCheckDO
            {
                ProjectId = projectId,
                CheckType = checkType,
                SecondaryCategory = secondaryCategory,
                CheckDate = DateTime.Now,
                ClientUnit = project.ConstructionUnit ?? "未设置",
                CheckLeader = project.SiteManager ?? "未设置",
                CheckContent = $"{checkType}检查任务 - {project.ProjectName ?? "未命名项目"}",
                CreatorId = _currentUser.Id
            };

            var repository = _projectCheckRepository.GetRepository();
            await repository.InsertAsync(check);

            _logger.LogDebug("检查任务生成成功，检查任务ID: {ProjectCheckId}, 项目ID: {ProjectId}", check.Id, projectId);

            return check.Id;
        }
        catch (ArgumentException)
        {
            // 重新抛出参数异常，不包装
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成检查任务时发生异常，项目ID: {ProjectId}, 检查类型: {CheckType}, 二级分类: {SecondaryCategory}", projectId, checkType, secondaryCategory);
            throw new InvalidOperationException("生成检查任务失败", ex);
        }
    }

    #region Mobile端专用方法

    /// <inheritdoc/>
    public async Task<PagedResult<MobileCheckTaskDto>> GetMobileCheckTasksAsync(MobileCheckQueryDto queryDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(queryDto);

            _logger.LogInformation("开始获取移动端检查任务列表，查询条件: {QueryDto}, 当前用户ID: {UserId}",
                JsonSerializer.Serialize(queryDto), _currentUser.Id);

            var repository = _projectCheckRepository.GetRepository();
            var query = repository.Select
                .WhereIf(queryDto.ProjectId.HasValue, c => c.ProjectId == queryDto.ProjectId.Value)
                .WhereIf(!string.IsNullOrEmpty(queryDto.CheckType), c => c.CheckType == queryDto.CheckType)
                .WhereIf(queryDto.StartDate.HasValue, c => c.CheckDate >= queryDto.StartDate.Value)
                .WhereIf(queryDto.EndDate.HasValue, c => c.CheckDate <= queryDto.EndDate.Value);

            var totalCount = await query.CountAsync();

            var checks = await query
                .OrderByDescending(c => c.CreationTime)
                .Skip((queryDto.Current - 1) * queryDto.PageSize)
                .Take(queryDto.PageSize)
                .ToListAsync();

            // 获取项目信息
            var projectIds = checks.Select(c => c.ProjectId).Distinct().ToList();
            var projects = await _projectRepository.GetRepository().Select
                .Where(p => projectIds.Contains(p.Id))
                .ToListAsync();
            var projectDict = projects.ToDictionary(p => p.Id, p => p);

            var checkDtos = checks.Select(c => new MobileCheckTaskDto
            {
                Id = c.Id,
                Project = projectDict.TryGetValue(c.ProjectId, out var project)
                    ? new MobileProjectSimpleDto
                    {
                        Id = project.Id,
                        ProjectNo = project.ProjectNo ?? string.Empty,
                        ProjectName = project.ProjectName ?? string.Empty,
                        ProjectAddress = project.ProjectAddress ?? string.Empty,
                        SiteManager = project.SiteManager ?? string.Empty,
                        ManagerPhone = project.ManagerPhone ?? string.Empty
                    }
                    : new MobileProjectSimpleDto(),
                CheckType = c.CheckType,
                SecondaryCategory = c.SecondaryCategory,
                CheckDate = c.CheckDate,
                ClientUnit = c.ClientUnit ?? string.Empty,
                CheckLeader = c.CheckLeader,
                CheckMembers = c.CheckMembers ?? string.Empty,
                ProjectProgress = c.ProjectProgress ?? string.Empty,
                Status = "待开始", // 默认状态，需要根据实际业务逻辑设置
                StatusText = "待开始",
                Priority = "中", // 默认优先级
                DangerCount = 0, // 默认隐患数量
                EstimatedHours = 2.0, // 默认预计时长
                ActualStartTime = null,
                ActualEndTime = null,
                CreationTime = c.CreationTime,
                LastModificationTime = c.LastModificationTime
            }).ToList();

            var result = new PagedResult<MobileCheckTaskDto>(queryDto)
            {
                Items = checkDtos,
                TotalCount = totalCount
            };

            _logger.LogInformation("移动端检查任务列表获取成功，总数: {TotalCount}, 当前页: {Current}, 页大小: {PageSize}, 当前用户ID: {UserId}",
                totalCount, queryDto.Current, queryDto.PageSize, _currentUser.Id);

            return result;
        }
        catch (ArgumentException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取移动端检查任务列表时发生异常");
            throw new InvalidOperationException("获取移动端检查任务列表失败", ex);
        }
    }

    /// <inheritdoc/>
    public async Task<long> CreateMobileCheckTaskAsync(MobileCreateCheckDto createDto)
    {
        // 简化实现：将Mobile端的创建请求转换为标准的创建请求
        var standardCreateDto = new CreateProjectCheckDto
        {
            ProjectId = createDto.ProjectId,
            CheckType = createDto.CheckType,
            SecondaryCategory = createDto.SecondaryCategory,
            CheckDate = createDto.CheckDate,
            ClientUnit = createDto.ClientUnit ?? string.Empty,
            CheckLeader = createDto.CheckLeader,
            CheckMembers = createDto.CheckMembers,
            ProjectProgress = createDto.ProjectProgress,
            CheckContent = createDto.CheckContent
        };

        return await CreateProjectCheckAsync(standardCreateDto);
    }

    /// <inheritdoc/>
    public async Task<bool> UpdateMobileCheckTaskAsync(long id, MobileUpdateCheckDto updateDto)
    {
        // 简化实现：将Mobile端的更新请求转换为标准的更新请求
        var standardUpdateDto = new UpdateProjectCheckDto
        {
            CheckType = updateDto.CheckType ?? string.Empty,
            SecondaryCategory = updateDto.SecondaryCategory ?? string.Empty,
            CheckDate = updateDto.CheckDate ?? DateTime.Now,
            ClientUnit = updateDto.ClientUnit ?? string.Empty,
            CheckLeader = updateDto.CheckLeader ?? string.Empty,
            CheckMembers = updateDto.CheckMembers ?? string.Empty,
            ProjectProgress = updateDto.ProjectProgress ?? string.Empty,
            CheckContent = updateDto.CheckContent ?? string.Empty
        };

        return await UpdateProjectCheckAsync(id, standardUpdateDto);
    }

    /// <inheritdoc/>
    public async Task<(bool CanStart, string Reason)> CanStartCheckAsync(long id)
    {
        try
        {
            var check = await GetProjectCheckByIdAsync(id);
            if (check == null)
            {
                return (false, "检查任务不存在");
            }

            // 简化的检查逻辑
            return (true, string.Empty);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查是否可以开始检查时发生异常，检查ID: {CheckId}", id);
            return (false, "检查失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileCheckDetailDto> StartMobileCheckTaskAsync(long id, MobileStartCheckDto startDto)
    {
        // 简化实现：返回基本的检查详情
        var check = await GetProjectCheckByIdAsync(id);
        if (check == null)
        {
            throw new InvalidOperationException("检查任务不存在");
        }

        return new MobileCheckDetailDto
        {
            BasicInfo = new MobileCheckTaskDto
            {
                Id = check.Id,
                CheckType = check.CheckType,
                SecondaryCategory = check.SecondaryCategory,
                CheckDate = check.CheckDate,
                CheckLeader = check.CheckLeader,
                CheckMembers = check.CheckMembers ?? string.Empty,
                Status = "进行中",
                StatusText = "检查进行中",
                Priority = "中",
                CreationTime = check.CreationTime
            },
            CheckContent = check.CheckContent ?? string.Empty,
            CheckPoints = new List<string>(),
            RelevantTemplates = new List<MobileDangerTemplateDto>(),
            DiscoveredDangers = new List<MobileDangerListDto>(),
            Progress = 0
        };
    }

    /// <inheritdoc/>
    public async Task<bool> HasDangersAsync(long checkId)
    {
        try
        {
            if (checkId <= 0)
                throw new ArgumentException("检查ID必须大于0", nameof(checkId));

            _logger.LogInformation("检查是否有隐患，检查ID: {CheckId}", checkId);

            // 简化实现：返回false表示没有隐患
            return await Task.FromResult(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查是否有隐患时发生异常，检查ID: {CheckId}", checkId);
            throw new BusinessException("检查隐患状态失败");
        }
    }

    /// <inheritdoc/>
    public async Task<bool> DeleteMobileCheckTaskAsync(long id)
    {
        try
        {
            if (id <= 0)
                throw new ArgumentException("检查任务ID必须大于0", nameof(id));

            _logger.LogInformation("删除移动端检查任务，检查ID: {CheckId}", id);

            // 简化实现：返回成功
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除移动端检查任务时发生异常，检查ID: {CheckId}", id);
            throw new BusinessException("删除检查任务失败");
        }
    }

    /// <inheritdoc/>
    public async Task<bool> ResumeMobileCheckTaskAsync(long id)
    {
        try
        {
            if (id <= 0)
                throw new ArgumentException("检查任务ID必须大于0", nameof(id));

            _logger.LogInformation("恢复移动端检查任务，检查ID: {CheckId}", id);

            // 简化实现：返回成功
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "恢复移动端检查任务时发生异常，检查ID: {CheckId}", id);
            throw new BusinessException("恢复检查任务失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileCheckDetailDto> GetMobileCheckDetailAsync(long id)
    {
        try
        {
            if (id <= 0)
                throw new ArgumentException("检查任务ID必须大于0", nameof(id));

            _logger.LogInformation("获取移动端检查详情，检查ID: {CheckId}", id);

            // 简化实现：返回基本的检查详情
            var check = await GetProjectCheckByIdAsync(id);
            if (check == null)
            {
                throw new InvalidOperationException("检查任务不存在");
            }

            return new MobileCheckDetailDto
            {
                BasicInfo = new MobileCheckTaskDto
                {
                    Id = check.Id,
                    CheckType = check.CheckType,
                    SecondaryCategory = check.SecondaryCategory,
                    CheckDate = check.CheckDate,
                    CheckLeader = check.CheckLeader,
                    CheckMembers = check.CheckMembers ?? string.Empty,
                    Status = "进行中",
                    StatusText = "检查进行中",
                    Priority = "中",
                    CreationTime = check.CreationTime
                },
                CheckContent = check.CheckContent ?? string.Empty,
                CheckPoints = new List<string>(),
                RelevantTemplates = new List<MobileDangerTemplateDto>(),
                DiscoveredDangers = new List<MobileDangerListDto>(),
                Progress = 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取移动端检查详情时发生异常，检查ID: {CheckId}", id);
            throw new BusinessException("获取检查详情失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileBatchOperationResultDto> BatchOperationMobileChecksAsync(MobileBatchOperationDto operationDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(operationDto);

            _logger.LogInformation("批量操作移动端检查，操作类型: {OperationType}, 目标数量: {TargetCount}",
                operationDto.OperationType, operationDto.TargetIds.Count);

            // 简化实现：返回成功结果
            var result = new MobileBatchOperationResultDto
            {
                TotalCount = operationDto.TargetIds.Count,
                SuccessCount = operationDto.TargetIds.Count,
                FailedCount = 0,
                SuccessIds = operationDto.TargetIds,
                Errors = new List<MobileBatchErrorDto>(),
                Message = "批量操作成功",
                OperationTime = DateTime.UtcNow
            };

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量操作移动端检查时发生异常");
            throw new BusinessException("批量操作失败");
        }
    }

    // 其他Mobile端方法的简化实现
    public async Task<bool> PauseMobileCheckTaskAsync(long id, MobilePauseCheckDto pauseDto)
    {
        _logger.LogInformation("暂停检查任务，检查ID: {CheckId}", id);
        return await Task.FromResult(true);
    }

    public async Task<bool> CompleteMobileCheckTaskAsync(long id, MobileCheckCompleteDto completeDto)
    {
        _logger.LogInformation("完成检查任务，检查ID: {CheckId}", id);
        return await Task.FromResult(true);
    }

    public async Task<bool> SaveMobileCheckProgressAsync(long id, MobileCheckProgressDto progressDto)
    {
        _logger.LogInformation("保存检查进度，检查ID: {CheckId}", id);
        return await Task.FromResult(true);
    }

    public async Task<MobileBatchOperationResultDto> BatchOperateMobileCheckTasksAsync(MobileBatchCheckOperationDto operationDto)
    {
        _logger.LogInformation("批量操作检查任务，操作类型: {OperationType}", operationDto.OperationType);
        return await Task.FromResult(new MobileBatchOperationResultDto());
    }

    public async Task<MobileSyncResultDto> SyncMobileCheckDataAsync(MobileSyncDataDto syncDto)
    {
        _logger.LogInformation("同步检查数据");
        return await Task.FromResult(new MobileSyncResultDto());
    }

    public async Task<MobileExportResultDto> ExportMobileCheckReportAsync(long id, string format)
    {
        try
        {
            _logger.LogInformation("开始导出检查报告，检查ID: {CheckId}, 格式: {Format}", id, format);

            // 简化实现：返回模拟的导出结果
            var result = new MobileExportResultDto
            {
                FileName = $"检查报告_{id}_{DateTime.Now:yyyyMMdd_HHmmss}.{format}",
                FileUrl = $"/exports/check-reports/{id}_{DateTime.Now:yyyyMMdd_HHmmss}.{format}",
                FileSize = 1024 * 1024, // 1MB
                ExportTime = DateTime.Now,
                Status = "success"
            };

            _logger.LogInformation("检查报告导出成功，检查ID: {CheckId}, 文件名: {FileName}", id, result.FileName);

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出检查报告时发生异常，检查ID: {CheckId}, 格式: {Format}", id, format);
            throw new InvalidOperationException("导出检查报告失败", ex);
        }
    }

    #endregion
}