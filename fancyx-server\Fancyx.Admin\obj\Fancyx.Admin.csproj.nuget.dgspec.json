{"format": 1, "restore": {"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\Fancyx.Admin.csproj": {}}, "projects": {"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\Fancyx.Admin.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\Fancyx.Admin.csproj", "projectName": "Fancyx.Admin", "projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\Fancyx.Admin.csproj", "packagesPath": "D:\\开发环境\\nuget", "outputPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj"}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Logger\\Fancyx.Logger.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Logger\\Fancyx.Logger.csproj"}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\Fancyx.ObjectStorage.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\Fancyx.ObjectStorage.csproj"}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Redis\\Fancyx.Redis.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Redis\\Fancyx.Redis.csproj"}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj"}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Shared\\Fancyx.Shared.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Shared\\Fancyx.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Coravel": {"target": "Package", "version": "[5.0.4, )"}, "MQTTnet": {"target": "Package", "version": "[5.0.1.1416, )"}, "MQTTnet.AspNetCore": {"target": "Package", "version": "[5.0.1.1416, )"}, "MQTTnet.Server": {"target": "Package", "version": "[5.0.1.1416, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj", "projectName": "Fancyx.Cap", "projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj", "packagesPath": "D:\\开发环境\\nuget", "outputPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"DotNetCore.CAP": {"target": "Package", "version": "[8.3.4, )"}, "DotNetCore.CAP.Dashboard": {"target": "Package", "version": "[8.3.4, )"}, "DotNetCore.CAP.RedisStreams": {"target": "Package", "version": "[8.3.4, )"}, "DotNetCore.CAP.SqlServer": {"target": "Package", "version": "[8.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj", "projectName": "Fancyx.Core", "projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj", "packagesPath": "D:\\开发环境\\nuget", "outputPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "Autofac": {"target": "Package", "version": "[8.2.0, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[10.0.0, )"}, "Autofac.Extras.DynamicProxy": {"target": "Package", "version": "[7.1.0, )"}, "Castle.Core": {"target": "Package", "version": "[5.2.1, )"}, "Castle.Core.AsyncInterceptor": {"target": "Package", "version": "[2.1.0, )"}, "IP2Region.Net": {"target": "Package", "version": "[2.0.2, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.6, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.14, )"}, "Microsoft.Extensions.DependencyModel": {"target": "Package", "version": "[9.0.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "UAParser": {"target": "Package", "version": "[3.1.47, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Logger\\Fancyx.Logger.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Logger\\Fancyx.Logger.csproj", "projectName": "Fancyx.Logger", "projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Logger\\Fancyx.Logger.csproj", "packagesPath": "D:\\开发环境\\nuget", "outputPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Logger\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj"}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[1.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\Fancyx.ObjectStorage.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\Fancyx.ObjectStorage.csproj", "projectName": "Fancyx.ObjectStorage", "projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\Fancyx.ObjectStorage.csproj", "packagesPath": "D:\\开发环境\\nuget", "outputPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Aliyun.OSS.SDK.NetCore": {"target": "Package", "version": "[2.14.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Redis\\Fancyx.Redis.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Redis\\Fancyx.Redis.csproj", "projectName": "Fancyx.Redis", "projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Redis\\Fancyx.Redis.csproj", "packagesPath": "D:\\开发环境\\nuget", "outputPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Redis\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FreeRedis": {"target": "Package", "version": "[1.3.7, )"}, "RedLock.net": {"target": "Package", "version": "[2.3.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj", "projectName": "Fancyx.Repository", "projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj", "packagesPath": "D:\\开发环境\\nuget", "outputPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj": {"projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FreeSql": {"target": "Package", "version": "[3.5.209, )"}, "FreeSql.Cloud": {"target": "Package", "version": "[2.0.1, )"}, "FreeSql.DbContext": {"target": "Package", "version": "[3.5.209, )"}, "FreeSql.Provider.SqlServer": {"target": "Package", "version": "[3.5.209, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Shared\\Fancyx.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Shared\\Fancyx.Shared.csproj", "projectName": "Fancyx.Shared", "projectPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Shared\\Fancyx.Shared.csproj", "packagesPath": "D:\\开发环境\\nuget", "outputPath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}