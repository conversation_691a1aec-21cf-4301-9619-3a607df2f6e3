# 通过API添加项目菜单配置

如果你不想直接操作数据库，可以通过后端API来添加菜单配置。

## API接口信息
- **接口地址**: `POST /api/menu/add`
- **权限要求**: `Sys.Menu.Add`
- **Content-Type**: `application/json`

## 请求示例

### 1. 添加项目管理文件夹
```bash
curl -X POST "http://your-api-domain/api/menu/add" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "项目管理",
    "icon": "project",
    "path": "/project",
    "menuType": 1,
    "parentId": null,
    "sort": 10,
    "display": true,
    "component": null
  }'
```

### 2. 添加项目列表菜单
```bash
curl -X POST "http://your-api-domain/api/menu/add" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "项目列表",
    "icon": "list",
    "path": "/project/list",
    "menuType": 2,
    "permission": "Project.List",
    "parentId": "项目管理文件夹的ID",
    "sort": 1,
    "display": true,
    "component": "pages/project/list"
  }'
```

### 3. 添加项目编辑菜单（重要！）
```bash
curl -X POST "http://your-api-domain/api/menu/add" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "项目编辑",
    "icon": "edit",
    "path": "/project/edit",
    "menuType": 2,
    "permission": "Project.Edit",
    "parentId": "项目管理文件夹的ID",
    "sort": 2,
    "display": false,
    "component": "pages/project/edit"
  }'
```

### 4. 添加项目详情菜单
```bash
curl -X POST "http://your-api-domain/api/menu/add" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "项目详情",
    "icon": "detail",
    "path": "/project/detail/:id",
    "menuType": 2,
    "permission": "Project.Detail",
    "parentId": "项目管理文件夹的ID",
    "sort": 3,
    "display": false,
    "component": "pages/project/detail"
  }'
```

## 使用Postman或其他API工具

你也可以使用Postman等工具，按照以下步骤：

1. **设置请求头**:
   - `Authorization: Bearer YOUR_ACCESS_TOKEN`
   - `Content-Type: application/json`

2. **请求体参数说明**:
   - `title`: 菜单名称
   - `icon`: 图标名称
   - `path`: 路由路径
   - `menuType`: 菜单类型 (1=文件夹, 2=菜单, 3=按钮)
   - `permission`: 权限码
   - `parentId`: 父级菜单ID (顶级菜单为null)
   - `sort`: 排序号
   - `display`: 是否显示 (true/false)
   - `component`: 前端组件路径

## 注意事项

1. **获取Token**: 需要先登录获取有效的访问令牌
2. **权限检查**: 确保当前用户有 `Sys.Menu.Add` 权限
3. **父级ID**: 添加子菜单时需要先获取父级菜单的ID
4. **组件路径**: 确保组件路径与前端实际路径匹配

## 验证结果

添加完成后：
1. 调用 `/api/account/userAuth` 查看菜单是否返回
2. 前端重新登录测试路由是否正常
3. 检查 `/project/edit` 是否还会出现404错误