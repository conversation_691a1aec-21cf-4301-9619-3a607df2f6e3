﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="ip2region.xdb">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.14" />
		<PackageReference Include="Autofac" Version="8.2.0" />
		<PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
		<PackageReference Include="Castle.Core" Version="5.2.1" />
		<PackageReference Include="Castle.Core.AsyncInterceptor" Version="2.1.0" />
		<PackageReference Include="Autofac.Extras.DynamicProxy" Version="7.1.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyModel" Version="9.0.3" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.6" />
		<PackageReference Include="UAParser" Version="3.1.47" />
		<PackageReference Include="IP2Region.Net" Version="2.0.2" />
		<PackageReference Include="AutoMapper" Version="12.0.1" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
	</ItemGroup>
</Project>
