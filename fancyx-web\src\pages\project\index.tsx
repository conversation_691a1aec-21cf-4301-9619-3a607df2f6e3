import React from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';

const ProjectIndex: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // 如果访问 /project 根路径，重定向到项目列表
  React.useEffect(() => {
    if (location.pathname === '/project' || location.pathname === '/project/') {
      navigate('/project/list', { replace: true });
    }
  }, [location.pathname, navigate]);

  return (
    <div className="project-container">
      <Outlet />
    </div>
  );
};

export default ProjectIndex;
