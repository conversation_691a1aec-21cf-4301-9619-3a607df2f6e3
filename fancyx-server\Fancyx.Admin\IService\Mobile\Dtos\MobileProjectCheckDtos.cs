using Microsoft.AspNetCore.Http;

namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端检查查询DTO
/// </summary>
public class MobileCheckQueryDto : PageSearch
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long? ProjectId { get; set; }
    
    /// <summary>
    /// 检查类型
    /// </summary>
    public string? CheckType { get; set; }
    
    /// <summary>
    /// 检查状态（待开始/进行中/已完成）
    /// </summary>
    public string? Status { get; set; }
    
    /// <summary>
    /// 检查组长ID（我负责的检查）
    /// </summary>
    public long? CheckLeaderId { get; set; }
    
    /// <summary>
    /// 检查日期范围开始
    /// </summary>
    public DateTime? StartDate { get; set; }
    
    /// <summary>
    /// 检查日期范围结束
    /// </summary>
    public DateTime? EndDate { get; set; }
    
    /// <summary>
    /// 是否只显示我的任务
    /// </summary>
    public bool OnlyMyTasks { get; set; } = true;
}

/// <summary>
/// 移动端检查任务DTO
/// </summary>
public class MobileCheckTaskDto
{
    /// <summary>
    /// 检查任务ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 项目信息
    /// </summary>
    public MobileProjectSimpleDto Project { get; set; } = new();
    
    /// <summary>
    /// 检查类型
    /// </summary>
    public string CheckType { get; set; } = string.Empty;
    
    /// <summary>
    /// 二级分类
    /// </summary>
    public string SecondaryCategory { get; set; } = string.Empty;
    
    /// <summary>
    /// 检查日期
    /// </summary>
    public DateTime CheckDate { get; set; }
    
    /// <summary>
    /// 委托单位
    /// </summary>
    public string ClientUnit { get; set; } = string.Empty;
    
    /// <summary>
    /// 检查组长
    /// </summary>
    public string CheckLeader { get; set; } = string.Empty;
    
    /// <summary>
    /// 检查组员
    /// </summary>
    public string CheckMembers { get; set; } = string.Empty;
    
    /// <summary>
    /// 工程进度
    /// </summary>
    public string ProjectProgress { get; set; } = string.Empty;
    
    /// <summary>
    /// 检查状态
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 状态显示文字
    /// </summary>
    public string StatusText { get; set; } = string.Empty;
    
    /// <summary>
    /// 优先级（高/中/低）
    /// </summary>
    public string Priority { get; set; } = string.Empty;
    
    /// <summary>
    /// 已发现隐患数量
    /// </summary>
    public int DangerCount { get; set; }
    
    /// <summary>
    /// 预计耗时（小时）
    /// </summary>
    public double? EstimatedHours { get; set; }
    
    /// <summary>
    /// 实际开始时间
    /// </summary>
    public DateTime? ActualStartTime { get; set; }
    
    /// <summary>
    /// 实际结束时间
    /// </summary>
    public DateTime? ActualEndTime { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreationTime { get; set; }
    
    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime? LastModificationTime { get; set; }
}

/// <summary>
/// 移动端检查详情DTO
/// </summary>
public class MobileCheckDetailDto
{
    /// <summary>
    /// 基本信息
    /// </summary>
    public MobileCheckTaskDto BasicInfo { get; set; } = new();
    
    /// <summary>
    /// 检查内容
    /// </summary>
    public string CheckContent { get; set; } = string.Empty;
    
    /// <summary>
    /// 检查要点
    /// </summary>
    public List<string> CheckPoints { get; set; } = new();
    
    /// <summary>
    /// 相关的隐患模板
    /// </summary>
    public List<MobileDangerTemplateDto> RelevantTemplates { get; set; } = new();
    
    /// <summary>
    /// 已发现的隐患列表
    /// </summary>
    public List<MobileDangerListDto> DiscoveredDangers { get; set; } = new();
    
    /// <summary>
    /// 检查进度（百分比）
    /// </summary>
    public int Progress { get; set; }
    

    
    /// <summary>
    /// 检查位置
    /// </summary>
    public MobileLocationDto? CheckLocation { get; set; }
}

/// <summary>
/// 移动端检查完成DTO
/// </summary>
public class MobileCheckCompleteDto
{
    /// <summary>
    /// 检查总结
    /// </summary>
    public string Summary { get; set; } = string.Empty;
    
    /// <summary>
    /// 整体评价（优秀/良好/一般/较差）
    /// </summary>
    public string OverallRating { get; set; } = string.Empty;
    
    /// <summary>
    /// 检查建议
    /// </summary>
    public string Recommendations { get; set; } = string.Empty;
    
    /// <summary>
    /// 下次检查建议时间
    /// </summary>
    public DateTime? NextCheckDate { get; set; }
    
    /// <summary>
    /// 检查用时（分钟）
    /// </summary>
    public int? DurationMinutes { get; set; }
    
    /// <summary>
    /// 检查照片（现场整体照片）
    /// </summary>
    public List<string> OverallPhotos { get; set; } = new();
    
    /// <summary>
    /// 完成位置
    /// </summary>
    public MobileLocationDto CompletionLocation { get; set; } = new();
    
    /// <summary>
    /// 签名图片（检查组长签名）
    /// </summary>
    public string? SignatureImage { get; set; }
}






/// <summary>
/// 移动端隐患模板DTO
/// </summary>
public class MobileDangerTemplateDto
{
    /// <summary>
    /// 模板ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 隐患大类
    /// </summary>
    public string Category { get; set; } = string.Empty;
    
    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 模板描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 推荐等级
    /// </summary>
    public string RecommendedLevel { get; set; } = string.Empty;
    
    /// <summary>
    /// 整改要求模板
    /// </summary>
    public string RectificationTemplate { get; set; } = string.Empty;
    
    /// <summary>
    /// 相关标准规范
    /// </summary>
    public string? RelatedStandards { get; set; }
    
    /// <summary>
    /// 使用频次
    /// </summary>
    public int UsageCount { get; set; }
}




/// <summary>
/// 移动端同步错误DTO
/// </summary>
public class MobileSyncErrorDto
{
    /// <summary>
    /// 错误类型（check/danger/file）
    /// </summary>
    public string ErrorType { get; set; } = string.Empty;
    
    /// <summary>
    /// 客户端记录ID
    /// </summary>
    public string ClientRecordId { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否可重试
    /// </summary>
    public bool CanRetry { get; set; }
}

/// <summary>
/// 移动端更新数据DTO
/// </summary>
public class MobileUpdateDataDto
{
    /// <summary>
    /// 新的检查任务
    /// </summary>
    public List<MobileCheckTaskDto> NewCheckTasks { get; set; } = new();
    
    /// <summary>
    /// 更新的隐患模板
    /// </summary>
    public List<MobileDangerTemplateDto> UpdatedTemplates { get; set; } = new();
    
    /// <summary>
    /// 系统配置更新
    /// </summary>
    public Dictionary<string, object> ConfigUpdates { get; set; } = new();
    
    /// <summary>
    /// 最后同步时间
    /// </summary>
    public DateTime LastSyncTime { get; set; }
}