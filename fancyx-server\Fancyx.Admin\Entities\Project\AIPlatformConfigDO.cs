using Fancyx.Repository.BaseEntity;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Fancyx.Core.Interfaces;

namespace Fancyx.Admin.Entities.Project
{
    [Table("sys_ai_platform_config")]
    public class AIPlatformConfigDO : FullAuditedEntity, ITenant
    {
        /// <summary>
        /// 平台名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string PlatformName { get; set; }

        /// <summary>
        /// API地址
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string ApiUrl { get; set; }

        /// <summary>
        /// API Key
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string ApiKey { get; set; }

        /// <summary>
        /// 模型名称
        /// </summary>
        [MaxLength(100)]
        public string ModelName { get; set; }

        /// <summary>
        /// 请求超时时间（秒）
        /// </summary>
        public int Timeout { get; set; } = 30;

        /// <summary>
        /// 状态（0启用，1停用）
        /// </summary>
        public int Status { get; set; } = 0;

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(1000)]
        public string Remarks { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        public string? TenantId { get; set; }
    }
}