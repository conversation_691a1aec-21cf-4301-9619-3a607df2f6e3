-- 项目菜单配置SQL脚本
-- 注意：请根据你的实际数据库表名和字段名调整
-- 这里假设表名为 Menu，请根据实际情况修改

-- 方法1：使用临时变量（SQL Server语法）
-- 1. 项目管理文件夹（如果不存在）
DECLARE @ProjectFolderId UNIQUEIDENTIFIER = NEWID();

INSERT INTO Menu (
    Id, Title, Icon, Path, MenuType, Permission, 
    ParentId, Sort, Display, Component, CreationTime
) VALUES (
    @ProjectFolderId,
    '项目管理', 
    'project', 
    '/project', 
    1, -- Folder类型
    NULL, 
    NULL, -- 顶级菜单
    10, 
    1, -- 显示
    NULL, 
    GETDATE()
);

-- 2. 项目列表菜单
INSERT INTO Menu (
    Id, Title, Icon, Path, MenuType, Permission, 
    ParentId, Sort, Display, Component, CreationTime
) VALUES (
    NEWID(),
    '项目列表', 
    'list', 
    '/project/list', 
    2, -- Menu类型
    'Project.List', 
    @ProjectFolderId, 
    1, 
    1, 
    'pages/project/list', -- 对应前端组件路径
    GETDATE()
);

-- 3. 项目新增/编辑菜单（重要！解决404问题）
INSERT INTO Menu (
    Id, Title, Icon, Path, MenuType, Permission, 
    ParentId, Sort, Display, Component, CreationTime
) VALUES (
    NEWID(),
    '项目编辑', 
    'edit', 
    '/project/edit', 
    2, -- Menu类型
    'Project.Edit', 
    @ProjectFolderId, 
    2, 
    0, -- 不在菜单中显示，但需要路由
    'pages/project/edit', -- 对应前端组件路径
    GETDATE()
);

-- 4. 项目详情菜单
INSERT INTO Menu (
    Id, Title, Icon, Path, MenuType, Permission, 
    ParentId, Sort, Display, Component, CreationTime
) VALUES (
    NEWID(),
    '项目详情', 
    'detail', 
    '/project/detail/:id', 
    2, -- Menu类型
    'Project.Detail', 
    @ProjectFolderId, 
    3, 
    0, -- 不在菜单中显示，但需要路由
    'pages/project/detail', -- 对应前端组件路径
    GETDATE()
);

-- 5. 项目相关按钮权限
INSERT INTO Menu (
    Id, Title, Icon, Path, MenuType, Permission, 
    ParentId, Sort, Display, Component, CreationTime
) VALUES 
(NEWID(), '新增项目', NULL, NULL, 3, 'Project.Add', @ProjectFolderId, 1, 1, NULL, GETDATE()),
(NEWID(), '编辑项目', NULL, NULL, 3, 'Project.Update', @ProjectFolderId, 2, 1, NULL, GETDATE()),
(NEWID(), '删除项目', NULL, NULL, 3, 'Project.Delete', @ProjectFolderId, 3, 1, NULL, GETDATE()),
(NEWID(), '查看项目', NULL, NULL, 3, 'Project.View', @ProjectFolderId, 4, 1, NULL, GETDATE());

-- 方法2：如果使用PostgreSQL，可以使用以下语法
/*
-- 1. 项目管理文件夹
INSERT INTO menu (
    id, title, icon, path, menu_type, permission, 
    parent_id, sort, display, component, creation_time
) VALUES (
    gen_random_uuid(),
    '项目管理', 
    'project', 
    '/project', 
    1,
    NULL, 
    NULL,
    10, 
    true,
    NULL, 
    NOW()
) RETURNING id;

-- 后续插入需要使用上面返回的id作为parent_id
*/
