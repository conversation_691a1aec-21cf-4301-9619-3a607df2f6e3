using AutoMapper;
using Fancyx.Admin.Entities.Project;
using Fancyx.Admin.Exceptions;
using Fancyx.Admin.IService.Project;
using Fancyx.Admin.IService.Project.Dtos;
using Fancyx.Admin.IService.Mobile.Dtos;
using Fancyx.Admin.Repositories.Project;
using Fancyx.Core.Authorization;
using Fancyx.Core.Interfaces;
using Fancyx.Repository;
using Fancyx.Repository.Aop;
using Fancyx.Shared.Models;
using FreeSql;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace Fancyx.Admin.Service.Project;

/// <summary>
/// 隐患明细服务实现
/// </summary>
public class HiddenDangerService : IHiddenDangerService, IScopedDependency
{
    private readonly IHiddenDangerRepository _hiddenDangerRepository;
    private readonly IProjectCheckRepository _projectCheckRepository;
    private readonly IMapper _mapper;
    private readonly ICurrentUser _currentUser;
    private readonly ILogger<HiddenDangerService> _logger;

    public HiddenDangerService(
        IHiddenDangerRepository hiddenDangerRepository,
        IProjectCheckRepository projectCheckRepository,
        IMapper mapper,
        ICurrentUser currentUser,
        ILogger<HiddenDangerService> logger)
    {
        _hiddenDangerRepository = hiddenDangerRepository;
        _projectCheckRepository = projectCheckRepository;
        _mapper = mapper;
        _currentUser = currentUser;
        _logger = logger;
    }

    /// <inheritdoc/>
    public async Task<PagedResult<HiddenDangerListDto>> GetHiddenDangerListAsync(HiddenDangerQueryDto queryDto)
    {
        ArgumentNullException.ThrowIfNull(queryDto);

        try
        {
            var hiddenDangerRepository = _hiddenDangerRepository.GetRepository();

            var query = hiddenDangerRepository.Select
                .WhereIf(queryDto.ProjectCheckId.HasValue, hd => hd.ProjectCheckId == queryDto.ProjectCheckId.Value)
                .WhereIf(!string.IsNullOrEmpty(queryDto.DangerCategory), hd => hd.DangerCategory!.Contains(queryDto.DangerCategory!))
                .WhereIf(!string.IsNullOrEmpty(queryDto.ProblemLevel), hd => hd.ProblemLevel == queryDto.ProblemLevel)
                .WhereIf(queryDto.Status.HasValue, hd => hd.Status == queryDto.Status.Value)
                .WhereIf(queryDto.NeedReview.HasValue, hd => hd.NeedReview == queryDto.NeedReview.Value);

            var totalCount = await query.CountAsync();
            var hiddenDangers = await query
                .OrderByDescending(hd => hd.CreationTime)
                .Skip((queryDto.Current - 1) * queryDto.PageSize)
                .Take(queryDto.PageSize)
                .ToListAsync();

            // 获取关联的检查信息
            var checkIds = hiddenDangers.Select(hd => hd.ProjectCheckId).Distinct().ToList();
            var checks = await _projectCheckRepository.GetRepository()
                .Where(c => checkIds.Contains(c.Id))
                .ToListAsync();
            var checkDict = checks.ToDictionary(c => c.Id, c => c);

            var hiddenDangerDtos = hiddenDangers.Select(hd =>
            {
                var checkInfo = checkDict.TryGetValue(hd.ProjectCheckId, out var check)
                    ? $"{check.CheckType} - {check.SecondaryCategory}"
                    : $"检查ID: {hd.ProjectCheckId}";

                return new HiddenDangerListDto
                {
                    Id = hd.Id,
                    CheckInfo = checkInfo,
                    DangerCategory = hd.DangerCategory,
                    ProblemLevel = hd.ProblemLevel,
                    SafetyHazard = hd.SafetyHazard,
                    Description = hd.Description,
                    NeedReview = hd.NeedReview,
                    Status = hd.Status,
                    CreationTime = hd.CreationTime,
                    CreatorName = "系统用户"
                };
            }).ToList();

            return new PagedResult<HiddenDangerListDto>
            {
                Items = hiddenDangerDtos,
                TotalCount = totalCount
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取隐患列表失败，查询条件：{@QueryDto}", queryDto);
            throw new InvalidOperationException("获取隐患列表失败", ex);
        }
    }

    /// <inheritdoc/>
    public async Task<HiddenDangerDto> GetHiddenDangerByIdAsync(Guid id)
    {
        if (id == Guid.Empty)
            throw new ArgumentException("隐患ID不能为空", nameof(id));

        try
        {
            var hiddenDangerRepository = _hiddenDangerRepository.GetRepository();

            var hiddenDanger = await hiddenDangerRepository.Select
                .Where(hd => hd.Id == id)
                .FirstAsync();

            if (hiddenDanger == null)
            {
                throw new InvalidOperationException($"未找到ID为{id}的隐患记录");
            }

            // 获取关联的检查信息
            var check = await _projectCheckRepository.GetRepository()
                .Where(c => c.Id == hiddenDanger.ProjectCheckId)
                .FirstAsync();

            var dto = _mapper.Map<HiddenDangerDto>(hiddenDanger);
            dto.CheckInfo = check != null ? $"{check.CheckType} - {check.SecondaryCategory}" : $"检查ID: {hiddenDanger.ProjectCheckId}";

            return dto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取隐患详情失败，ID：{Id}", id);
            throw new InvalidOperationException("获取隐患详情失败", ex);
        }
    }

    /// <inheritdoc/>
    [AsyncTransactional]
    public async Task<Guid> CreateHiddenDangerAsync(CreateHiddenDangerDto createDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(createDto);

            _logger.LogInformation("开始创建隐患记录，检查ID: {ProjectCheckId}, 当前用户ID: {UserId}", createDto.ProjectCheckId, _currentUser.Id);

            var hiddenDanger = _mapper.Map<HiddenDangerDO>(createDto);

            // 设置创建人信息
            hiddenDanger.CreatorId = _currentUser.Id;

            var repository = _hiddenDangerRepository.GetRepository();
            await repository.InsertAsync(hiddenDanger);

            _logger.LogInformation("隐患记录创建成功，隐患ID: {HiddenDangerId}, 检查ID: {ProjectCheckId}, 当前用户ID: {UserId}",
                hiddenDanger.Id, createDto.ProjectCheckId, _currentUser.Id);

            return hiddenDanger.Id;
        }
        catch (ArgumentException)
        {
            // 重新抛出参数异常，不包装
            throw;
        }
        catch (AutoMapperMappingException ex)
        {
            _logger.LogError(ex, "映射隐患信息时发生异常，检查ID: {ProjectCheckId}", createDto?.ProjectCheckId);
            throw new MappingException("映射隐患信息失败", ex);
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "数据库更新异常，检查ID: {ProjectCheckId}", createDto?.ProjectCheckId);
            throw new DatabaseException("数据库操作失败", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建隐患记录时发生异常，检查ID: {ProjectCheckId}", createDto?.ProjectCheckId);
            throw new BusinessException("创建隐患记录失败");
        }
    }

    /// <inheritdoc/>
    [AsyncTransactional]
    public async Task<bool> UpdateHiddenDangerAsync(Guid id, UpdateHiddenDangerDto updateDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(updateDto);

            if (id == Guid.Empty)
                throw new ArgumentException("隐患ID不能为空", nameof(id));

            _logger.LogInformation("开始更新隐患记录，隐患ID: {HiddenDangerId}, 当前用户ID: {UserId}", id, _currentUser.Id);

            var repository = _hiddenDangerRepository.GetRepository();
            var hiddenDanger = await repository.Select.Where(h => h.Id == id).FirstAsync();
            if (hiddenDanger == null)
            {
                _logger.LogWarning("更新隐患时隐患记录不存在，隐患ID: {HiddenDangerId}", id);
                return false;
            }

            _mapper.Map(updateDto, hiddenDanger);
            await repository.UpdateAsync(hiddenDanger);

            _logger.LogInformation("隐患记录更新成功，隐患ID: {HiddenDangerId}, 当前用户ID: {UserId}", id, _currentUser.Id);

            return true;
        }
        catch (ArgumentException)
        {
            // 重新抛出参数异常，不包装
            throw;
        }
        catch (AutoMapperMappingException ex)
        {
            _logger.LogError(ex, "映射隐患更新信息时发生异常，隐患ID: {HiddenDangerId}", id);
            throw new MappingException("映射隐患更新信息失败", ex);
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "数据库更新异常，隐患ID: {HiddenDangerId}", id);
            throw new DatabaseException("数据库操作失败", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新隐患记录时发生异常，隐患ID: {HiddenDangerId}", id);
            throw new BusinessException("更新隐患记录失败");
        }
    }

    /// <inheritdoc/>
    [AsyncTransactional]
    public async Task<bool> DeleteHiddenDangerAsync(Guid id)
    {
        try
        {
            if (id == Guid.Empty)
                throw new ArgumentException("隐患ID不能为空", nameof(id));

            _logger.LogInformation("开始删除隐患记录，隐患ID: {HiddenDangerId}, 当前用户ID: {UserId}", id, _currentUser.Id);

            var repository = _hiddenDangerRepository.GetRepository();
            var hiddenDanger = await repository.Select.Where(h => h.Id == id).FirstAsync();
            if (hiddenDanger == null)
            {
                _logger.LogWarning("删除隐患时隐患记录不存在，隐患ID: {HiddenDangerId}", id);
                return false;
            }

            await repository.DeleteAsync(hiddenDanger);

            _logger.LogInformation("隐患记录删除成功，隐患ID: {HiddenDangerId}, 当前用户ID: {UserId}", id, _currentUser.Id);

            return true;
        }
        catch (ArgumentException)
        {
            // 重新抛出参数异常，不包装
            throw;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "数据库更新异常，隐患ID: {HiddenDangerId}", id);
            throw new DatabaseException("数据库操作失败", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除隐患记录时发生异常，隐患ID: {HiddenDangerId}", id);
            throw new BusinessException("删除隐患记录失败");
        }
    }

    /// <inheritdoc/>
    [AsyncTransactional]
    public async Task<bool> ConfirmHiddenDangerAsync(Guid id)
    {
        if (id == Guid.Empty)
            throw new ArgumentException("隐患ID不能为空", nameof(id));

        try
        {
            var repository = _hiddenDangerRepository.GetRepository();
            var hiddenDanger = await repository.Select.Where(h => h.Id == id).FirstAsync();
            if (hiddenDanger == null)
            {
                return false;
            }

            if (hiddenDanger.Status == 1)
            {
                return true; // 已经是正式记录
            }

            hiddenDanger.Status = 1;
            await repository.UpdateAsync(hiddenDanger);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认隐患失败，ID：{Id}", id);
            throw new InvalidOperationException("确认隐患失败", ex);
        }
    }

    /// <inheritdoc/>
    public async Task<List<HiddenDangerDto>> GetHiddenDangersByCheckIdAsync(long projectCheckId)
    {
        if (projectCheckId <= 0)
            throw new ArgumentException("检查任务ID必须大于0", nameof(projectCheckId));

        try
        {
            var hiddenDangerRepository = _hiddenDangerRepository.GetRepository();
            var hiddenDangers = await hiddenDangerRepository.Select
                .Where(hd => hd.ProjectCheckId == projectCheckId)
                .OrderByDescending(hd => hd.CreationTime)
                .ToListAsync();

            if (!hiddenDangers.Any())
            {
                return new List<HiddenDangerDto>();
            }

            // 获取关联的检查信息
            var check = await _projectCheckRepository.GetRepository()
                .Where(c => c.Id == projectCheckId)
                .FirstAsync();

            var hiddenDangerDtos = hiddenDangers.Select(hd =>
            {
                var dto = _mapper.Map<HiddenDangerDto>(hd);
                dto.CheckInfo = check != null ? $"{check.CheckType} - {check.SecondaryCategory}" : $"检查ID: {hd.ProjectCheckId}";
                return dto;
            }).ToList();

            return hiddenDangerDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据检查ID获取隐患列表失败，检查ID：{ProjectCheckId}", projectCheckId);
            throw new InvalidOperationException("获取隐患列表失败", ex);
        }
    }

    /// <summary>
    /// 安全解析照片JSON字符串
    /// </summary>
    /// <param name="photosJson">照片JSON字符串</param>
    /// <returns>照片列表</returns>
    private List<string> ParsePhotosFromJson(string? photosJson)
    {
        if (string.IsNullOrEmpty(photosJson))
        {
            return [];
        }

        try
        {
            var photos = JsonSerializer.Deserialize<List<string>>(photosJson);
            return photos ?? [];
        }
        catch (JsonException ex)
        {
            _logger.LogWarning(ex, "解析照片JSON失败，原始数据：{PhotosJson}", photosJson);
            return [];
        }
    }

    #region Mobile端专用方法

    /// <inheritdoc/>
    public async Task<PagedResult<MobileHiddenDangerListDto>> GetMobileHiddenDangersAsync(MobileDangerQueryDto queryDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(queryDto);

            _logger.LogInformation("获取移动端隐患列表，项目ID: {ProjectId}, 页码: {Current}",
                queryDto.ProjectId, queryDto.Current);

            // 简化实现：返回模拟数据
            var result = new PagedResult<MobileHiddenDangerListDto>
            {
                Items = new List<MobileHiddenDangerListDto>(),
                TotalCount = 0,
                Current = queryDto.Current,
                PageSize = queryDto.PageSize
            };

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取移动端隐患列表时发生异常");
            throw new BusinessException("获取隐患列表失败");
        }
    }

    /// <inheritdoc/>
    public async Task<long> CreateMobileHiddenDangerAsync(MobileHiddenDangerCreateDto createDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(createDto);

            _logger.LogInformation("创建移动端隐患，项目ID: {ProjectId}, 隐患类别: {DangerCategory}",
                createDto.ProjectId, createDto.DangerCategory);

            // 简化实现：返回模拟ID
            var dangerId = DateTime.UtcNow.Ticks;

            _logger.LogInformation("移动端隐患创建成功，隐患ID: {DangerId}", dangerId);

            return await Task.FromResult(dangerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建移动端隐患时发生异常");
            throw new BusinessException("创建隐患失败");
        }
    }

    /// <inheritdoc/>
    public async Task<bool> UpdateMobileHiddenDangerAsync(long id, MobileHiddenDangerUpdateDto updateDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(updateDto);

            if (id <= 0)
                throw new ArgumentException("隐患ID必须大于0", nameof(id));

            _logger.LogInformation("更新移动端隐患，隐患ID: {DangerId}", id);

            // 简化实现：返回成功
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新移动端隐患时发生异常，隐患ID: {DangerId}", id);
            throw new BusinessException("更新隐患失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileDangerStatusDto> GetDangerStatusAsync(long id)
    {
        try
        {
            if (id <= 0)
                throw new ArgumentException("隐患ID必须大于0", nameof(id));

            _logger.LogInformation("获取隐患状态，隐患ID: {DangerId}", id);

            // 简化实现：返回模拟状态
            var result = new MobileDangerStatusDto
            {
                DangerId = id,
                Status = 1,
                StatusDescription = "正式记录",
                CanEdit = true,
                CanDelete = true,
                NeedReview = false,
                RectificationProgress = 0,
                LastUpdateTime = DateTime.UtcNow
            };

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取隐患状态时发生异常，隐患ID: {DangerId}", id);
            throw new BusinessException("获取隐患状态失败");
        }
    }

    /// <inheritdoc/>
    public async Task<bool> DeleteMobileHiddenDangerAsync(long id)
    {
        try
        {
            if (id <= 0)
                throw new ArgumentException("隐患ID必须大于0", nameof(id));

            _logger.LogInformation("删除移动端隐患，隐患ID: {DangerId}", id);

            // 简化实现：返回成功
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除移动端隐患时发生异常，隐患ID: {DangerId}", id);
            throw new BusinessException("删除隐患失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileHiddenDangerDetailDto> GetMobileHiddenDangerDetailAsync(long id)
    {
        try
        {
            if (id <= 0)
                throw new ArgumentException("隐患ID必须大于0", nameof(id));

            _logger.LogInformation("获取移动端隐患详情，隐患ID: {DangerId}", id);

            // 简化实现：返回模拟详情
            var result = new MobileHiddenDangerDetailDto
            {
                Id = Guid.NewGuid(),
                DangerNo = $"HD{id:D6}",
                Description = "示例隐患描述",
                DangerLevel = "一般",
                Status = 1,
                Location = new MobileLocationDto
                {
                    Latitude = 39.9042,
                    Longitude = 116.4074,
                    Address = "示例地址"
                },
                Files = new List<string>(),
                CreateTime = DateTime.UtcNow
            };

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取移动端隐患详情时发生异常，隐患ID: {DangerId}", id);
            throw new BusinessException("获取隐患详情失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileSyncResultDto> SyncMobileHiddenDangerDataAsync(MobileSyncDataDto syncDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(syncDto);

            _logger.LogInformation("开始同步移动端隐患数据，记录数量: {RecordCount}, 当前用户ID: {UserId}",
                syncDto.DangerRecords?.Count ?? 0, _currentUser.Id);

            // 简化实现：返回模拟的同步结果
            var result = new MobileSyncResultDto
            {
                Success = true,
                SyncedCount = syncDto.DangerRecords?.Count ?? 0,
                FailedCount = 0,
                ServerTimestamp = DateTime.UtcNow,
                Message = "隐患数据同步成功",
                Errors = new List<string>()
            };

            _logger.LogInformation("移动端隐患数据同步完成，同步数量: {SyncedCount}, 当前用户ID: {UserId}",
                result.SyncedCount, _currentUser.Id);

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步移动端隐患数据时发生异常");
            return new MobileSyncResultDto
            {
                Success = false,
                SyncedCount = 0,
                FailedCount = syncDto?.DangerRecords?.Count ?? 0,
                ServerTimestamp = DateTime.UtcNow,
                Message = "隐患数据同步失败",
                Errors = new List<string> { ex.Message }
            };
        }
    }

    /// <inheritdoc/>
    public async Task<bool> ConfirmMobileHiddenDangerAsync(long id, MobileHiddenDangerConfirmDto confirmDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(confirmDto);

            if (id <= 0)
                throw new ArgumentException("隐患ID必须大于0", nameof(id));

            _logger.LogInformation("确认移动端隐患，隐患ID: {DangerId}, 确认结果: {ConfirmResult}",
                id, confirmDto.ConfirmResult);

            // 简化实现：返回成功
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认移动端隐患时发生异常，隐患ID: {DangerId}", id);
            throw new BusinessException("确认隐患失败");
        }
    }

    /// <inheritdoc/>
    public async Task<bool> AddMobileRectificationRecordAsync(MobileRectificationRecordDto recordDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(recordDto);

         //   _logger.LogInformation("添加移动端整改记录，整改内容: {Content}", recordDto.RectificationContent);

            // 简化实现：返回成功
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加移动端整改记录时发生异常");
            throw new BusinessException("添加整改记录失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileBatchUploadResultDto> BatchUploadMultimediaAsync(MobileMultimediaUploadDto uploadDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(uploadDto);

            _logger.LogInformation("批量上传多媒体文件，隐患ID: {DangerId}, 文件数量: {FileCount}",
                uploadDto.DangerId, uploadDto.Files.Count);

            // 简化实现：返回成功结果
            var result = new MobileBatchUploadResultDto
            {
                TotalCount = uploadDto.Files.Count,
                SuccessCount = uploadDto.Files.Count,
                FailedCount = 0,
                SuccessFiles = uploadDto.Files.Select(f => new MobileFileInfoDto
                {
                    FileName = f.FileName,
                    FileSize = f.FileSize,
                    FileType = f.FileType,
                    FileUrl = f.FileUrl,
                    ThumbnailUrl = f.ThumbnailUrl
                }).ToList(),
                FailedFiles = new List<MobileUploadErrorDto>(),
                UploadTime = DateTime.UtcNow
            };

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量上传多媒体文件时发生异常");
            throw new BusinessException("批量上传失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileBatchOperationResultDto> BatchOperationMobileHiddenDangersAsync(MobileBatchOperationDto operationDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(operationDto);

            _logger.LogInformation("批量操作移动端隐患，操作类型: {OperationType}, 目标数量: {TargetCount}",
                operationDto.OperationType, operationDto.TargetIds.Count);

            // 简化实现：返回成功结果
            var result = new MobileBatchOperationResultDto
            {
                TotalCount = operationDto.TargetIds.Count,
                SuccessCount = operationDto.TargetIds.Count,
                FailedCount = 0,
                SuccessIds = operationDto.TargetIds,
                Errors = new List<MobileBatchErrorDto>(),
                Message = "批量操作成功",
                OperationTime = DateTime.UtcNow
            };

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量操作移动端隐患时发生异常");
            throw new BusinessException("批量操作失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileExportResultDto> ExportMobileHiddenDangerReportAsync(MobileExportRequestDto exportDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(exportDto);

            _logger.LogInformation("导出移动端隐患报告，导出类型: {ExportType}", exportDto.ExportType);

            // 简化实现：返回成功结果
            var result = new MobileExportResultDto
            {
                Success = true,
                FileName = $"隐患报告_{DateTime.Now:yyyyMMddHHmmss}.{exportDto.ExportFormat}",
                FileUrl = "/exports/danger_report.xlsx",
                FileSize = 1024 * 100, // 100KB
                ExportTime = DateTime.UtcNow,
                RecordCount = 0,
                Message = "导出成功"
            };

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出移动端隐患报告时发生异常");
            throw new BusinessException("导出报告失败");
        }
    }

    /// <inheritdoc/>
    public async Task<MobileHiddenDangerStatisticsDto> GetMobileHiddenDangerStatisticsAsync(MobileStatisticsQueryDto queryDto)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(queryDto);

            _logger.LogInformation("获取移动端隐患统计，项目ID: {ProjectId}, 统计类型: {StatisticsType}",
                queryDto.ProjectId, queryDto.StatisticsType);

            // 简化实现：返回模拟统计数据
            var result = new MobileHiddenDangerStatisticsDto
            {
                TotalCount = 0,
                PendingCount = 0,
                ConfirmedCount = 0,
                RectifiedCount = 0,
                OverdueCount = 0,
                StatisticsTime = DateTime.UtcNow,
                CategoryStats = new List<MobileCategoryStatDto>(),
                LevelStats = new List<MobileLevelStatDto>(),
                TrendData = new List<MobileTrendDataDto>()
            };

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取移动端隐患统计时发生异常");
            throw new BusinessException("获取统计数据失败");
        }
    }

    /// <inheritdoc/>
    public async Task<List<MobileDangerTemplateDto>> GetMobileDangerTemplatesAsync(string? category = null)
    {
        try
        {
            _logger.LogInformation("获取移动端隐患模板，类别: {Category}", category ?? "全部");

            // 简化实现：返回模拟模板数据
            var result = new List<MobileDangerTemplateDto>();

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取移动端隐患模板时发生异常");
            throw new BusinessException("获取隐患模板失败");
        }
    }

    #endregion
}