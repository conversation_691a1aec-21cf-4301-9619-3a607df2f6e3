// @ts-nocheck
import React, { useRef } from 'react';
import { Button, Modal, message, Form, Input, Select } from 'antd';
import SmartTable from '@/components/SmartTable';
import type { SmartTableRef } from '@/components/SmartTable/type';
import { getProjectList, deleteProject } from '@/api/project';
import { useNavigate } from 'react-router-dom';
import UserStore from '@/store/userStore';

const { Option } = Select;

const ProjectList: React.FC = () => {
  const tableRef = useRef<SmartTableRef>(null);
  const navigate = useNavigate();

  // 删除项目
  const handleDelete = (id: number) => {
    Modal.confirm({
      title: '确认删除该项目？',
      onOk: async () => {
        await deleteProject(id);
        message.success('删除成功');
        tableRef.current?.reload();
      },
    });
  };

  // 创建检查任务
  const handleCreateCheck = (record: any) => {
    navigate(`/project-check/edit?projectId=${record.id}&projectName=${encodeURIComponent(record.projectName)}`);
  };

  // 表格列定义
  const columns = [
    { title: '项目编号', dataIndex: 'projectNo', key: 'projectNo' },
    { title: '项目名称', dataIndex: 'projectName', key: 'projectName' },
    { title: '项目地址', dataIndex: 'projectAddress', key: 'projectAddress' },
    { title: '所属区域', dataIndex: 'region', key: 'region' },
    { title: '项目类型', dataIndex: 'projectType', key: 'projectType' },
    { title: '负责人', dataIndex: 'siteManager', key: 'siteManager' },
    { title: '负责人电话', dataIndex: 'managerPhone', key: 'managerPhone' },
    { title: '开工日期', dataIndex: 'startDate', key: 'startDate' },
    { title: '竣工日期', dataIndex: 'endDate', key: 'endDate' },
    { title: '创建时间', dataIndex: 'creationTime', key: 'creationTime' },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <>
          <Button type="link" onClick={() => navigate(`/project/detail/${record.id}`)}>详情</Button>
          <Button type="link" onClick={() => navigate(`/project/edit/${record.id}`)}>编辑</Button>
          <Button type="link" onClick={() => handleCreateCheck(record)}>创建检查任务</Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>删除</Button>
        </>
      ),
    },
  ];

  // 上海16区选项
  const SHANGHAI_DISTRICTS = [
    '黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区',
    '浦东新区', '闵行区', '宝山区', '嘉定区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'
  ];

  // 查询项（可根据实际字段扩展）
  const searchItems: React.ReactNode[] = [
    <Form.Item name="projectName" label="项目名称" key="projectName">
      <Input placeholder="请输入项目名称" />
    </Form.Item>,
    <Form.Item name="projectNo" label="项目编号" key="projectNo">
      <Input placeholder="请输入项目编号" />
    </Form.Item>,
    <Form.Item name="region" label="所属区域" key="region">
      <Select placeholder="请选择区域" allowClear showSearch>
        {SHANGHAI_DISTRICTS.map(district => (
          <Option key={district} value={district}>{district}</Option>
        ))}
      </Select>
    </Form.Item>,
  ];

  // 包装API调用，添加必需的Region参数
  const wrappedGetProjectList = async (params: any) => {
    // 添加Region参数 - 如果没有指定区域，传递空字符串让后端返回所有区域的数据
    // 或者根据业务需求设置默认区域
    const requestParams = {
      ...params,
      Region: params.region || '' // 从搜索条件中获取区域，如果没有则为空
    };
    
    console.log('项目列表API请求参数:', requestParams);
    
    return getProjectList(requestParams);
  };

  // 工具栏
  const toolbar: React.ReactNode[] = [
    <Button type="primary" key="add" onClick={() => navigate('/project/edit')}>新增项目</Button>,
  ];

  return (
    <SmartTable
      ref={tableRef}
      columns={columns}
      request={wrappedGetProjectList}
      searchItems={searchItems}
      toolbar={toolbar}
      rowKey="id"
    />
  );
};

export default ProjectList; 