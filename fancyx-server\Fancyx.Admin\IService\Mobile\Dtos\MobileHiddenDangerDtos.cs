using Microsoft.AspNetCore.Http;

using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端隐患查询DTO
/// </summary>
public class MobileDangerQueryDto : PageSearch
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long? ProjectId { get; set; }
    
    /// <summary>
    /// 检查任务ID
    /// </summary>
    public long? CheckId { get; set; }
    
    /// <summary>
    /// 隐患大类
    /// </summary>
    public string? DangerCategory { get; set; }
    
    /// <summary>
    /// 问题等级
    /// </summary>
    public string? ProblemLevel { get; set; }
    
    /// <summary>
    /// 状态（待确认/正式记录）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 是否需要复查
    /// </summary>
    public bool? NeedReview { get; set; }
    
    /// <summary>
    /// 创建日期范围开始
    /// </summary>
    public DateTime? StartDate { get; set; }
    
    /// <summary>
    /// 创建日期范围结束
    /// </summary>
    public DateTime? EndDate { get; set; }
}

/// <summary>
/// 移动端隐患列表DTO
/// </summary>
public class MobileDangerListDto
{
    /// <summary>
    /// 隐患ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;
    
    /// <summary>
    /// 检查信息
    /// </summary>
    public string CheckInfo { get; set; } = string.Empty;
    
    /// <summary>
    /// 隐患大类
    /// </summary>
    public string DangerCategory { get; set; } = string.Empty;
    
    /// <summary>
    /// 问题等级
    /// </summary>
    public string ProblemLevel { get; set; } = string.Empty;
    
    /// <summary>
    /// 安全隐患简述
    /// </summary>
    public string SafetyHazard { get; set; } = string.Empty;
    
    /// <summary>
    /// 具体描述（截取前50字）
    /// </summary>
    public string ShortDescription { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否需要复查
    /// </summary>
    public bool NeedReview { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }
    
    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusText { get; set; } = string.Empty;
    
    /// <summary>
    /// 第一张照片缩略图
    /// </summary>
    public string? ThumbnailUrl { get; set; }
    
    /// <summary>
    /// 照片数量
    /// </summary>
    public int PhotoCount { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreationTime { get; set; }
    
    /// <summary>
    /// 创建人
    /// </summary>
    public string CreatorName { get; set; } = string.Empty;
    
    /// <summary>
    /// 位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }
}

/// <summary>
/// 移动端AI识别请求DTO
/// </summary>
public class MobileAIRecognitionRequestDto
{
    /// <summary>
    /// 检查任务ID
    /// </summary>
    public long CheckId { get; set; }
    
    /// <summary>
    /// 现场照片（支持多张）
    /// </summary>
    public List<IFormFile> Photos { get; set; } = new();
    
    /// <summary>
    /// 语音描述文件
    /// </summary>
    public IFormFile? VoiceDescription { get; set; }
    
    /// <summary>
    /// 文字描述（语音转文字结果或直接输入）
    /// </summary>
    public string? TextDescription { get; set; }
    
    /// <summary>
    /// GPS位置信息
    /// </summary>
    public MobileLocationDto Location { get; set; } = new();
    
    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
    
    /// <summary>
    /// 拍照时间
    /// </summary>
    public DateTime PhotoTimestamp { get; set; }
    
    /// <summary>
    /// 环境信息（可选）
    /// </summary>
    public MobileEnvironmentInfoDto? EnvironmentInfo { get; set; }
}

/// <summary>
/// 移动端AI识别结果DTO
/// </summary>
public class MobileAIRecognitionResultDto
{
    /// <summary>
    /// 识别任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;
    
    /// <summary>
    /// 识别状态（processing/completed/failed）
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 识别进度（0-100）
    /// </summary>
    public int Progress { get; set; }
    
    /// <summary>
    /// 识别到的隐患列表
    /// </summary>
    public List<MobileAIRecognizedDangerDto> RecognizedDangers { get; set; } = new();
    
    /// <summary>
    /// 语音转文字结果
    /// </summary>
    public string? TranscribedText { get; set; }
    
    /// <summary>
    /// AI分析建议
    /// </summary>
    public string? AISuggestion { get; set; }
    
    /// <summary>
    /// 置信度评分（0-1）
    /// </summary>
    public double ConfidenceScore { get; set; }
    
    /// <summary>
    /// 处理耗时（毫秒）
    /// </summary>
    public long ProcessingTime { get; set; }
    
    /// <summary>
    /// 错误信息（如果识别失败）
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 移动端AI识别的隐患DTO
/// </summary>
public class MobileAIRecognizedDangerDto
{
    /// <summary>
    /// 临时ID（用于前端标识）
    /// </summary>
    public string TempId { get; set; } = string.Empty;
    
    /// <summary>
    /// 隐患大类
    /// </summary>
    public string DangerCategory { get; set; } = string.Empty;
    
    /// <summary>
    /// 推荐的问题等级
    /// </summary>
    public string SuggestedLevel { get; set; } = string.Empty;
    
    /// <summary>
    /// 安全隐患描述
    /// </summary>
    public string SafetyHazard { get; set; } = string.Empty;
    
    /// <summary>
    /// 具体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 建议的整改要求
    /// </summary>
    public string SuggestedRectification { get; set; } = string.Empty;
    
    /// <summary>
    /// 可能的后果
    /// </summary>
    public string PossibleConsequences { get; set; } = string.Empty;
    
    /// <summary>
    /// 相关标准规范
    /// </summary>
    public MobileStandardReferenceDto? StandardReference { get; set; }
    
    /// <summary>
    /// 识别置信度
    /// </summary>
    public double Confidence { get; set; }
    
    /// <summary>
    /// 关联的照片索引
    /// </summary>
    public List<int> RelatedPhotoIndexes { get; set; } = new();
    
    /// <summary>
    /// 照片中的标注区域
    /// </summary>
    public List<MobileBoundingBoxDto> BoundingBoxes { get; set; } = new();
}

/// <summary>
/// 移动端确认AI识别隐患DTO
/// </summary>
public class MobileConfirmAIDangerDto
{
    /// <summary>
    /// AI识别任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;
    
    /// <summary>
    /// 确认的隐患列表
    /// </summary>
    public List<MobileConfirmedDangerDto> ConfirmedDangers { get; set; } = new();
    
    /// <summary>
    /// 检查任务ID
    /// </summary>
    public long CheckId { get; set; }
    
    /// <summary>
    /// 用户修改的文字描述
    /// </summary>
    public string? UserModifiedDescription { get; set; }
    
    /// <summary>
    /// 位置信息
    /// </summary>
    public MobileLocationDto Location { get; set; } = new();
}

/// <summary>
/// 移动端确认的隐患DTO
/// </summary>
public class MobileConfirmedDangerDto
{
    /// <summary>
    /// AI识别的临时ID
    /// </summary>
    public string TempId { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户确认/修改的隐患大类
    /// </summary>
    public string DangerCategory { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户确认/修改的问题等级
    /// </summary>
    public string ProblemLevel { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户确认/修改的安全隐患
    /// </summary>
    public string SafetyHazard { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户确认/修改的具体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户确认/修改的整改要求
    /// </summary>
    public string RectificationRequirement { get; set; } = string.Empty;
    
    /// <summary>
    /// 可能产生后果
    /// </summary>
    public string? PossibleConsequences { get; set; }
    
    /// <summary>
    /// 整改建议
    /// </summary>
    public string? RectificationSuggestion { get; set; }
    
    /// <summary>
    /// 标准规范ID
    /// </summary>
    public long? StandardId { get; set; }
    
    /// <summary>
    /// 标准规范名称
    /// </summary>
    public string? StandardName { get; set; }
    
    /// <summary>
    /// 法规条款
    /// </summary>
    public string? RegulationClause { get; set; }
    
    /// <summary>
    /// 是否需要复查
    /// </summary>
    public bool NeedReview { get; set; }
    
    /// <summary>
    /// 关联的照片URL列表
    /// </summary>
    public List<string> PhotoUrls { get; set; } = new();
}


// MobileDeviceInfoDto 已移动到 MobileCommonDtos.cs

/// <summary>
/// 移动端环境信息DTO
/// </summary>
public class MobileEnvironmentInfoDto
{
    /// <summary>
    /// 光线条件（良好/一般/较暗）
    /// </summary>
    public string? LightCondition { get; set; }
    
    /// <summary>
    /// 天气情况
    /// </summary>
    public string? Weather { get; set; }
    
    /// <summary>
    /// 温度
    /// </summary>
    public double? Temperature { get; set; }
    
    /// <summary>
    /// 湿度
    /// </summary>
    public double? Humidity { get; set; }
}

/// <summary>
/// 移动端标注框DTO
/// </summary>
public class MobileBoundingBoxDto
{
    /// <summary>
    /// 左上角X坐标（相对于图片宽度的比例，0-1）
    /// </summary>
    public double X { get; set; }
    
    /// <summary>
    /// 左上角Y坐标（相对于图片高度的比例，0-1）
    /// </summary>
    public double Y { get; set; }
    
    /// <summary>
    /// 宽度（相对于图片宽度的比例，0-1）
    /// </summary>
    public double Width { get; set; }
    
    /// <summary>
    /// 高度（相对于图片高度的比例，0-1）
    /// </summary>
    public double Height { get; set; }
    
    /// <summary>
    /// 标注说明
    /// </summary>
    public string Label { get; set; } = string.Empty;
    
    /// <summary>
    /// 置信度
    /// </summary>
    public double Confidence { get; set; }
}

/// <summary>
/// 移动端标准规范引用DTO
/// </summary>
public class MobileStandardReferenceDto
{
    /// <summary>
    /// 标准ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 标准名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 标准编号
    /// </summary>
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// 相关条款
    /// </summary>
    public string Clause { get; set; } = string.Empty;
    
    /// <summary>
    /// 匹配置信度
    /// </summary>
    public double MatchConfidence { get; set; }
}

/// <summary>
/// 移动端隐患查询DTO
/// </summary>
public class MobileHiddenDangerQueryDto : PageSearch
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long? ProjectId { get; set; }

    /// <summary>
    /// 隐患状态
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 隐患等级
    /// </summary>
    public string? DangerLevel { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }
}

/// <summary>
/// 移动端隐患列表DTO
/// </summary>
public class MobileHiddenDangerListDto
{
    /// <summary>
    /// 隐患ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 隐患编号
    /// </summary>
    public string DangerNo { get; set; } = string.Empty;

    /// <summary>
    /// 隐患描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 隐患等级
    /// </summary>
    public string DangerLevel { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;
}

/// <summary>
/// 移动端隐患详情DTO
/// </summary>
public class MobileHiddenDangerDetailDto
{
    /// <summary>
    /// 隐患ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 隐患编号
    /// </summary>
    public string DangerNo { get; set; } = string.Empty;

    /// <summary>
    /// 隐患描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 隐患等级
    /// </summary>
    public string DangerLevel { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 相关文件
    /// </summary>
    public List<string> Files { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
}

/// <summary>
/// 移动端隐患统计DTO
/// </summary>
public class MobileHiddenDangerStatisticsDto
{
    /// <summary>
    /// 总数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 待确认数量
    /// </summary>
    public int PendingCount { get; set; }

    /// <summary>
    /// 已确认数量
    /// </summary>
    public int ConfirmedCount { get; set; }

    /// <summary>
    /// 已整改数量
    /// </summary>
    public int ResolvedCount { get; set; }

    /// <summary>
    /// 已整改数量（兼容性属性）
    /// </summary>
    public int RectifiedCount { get; set; }

    /// <summary>
    /// 逾期数量
    /// </summary>
    public int OverdueCount { get; set; }

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 分类统计
    /// </summary>
    public List<MobileCategoryStatDto> CategoryStats { get; set; } = new();

    /// <summary>
    /// 等级统计
    /// </summary>
    public List<MobileLevelStatDto> LevelStats { get; set; } = new();

    /// <summary>
    /// 趋势数据
    /// </summary>
    public List<MobileTrendDataDto> TrendData { get; set; } = new();
}

/// <summary>
/// 移动端分类统计DTO
/// </summary>
public class MobileCategoryStatDto
{
    /// <summary>
    /// 分类名称
    /// </summary>
    public string CategoryName { get; set; } = string.Empty;

    /// <summary>
    /// 数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 百分比
    /// </summary>
    public decimal Percentage { get; set; }
}

/// <summary>
/// 移动端等级统计DTO
/// </summary>
public class MobileLevelStatDto
{
    /// <summary>
    /// 等级名称
    /// </summary>
    public string LevelName { get; set; } = string.Empty;

    /// <summary>
    /// 数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 百分比
    /// </summary>
    public decimal Percentage { get; set; }
}

/// <summary>
/// 移动端趋势数据DTO
/// </summary>
public class MobileTrendDataDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public string Type { get; set; } = string.Empty;
}