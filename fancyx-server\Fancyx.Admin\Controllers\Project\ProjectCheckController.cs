using Fancyx.Admin.IService.Project;
using Fancyx.Admin.IService.Project.Dtos;
using Fancyx.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;

namespace Fancyx.Admin.Controllers.Project
{
    /// <summary>
    /// 项目检查管理控制器
    /// </summary>
    [Route("api/project-check")]
    [Authorize]
    [ApiController]
    public class ProjectCheckController : ControllerBase
    {
        private readonly IProjectCheckService _projectCheckService;

        public ProjectCheckController(IProjectCheckService projectCheckService)
        {
            _projectCheckService = projectCheckService;
        }

        /// <summary>
        /// 获取项目检查列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>检查列表</returns>
        [HttpGet]
        [Route("list")]
        public async Task<AppResponse<PagedResult<ProjectCheckListDto>>> GetProjectCheckList([FromQuery] ProjectCheckQueryDto queryDto)
        {
            var result = await _projectCheckService.GetProjectCheckListAsync(queryDto);
            return Result.Data(result);
        }

        /// <summary>
        /// 根据ID获取项目检查详情
        /// </summary>
        /// <param name="id">检查ID</param>
        /// <returns>检查详情</returns>
        [HttpGet]
        [Route("{id}")]
        public async Task<AppResponse<ProjectCheckDto>> GetProjectCheckById(long id)
        {
            var check = await _projectCheckService.GetProjectCheckByIdAsync(id);
            return Result.Data(check);
        }

        /// <summary>
        /// 创建项目检查
        /// </summary>
        /// <param name="createDto">检查信息</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<AppResponse<long>> CreateProjectCheck([FromBody] CreateProjectCheckDto createDto)
        {
            var checkId = await _projectCheckService.CreateProjectCheckAsync(createDto);
            return Result.Data(checkId);
        }

        /// <summary>
        /// 更新项目检查
        /// </summary>
        /// <param name="id">检查ID</param>
        /// <param name="updateDto">更新信息</param>
        /// <returns>更新结果</returns>
        [HttpPut]
        [Route("{id}")]
        public async Task<AppResponse<bool>> UpdateProjectCheck(long id, [FromBody] UpdateProjectCheckDto updateDto)
        {
            var result = await _projectCheckService.UpdateProjectCheckAsync(id, updateDto);
            return result ? Result.Ok() : Result.Fail();
        }

        /// <summary>
        /// 删除项目检查
        /// </summary>
        /// <param name="id">检查ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete]
        [Route("{id}")]
        public async Task<AppResponse<bool>> DeleteProjectCheck(long id)
        {
            var result = await _projectCheckService.DeleteProjectCheckAsync(id);
            return result ? Result.Ok() : Result.Fail();
        }
    }
}