import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from '@/store';
import LoginPage from '../login';
import { AuthProvider } from '@/components/AuthProvider';

// Mock AuthProvider
const mockPwdLogin = jest.fn();
jest.mock('@/components/AuthProvider', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuthProvider: () => ({
    pwdLogin: mockPwdLogin,
  }),
}));

// Mock navigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const renderLoginPage = () => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('LoginPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders login page with AI theme elements', () => {
    renderLoginPage();
    
    // 检查系统标题
    expect(screen.getByText('工程现场安全隐患识别与报告生成系统')).toBeInTheDocument();
    expect(screen.getByText('AI-Powered Risk Detection Platform')).toBeInTheDocument();
    
    // 检查登录表单
    expect(screen.getByText('系统登录')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('请输入用户账号')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('请输入登录密码')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '立即登录' })).toBeInTheDocument();
  });

  test('displays AI features correctly', () => {
    renderLoginPage();
    
    expect(screen.getByText('智能图像识别')).toBeInTheDocument();
    expect(screen.getByText('实时风险评估')).toBeInTheDocument();
    expect(screen.getByText('预测性维护')).toBeInTheDocument();
    expect(screen.getByText('数据驱动决策')).toBeInTheDocument();
  });

  test('handles form submission correctly', async () => {
    mockPwdLogin.mockResolvedValue({});
    renderLoginPage();
    
    const usernameInput = screen.getByPlaceholderText('请输入用户账号');
    const passwordInput = screen.getByPlaceholderText('请输入登录密码');
    const submitButton = screen.getByRole('button', { name: '立即登录' });
    
    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(passwordInput, { target: { value: 'testpass' } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockPwdLogin).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'testpass',
        remember: true,
      });
    });
  });

  test('shows loading state during login', async () => {
    mockPwdLogin.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));
    renderLoginPage();
    
    const usernameInput = screen.getByPlaceholderText('请输入用户账号');
    const passwordInput = screen.getByPlaceholderText('请输入登录密码');
    const submitButton = screen.getByRole('button', { name: '立即登录' });
    
    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(passwordInput, { target: { value: 'testpass' } });
    fireEvent.click(submitButton);
    
    expect(screen.getByText('正在验证...')).toBeInTheDocument();
  });

  test('handles login error correctly', async () => {
    mockPwdLogin.mockRejectedValue(new Error('Login failed'));
    renderLoginPage();
    
    const usernameInput = screen.getByPlaceholderText('请输入用户账号');
    const passwordInput = screen.getByPlaceholderText('请输入登录密码');
    const submitButton = screen.getByRole('button', { name: '立即登录' });
    
    fireEvent.change(usernameInput, { target: { value: 'testuser' } });
    fireEvent.change(passwordInput, { target: { value: 'wrongpass' } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockPwdLogin).toHaveBeenCalled();
      // 验证错误处理逻辑
      const formContainer = document.querySelector('.form-container');
      expect(formContainer).toHaveClass('ai-error-shake');
    });
  });

  test('validates required fields', async () => {
    renderLoginPage();
    
    const submitButton = screen.getByRole('button', { name: '立即登录' });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('请输入账号')).toBeInTheDocument();
      expect(screen.getByText('请输入密码')).toBeInTheDocument();
    });
  });

  test('applies correct CSS classes for AI theme', () => {
    renderLoginPage();
    
    const container = document.querySelector('.login-container');
    const brandSection = document.querySelector('.ai-brand-section');
    const formTitle = document.querySelector('.ai-form-title');
    const loginButton = document.querySelector('.ai-login-button');
    
    expect(container).toBeInTheDocument();
    expect(brandSection).toBeInTheDocument();
    expect(formTitle).toBeInTheDocument();
    expect(loginButton).toBeInTheDocument();
  });

  test('renders particles animation elements', () => {
    renderLoginPage();
    
    const particlesContainer = document.querySelector('.ai-particles-container');
    const particles = document.querySelectorAll('.ai-particle');
    
    expect(particlesContainer).toBeInTheDocument();
    expect(particles).toHaveLength(20);
  });
});