using System.ComponentModel.DataAnnotations;
using Fancyx.Repository.BaseEntity;
using Fancyx.Core.Interfaces;
using FreeSql.DataAnnotations;

namespace Fancyx.Admin.Entities.Project
{
    [Table(Name ="sys_project")]
    public class ProjectDO : FullAuditedEntity, ITenant
    {
        /// <summary>
        /// 主键ID - 重写为long类型以匹配业务需求
        /// </summary>
        [Column(IsPrimary = true)]
        public new long Id { get; set; }

        /// <summary>
        /// 项目编号 (自动生成，规则P+4位年份+2位月份+4位序号)
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string? ProjectNo { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目地址
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string? ProjectAddress { get; set; }

        /// <summary>
        /// 所属区域
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string? Region { get; set; }

        /// <summary>
        /// 项目类型（生产安全、质量安全）
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string? ProjectType { get; set; }

        /// <summary>
        /// 关联合同
        /// </summary>
        [MaxLength(100)]
        public string? RelatedContract { get; set; }

        /// <summary>
        /// 现场负责人
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string? SiteManager { get; set; }

        /// <summary>
        /// 负责人电话
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string? ManagerPhone { get; set; }

        /// <summary>
        /// 负责人职务
        /// </summary>
        [MaxLength(50)]
        public string? ManagerPosition { get; set; }

        /// <summary>
        /// 开工日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 竣工日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 工程类型
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string? EngineeringType { get; set; }

        /// <summary>
        /// 工程类别
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string? EngineeringCategory { get; set; }

        /// <summary>
        /// 建设单位
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string? ConstructionUnit { get; set; }

        /// <summary>
        /// 监理单位
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string? SupervisionUnit { get; set; }

        /// <summary>
        /// 设计单位
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string? DesignUnit { get; set; }

        /// <summary>
        /// 施工单位
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string? ConstructionCompany { get; set; }

        /// <summary>
        /// 施工许可证号
        /// </summary>
        [MaxLength(100)]
        public string? ConstructionPermitNo { get; set; }

        /// <summary>
        /// 危险化学品从业人数
        /// </summary>
        public int? HazardousChemicalWorkers { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(1000)]
        public string? Remarks { get; set; }

        public string? TenantId { get; set; }
    }
}