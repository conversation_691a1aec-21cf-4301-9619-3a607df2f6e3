using System.ComponentModel.DataAnnotations;
using Fancyx.Admin.Controllers.Mobile;

namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端创建检查任务DTO
/// </summary>
public class MobileCreateCheckDto
{
    /// <summary>
    /// 关联项目ID
    /// </summary>
    [Required(ErrorMessage = "请选择关联项目")]
    public long ProjectId { get; set; }

    /// <summary>
    /// 检查类型（生产安全/质量安全）
    /// </summary>
    [Required(ErrorMessage = "检查类型不能为空")]
    public string CheckType { get; set; } = string.Empty;

    /// <summary>
    /// 二级分类
    /// </summary>
    [Required(ErrorMessage = "二级分类不能为空")]
    public string SecondaryCategory { get; set; } = string.Empty;

    /// <summary>
    /// 检查日期
    /// </summary>
    [Required(ErrorMessage = "检查日期不能为空")]
    public DateTime CheckDate { get; set; }

    /// <summary>
    /// 委托单位
    /// </summary>
    public string? ClientUnit { get; set; }

    /// <summary>
    /// 检查组长
    /// </summary>
    [Required(ErrorMessage = "检查组长不能为空")]
    [StringLength(50, ErrorMessage = "检查组长姓名长度不能超过50字符")]
    public string CheckLeader { get; set; } = string.Empty;

    /// <summary>
    /// 检查组员（多人用逗号分隔）
    /// </summary>
    [Required(ErrorMessage = "检查组员不能为空")]
    [StringLength(200, ErrorMessage = "检查组员信息长度不能超过200字符")]
    public string CheckMembers { get; set; } = string.Empty;

    /// <summary>
    /// 工程进度
    /// </summary>
    [Required(ErrorMessage = "工程进度不能为空")]
    public string ProjectProgress { get; set; } = string.Empty;

    /// <summary>
    /// 检查内容
    /// </summary>
    [Required(ErrorMessage = "检查内容不能为空")]
    [StringLength(2000, ErrorMessage = "检查内容长度不能超过2000字符")]
    public string CheckContent { get; set; } = string.Empty;

    /// <summary>
    /// 预计检查时长（小时）
    /// </summary>
    public double? EstimatedHours { get; set; }

    /// <summary>
    /// 检查优先级（高/中/低）
    /// </summary>
    public string Priority { get; set; } = "中";

    /// <summary>
    /// GPS位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 检查要点列表
    /// </summary>
    public List<string> CheckPoints { get; set; } = new();



    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500, ErrorMessage = "备注长度不能超过500字符")]
    public string? Remarks { get; set; }
}

/// <summary>
/// 移动端更新检查任务DTO
/// </summary>
public class MobileUpdateCheckDto
{
    /// <summary>
    /// 检查类型
    /// </summary>
    public string? CheckType { get; set; }

    /// <summary>
    /// 二级分类
    /// </summary>
    public string? SecondaryCategory { get; set; }

    /// <summary>
    /// 检查日期
    /// </summary>
    public DateTime? CheckDate { get; set; }

    /// <summary>
    /// 委托单位
    /// </summary>
    public string? ClientUnit { get; set; }

    /// <summary>
    /// 检查组长
    /// </summary>
    [StringLength(50, ErrorMessage = "检查组长姓名长度不能超过50字符")]
    public string? CheckLeader { get; set; }

    /// <summary>
    /// 检查组员
    /// </summary>
    [StringLength(200, ErrorMessage = "检查组员信息长度不能超过200字符")]
    public string? CheckMembers { get; set; }

    /// <summary>
    /// 工程进度
    /// </summary>
    public string? ProjectProgress { get; set; }

    /// <summary>
    /// 检查内容
    /// </summary>
    [StringLength(2000, ErrorMessage = "检查内容长度不能超过2000字符")]
    public string? CheckContent { get; set; }

    /// <summary>
    /// 预计检查时长
    /// </summary>
    public double? EstimatedHours { get; set; }

    /// <summary>
    /// 检查优先级
    /// </summary>
    public string? Priority { get; set; }

    /// <summary>
    /// 检查要点列表
    /// </summary>
    public List<string>? CheckPoints { get; set; }

    /// <summary>
    /// 更新理由
    /// </summary>
    [StringLength(500, ErrorMessage = "更新理由长度不能超过500字符")]
    public string? UpdateReason { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500, ErrorMessage = "备注长度不能超过500字符")]
    public string? Remarks { get; set; }
}

/// <summary>
/// 移动端开始检查DTO
/// </summary>
public class MobileStartCheckDto
{
    /// <summary>
    /// 开始位置
    /// </summary>
    [Required(ErrorMessage = "开始位置不能为空")]
    public MobileLocationDto Location { get; set; } = new();



    /// <summary>
    /// 现场照片
    /// </summary>
    public List<string> StartPhotos { get; set; } = new();

    /// <summary>
    /// 开始备注
    /// </summary>
    [StringLength(500, ErrorMessage = "开始备注长度不能超过500字符")]
    public string? StartRemarks { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端暂停检查DTO
/// </summary>
public class MobilePauseCheckDto
{
    /// <summary>
    /// 暂停原因
    /// </summary>
    [Required(ErrorMessage = "暂停原因不能为空")]
    [StringLength(200, ErrorMessage = "暂停原因长度不能超过200字符")]
    public string PauseReason { get; set; } = string.Empty;

    /// <summary>
    /// 暂停位置
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 已完成的检查内容
    /// </summary>
    public string? CompletedContent { get; set; }

    /// <summary>
    /// 暂停时进度（百分比）
    /// </summary>
    [Range(0, 100, ErrorMessage = "进度值必须在0-100之间")]
    public int Progress { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端恢复检查DTO
/// </summary>
public class MobileResumeCheckDto
{
    /// <summary>
    /// 恢复位置
    /// </summary>
    [Required(ErrorMessage = "恢复位置不能为空")]
    public MobileLocationDto Location { get; set; } = new();

    /// <summary>
    /// 恢复备注
    /// </summary>
    [StringLength(200, ErrorMessage = "恢复备注长度不能超过200字符")]
    public string? ResumeRemarks { get; set; }



    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端检查进度DTO
/// </summary>
public class MobileCheckProgressDto
{
    /// <summary>
    /// 当前进度（百分比）
    /// </summary>
    [Range(0, 100, ErrorMessage = "进度值必须在0-100之间")]
    public int Progress { get; set; }

    /// <summary>
    /// 已完成的检查项目
    /// </summary>
    public List<string> CompletedItems { get; set; } = new();

    /// <summary>
    /// 当前正在检查的项目
    /// </summary>
    public string? CurrentItem { get; set; }

    /// <summary>
    /// 已发现隐患数量
    /// </summary>
    public int DangerCount { get; set; }

    /// <summary>
    /// 当前位置
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 进度备注
    /// </summary>
    [StringLength(300, ErrorMessage = "进度备注长度不能超过300字符")]
    public string? ProgressRemarks { get; set; }

    /// <summary>
    /// 保存时间
    /// </summary>
    public DateTime SaveTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端批量检查操作DTO
/// </summary>
public class MobileBatchCheckOperationDto
{
    /// <summary>
    /// 检查任务ID列表
    /// </summary>
    [Required(ErrorMessage = "请选择要操作的检查任务")]
    public List<long> CheckIds { get; set; } = new();

    /// <summary>
    /// 操作类型（delete/export/complete/assign）
    /// </summary>
    [Required(ErrorMessage = "请选择操作类型")]
    public string OperationType { get; set; } = string.Empty;

    /// <summary>
    /// 操作参数（JSON格式）
    /// </summary>
    public string? OperationParams { get; set; }

    /// <summary>
    /// 操作原因
    /// </summary>
    [StringLength(500, ErrorMessage = "操作原因长度不能超过500字符")]
    public string? Reason { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}


/// <summary>
/// 移动端照片上传结果DTO
/// </summary>
public class MobilePhotoUploadResultDto
{
    /// <summary>
    /// 成功上传的照片列表
    /// </summary>
    public List<MobileFileUploadResultDto> SuccessPhotos { get; set; } = new();

    /// <summary>
    /// 失败的照片列表
    /// </summary>
    public List<MobileUploadErrorDto> FailedPhotos { get; set; } = new();

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 上传批次ID
    /// </summary>
    public string BatchId { get; set; } = string.Empty;

    /// <summary>
    /// 上传时间
    /// </summary>
    public DateTime UploadTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 移动端批量照片上传DTO
/// </summary>
public class MobileBatchPhotoUploadDto
{
    /// <summary>
    /// 关联记录ID（隐患或检查）
    /// </summary>
    [Required(ErrorMessage = "关联记录ID不能为空")]
    public string RelatedRecordId { get; set; } = string.Empty;

    /// <summary>
    /// 记录类型（danger/check）
    /// </summary>
    [Required(ErrorMessage = "记录类型不能为空")]
    public string RecordType { get; set; } = string.Empty;

    /// <summary>
    /// 照片文件列表
    /// </summary>
    [Required(ErrorMessage = "请选择要上传的照片")]
    public List<IFormFile> Photos { get; set; } = new();

    /// <summary>
    /// 是否压缩图片
    /// </summary>
    public bool CompressImages { get; set; } = true;

    /// <summary>
    /// 照片描述列表（与照片对应）
    /// </summary>
    public List<string>? PhotoDescriptions { get; set; }

    /// <summary>
    /// GPS位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端上传错误DTO
/// </summary>
public class MobileUploadErrorDto
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }
}