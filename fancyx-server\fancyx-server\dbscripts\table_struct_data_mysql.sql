/*
 MySQL DDL generated from original PostgreSQL script
 Target Version : MySQL 8.0+
 Encoding       : UTF8MB4
 Author         : AI Converter
 Date           : 2025-07-15
*/

-- 如果需要请先创建数据库
-- CREATE DATABASE IF NOT EXISTS `fancyx_admin` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
-- USE `fancyx_admin`;

-- ----------------------------
-- Table structure for api_access_log
-- ----------------------------
DROP TABLE IF EXISTS `api_access_log`;
CREATE TABLE `api_access_log` (
  `id` CHAR(36) NOT NULL COMMENT '主键 UUID',
  `creator_id` CHAR(36) NOT NULL COMMENT '创建者',
  `creation_time` DATETIME(6) NOT NULL COMMENT '创建时间',
  `path` VARCHAR(255) NULL,
  `method` VARCHAR(255) NULL,
  `ip` VARCHAR(32) NULL,
  `request_time` DATETIME(6) NOT NULL,
  `response_time` DATETIME(6) NULL,
  `duration` BIGINT NULL,
  `user_id` CHAR(36) NULL,
  `user_name` VARCHAR(255) NULL,
  `request_body` VARCHAR(255) NULL,
  `response_body` TEXT NULL,
  `browser` VARCHAR(512) NULL,
  `query_string` VARCHAR(255) NULL,
  `trace_id` VARCHAR(255) NULL,
  `operate_type` JSON NULL COMMENT 'INT 数组改为 JSON 存储',
  `operate_name` VARCHAR(255) NULL,
  `tenant_id` VARCHAR(255) NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for exception_log
-- ----------------------------
DROP TABLE IF EXISTS `exception_log`;
CREATE TABLE `exception_log` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `exception_type` VARCHAR(255) NULL,
  `message` TEXT NULL,
  `stack_trace` TEXT NULL,
  `inner_exception` TEXT NULL,
  `request_path` VARCHAR(255) NULL,
  `request_method` VARCHAR(255) NULL,
  `user_id` CHAR(36) NULL,
  `user_name` VARCHAR(255) NULL,
  `ip` VARCHAR(32) NULL,
  `browser` VARCHAR(512) NULL,
  `trace_id` VARCHAR(255) NULL,
  `is_handled` TINYINT(1) NOT NULL,
  `handled_time` DATETIME(6) NULL,
  `handled_by` VARCHAR(255) NULL,
  `tenant_id` VARCHAR(255) NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for log_record
-- ----------------------------
DROP TABLE IF EXISTS `log_record`;
CREATE TABLE `log_record` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `type` VARCHAR(255) NOT NULL,
  `sub_type` VARCHAR(255) NOT NULL,
  `biz_no` VARCHAR(255) NOT NULL,
  `content` VARCHAR(255) NOT NULL,
  `browser` VARCHAR(512) NULL,
  `ip` VARCHAR(32) NULL,
  `trace_id` VARCHAR(255) NULL,
  `tenant_id` VARCHAR(255) NULL,
  `user_id` CHAR(36) NULL,
  `user_name` VARCHAR(255) NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for org_employee
-- ----------------------------
DROP TABLE IF EXISTS `org_employee`;
CREATE TABLE `org_employee` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `is_deleted` TINYINT(1) NOT NULL,
  `deleter_id` CHAR(36) NULL,
  `deletion_time` DATETIME(6) NULL,
  `code` VARCHAR(64) NOT NULL COMMENT '工号',
  `name` VARCHAR(64) NOT NULL COMMENT '姓名',
  `sex` INT NOT NULL COMMENT '性别',
  `phone` VARCHAR(16) NOT NULL COMMENT '手机号码',
  `id_no` VARCHAR(32) NULL COMMENT '身份证',
  `front_id_no_url` VARCHAR(512) NULL COMMENT '身份证正面',
  `back_id_no_url` VARCHAR(512) NULL COMMENT '身份证背面',
  `birthday` DATETIME(6) NULL COMMENT '生日',
  `address` VARCHAR(512) NULL COMMENT '现住址',
  `email` VARCHAR(64) NULL COMMENT '邮箱',
  `in_time` DATETIME(6) NOT NULL COMMENT '入职时间',
  `out_time` DATETIME(6) NULL COMMENT '离职时间',
  `status` INT NOT NULL COMMENT '状态 1正常2离职',
  `user_id` CHAR(36) NULL COMMENT '关联用户ID',
  `dept_id` CHAR(36) NULL COMMENT '部门ID',
  `position_id` CHAR(36) NULL COMMENT '职位ID',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for org_position
-- ----------------------------
DROP TABLE IF EXISTS `org_position`;
CREATE TABLE `org_position` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `is_deleted` TINYINT(1) NOT NULL,
  `deleter_id` CHAR(36) NULL,
  `deletion_time` DATETIME(6) NULL,
  `code` VARCHAR(32) NOT NULL COMMENT '职位编号',
  `name` VARCHAR(64) NOT NULL COMMENT '职位名称',
  `level` INT NOT NULL COMMENT '职级',
  `status` INT NOT NULL COMMENT '状态：1正常2停用',
  `description` VARCHAR(512) NULL COMMENT '描述',
  `group_id` CHAR(36) NULL COMMENT '职位分组',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for org_position_group
-- ----------------------------
DROP TABLE IF EXISTS `org_position_group`;
CREATE TABLE `org_position_group` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `group_name` VARCHAR(64) NOT NULL COMMENT '分组名',
  `remark` VARCHAR(512) NULL COMMENT '备注',
  `parent_id` CHAR(36) NULL COMMENT '父ID',
  `parent_ids` VARCHAR(1024) NULL COMMENT '层级父ID',
  `sort` INT NOT NULL COMMENT '排序值',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `name` VARCHAR(256) NOT NULL COMMENT '配置名称',
  `cfg_key` VARCHAR(128) NOT NULL COMMENT '配置键名',
  `value` VARCHAR(255) NOT NULL COMMENT '配置键值',
  `group_key` VARCHAR(64) NULL COMMENT '组别',
  `remark` VARCHAR(512) NULL COMMENT '备注',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sys_config_key` (`cfg_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `is_deleted` TINYINT(1) NOT NULL,
  `deleter_id` CHAR(36) NULL,
  `deletion_time` DATETIME(6) NULL,
  `code` VARCHAR(32) NOT NULL COMMENT '部门编号',
  `name` VARCHAR(64) NOT NULL COMMENT '部门名称',
  `sort` INT NOT NULL COMMENT '排序',
  `description` VARCHAR(512) NULL COMMENT '描述',
  `status` INT NOT NULL COMMENT '状态：1正常2停用',
  `curator_id` CHAR(36) NULL COMMENT '负责人',
  `email` VARCHAR(64) NULL COMMENT '邮箱',
  `phone` VARCHAR(64) NULL COMMENT '电话',
  `parent_id` CHAR(36) NULL COMMENT '父ID',
  `parent_ids` VARCHAR(1024) NULL COMMENT '层级父ID',
  `layer` INT NOT NULL COMMENT '层级',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `value` VARCHAR(256) NOT NULL COMMENT '字典值',
  `label` VARCHAR(128) NULL COMMENT '显示文本',
  `dict_type` VARCHAR(128) NOT NULL COMMENT '字典类型',
  `remark` VARCHAR(512) NULL COMMENT '备注',
  `sort` INT NOT NULL COMMENT '排序值',
  `is_enabled` TINYINT(1) NOT NULL COMMENT '是否开启',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `name` VARCHAR(128) NOT NULL COMMENT '字典名称',
  `dict_type` VARCHAR(128) NOT NULL COMMENT '字典类型',
  `remark` VARCHAR(512) NULL COMMENT '备注',
  `is_enabled` TINYINT(1) NOT NULL COMMENT '是否开启',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sys_dict_type` (`dict_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_login_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `user_name` VARCHAR(32) NOT NULL COMMENT '账号',
  `ip` VARCHAR(32) NULL COMMENT 'IP',
  `address` VARCHAR(256) NULL COMMENT '登录地址',
  `os` VARCHAR(64) NULL COMMENT '系统',
  `browser` VARCHAR(512) NULL COMMENT '浏览器',
  `operation_msg` VARCHAR(128) NULL COMMENT '操作信息',
  `is_success` TINYINT(1) NOT NULL COMMENT '是否成功',
  `session_id` VARCHAR(36) NULL COMMENT '会话ID',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `title` VARCHAR(32) NOT NULL COMMENT '显示标题/名称',
  `icon` VARCHAR(64) NULL COMMENT '图标',
  `path` VARCHAR(256) NULL COMMENT '路由/地址',
  `component` VARCHAR(256) NULL COMMENT '组件地址',
  `menu_type` INT NOT NULL COMMENT '功能类型',
  `permission` VARCHAR(128) NOT NULL COMMENT '授权码',
  `parent_id` CHAR(36) NULL COMMENT '父级ID',
  `sort` INT NOT NULL COMMENT '排序',
  `display` TINYINT(1) NOT NULL COMMENT '是否隐藏',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_notification
-- ----------------------------
DROP TABLE IF EXISTS `sys_notification`;
CREATE TABLE `sys_notification` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `title` VARCHAR(100) NOT NULL COMMENT '通知标题',
  `description` VARCHAR(500) NULL COMMENT '通知描述',
  `employee_id` CHAR(36) NOT NULL COMMENT '通知员工',
  `is_readed` TINYINT(1) NOT NULL COMMENT '是否已读(1已读0未读)',
  `readed_time` DATETIME(6) NOT NULL COMMENT '已读时间',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `is_deleted` TINYINT(1) NOT NULL,
  `deleter_id` CHAR(36) NULL,
  `deletion_time` DATETIME(6) NULL,
  `role_name` VARCHAR(64) NOT NULL COMMENT '角色名',
  `remark` VARCHAR(512) NULL COMMENT '备注',
  `power_data_type` INT NOT NULL COMMENT '数据权限类型',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  `is_enabled` TINYINT(1) NOT NULL COMMENT '是否启用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
  `id` CHAR(36) NOT NULL,
  `menu_id` CHAR(36) NOT NULL COMMENT '菜单ID',
  `role_id` CHAR(36) NOT NULL COMMENT '角色ID',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_sys_role_menu_role` (`role_id`),
  KEY `idx_sys_role_menu_menu` (`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `id` CHAR(36) NOT NULL,
  `creator_id` CHAR(36) NOT NULL,
  `creation_time` DATETIME(6) NOT NULL,
  `last_modification_time` DATETIME(6) NULL,
  `last_modifier_id` CHAR(36) NULL,
  `is_deleted` TINYINT(1) NOT NULL,
  `deleter_id` CHAR(36) NULL,
  `deletion_time` DATETIME(6) NULL,
  `user_name` VARCHAR(32) NOT NULL COMMENT '用户名',
  `password` VARCHAR(512) NOT NULL COMMENT '密码',
  `password_salt` VARCHAR(256) NOT NULL COMMENT '密码盐',
  `avatar` VARCHAR(256) NULL COMMENT '头像',
  `nick_name` VARCHAR(64) NOT NULL COMMENT '昵称',
  `sex` INT NOT NULL COMMENT '性别',
  `is_enabled` TINYINT(1) NOT NULL COMMENT '是否启用',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
  `id` CHAR(36) NOT NULL,
  `user_id` CHAR(36) NOT NULL COMMENT '用户ID',
  `role_id` CHAR(36) NOT NULL COMMENT '角色ID',
  `tenant_id` VARCHAR(18) NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_sys_user_role_user` (`user_id`),
  KEY `idx_sys_user_role_role` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- 外键约束（如有需要可自行添加）
-- ALTER TABLE `sys_role_menu` ADD CONSTRAINT fk_role FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`);
-- ALTER TABLE `sys_role_menu` ADD CONSTRAINT fk_menu FOREIGN KEY (`menu_id`) REFERENCES `sys_menu` (`id`);
-- ...

-- ----------------------------
-- 数据初始化脚本（可选）
-- 注意：布尔类型请使用 0/1
-- INSERT INTO `org_employee` VALUES ('6869907c-...', ...); 