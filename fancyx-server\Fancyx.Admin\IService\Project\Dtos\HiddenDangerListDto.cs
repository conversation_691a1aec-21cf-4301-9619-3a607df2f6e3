using System;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 隐患明细列表 DTO
    /// </summary>
    public class HiddenDangerListDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 检查信息
        /// </summary>
        public string CheckInfo { get; set; }

        /// <summary>
        /// 隐患大类
        /// </summary>
        public string DangerCategory { get; set; }

        /// <summary>
        /// 问题等级
        /// </summary>
        public string ProblemLevel { get; set; }

        /// <summary>
        /// 安全隐患
        /// </summary>
        public string SafetyHazard { get; set; }

        /// <summary>
        /// 具体描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否需要复查
        /// </summary>
        public bool NeedReview { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatorName { get; set; }
    }
}