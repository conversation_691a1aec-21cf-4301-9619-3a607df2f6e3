using System;
using System.Collections.Generic;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 隐患明细详情 DTO
    /// </summary>
    public class HiddenDangerDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 检查ID
        /// </summary>
        public long ProjectCheckId { get; set; }

        /// <summary>
        /// 检查信息
        /// </summary>
        public string CheckInfo { get; set; }

        /// <summary>
        /// 隐患大类
        /// </summary>
        public string DangerCategory { get; set; }

        /// <summary>
        /// 标准规范ID
        /// </summary>
        public long? StandardId { get; set; }

        /// <summary>
        /// 标准规范名称
        /// </summary>
        public string StandardName { get; set; }

        /// <summary>
        /// 问题等级
        /// </summary>
        public string ProblemLevel { get; set; }

        /// <summary>
        /// 安全隐患
        /// </summary>
        public string SafetyHazard { get; set; }

        /// <summary>
        /// 法规条款
        /// </summary>
        public string RegulationClause { get; set; }

        /// <summary>
        /// 具体描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 整改要求
        /// </summary>
        public string RectificationRequirement { get; set; }

        /// <summary>
        /// 可能产生后果
        /// </summary>
        public string PossibleConsequences { get; set; }

        /// <summary>
        /// 整改建议
        /// </summary>
        public string RectificationSuggestion { get; set; }

        /// <summary>
        /// 是否需要复查
        /// </summary>
        public bool NeedReview { get; set; }

        /// <summary>
        /// 照片列表
        /// </summary>
        public List<string> Photos { get; set; } = new List<string>();

        /// <summary>
        /// 状态（0待确认，1正式记录）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatorName { get; set; }
    }
}