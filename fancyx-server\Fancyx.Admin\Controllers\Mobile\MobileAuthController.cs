using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Fancyx.Admin.IService.Account;
using Fancyx.Admin.IService.Mobile.Dtos;
using Fancyx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.Controllers.Mobile;

/// <summary>
/// 移动端认证API
/// </summary>
[ApiController]
[Route("api/mobile/v1/auth")]
[AllowAnonymous]
public class MobileAuthController : ControllerBase
{
    private readonly IAccountService _accountService;
    private readonly ILogger<MobileAuthController> _logger;

    public MobileAuthController(IAccountService accountService, ILogger<MobileAuthController> logger)
    {
        _accountService = accountService;
        _logger = logger;
    }

    /// <summary>
    /// 移动端登录
    /// </summary>
    /// <param name="loginDto">登录参数</param>
    /// <returns>登录结果</returns>
    [HttpPost("login")]
    public async Task<AppResponse<MobileLoginResultDto>> LoginAsync([FromBody] MobileLoginDto loginDto)
    {
        try
        {
            // 设备信息验证
            if (!ValidateDeviceInfo(loginDto.DeviceInfo))
            {
                return AppResponse<MobileLoginResultDto>.Error("设备信息不完整");
            }

            // 简化实现：调用基础登录服务
            // 注意：这里需要根据实际的IAccountService接口调整
            _logger.LogInformation("移动端用户登录尝试，用户名: {UserName}, 设备: {DeviceType}",
                loginDto.UserName, loginDto.DeviceInfo.DeviceType);

            // 模拟登录验证（实际项目中应该调用真实的认证服务）
            var loginResult = new MobileLoginResultDto
            {
                AccessToken = GenerateSimpleToken(),
                RefreshToken = GenerateSimpleToken(),
                TokenType = "Bearer",
                ExpiresIn = 7200, // 2小时
                UserInfo = new MobileUserInfoDto
                {
                    Id = Guid.NewGuid(),
                    UserName = loginDto.UserName,
                    RealName = loginDto.UserName,
                    Roles = new List<string> { "mobile_user" },
                    Permissions = GetMobilePermissions(new List<string>())
                },
                AppConfig = await GetMobileAppConfigAsync(),
                ServerTime = DateTime.Now
            };

            return AppResponse<MobileLoginResultDto>.Success(loginResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移动端登录失败：{UserName}", loginDto.UserName);
            return AppResponse<MobileLoginResultDto>.Error("登录失败，请重试");
        }
    }

    /// <summary>
    /// 刷新Token
    /// </summary>
    /// <param name="refreshDto">刷新参数</param>
    /// <returns>新的Token</returns>
    [HttpPost("refresh-token")]
    public async Task<AppResponse<MobileRefreshTokenResultDto>> RefreshTokenAsync([FromBody] MobileRefreshTokenDto refreshDto)
    {
        try
        {
            // 验证refresh token
            var tokenResult = await ValidateRefreshTokenAsync(refreshDto.RefreshToken);
            if (!tokenResult.IsValid)
            {
                return AppResponse<MobileRefreshTokenResultDto>.Error("Refresh Token无效或已过期");
            }

            // 生成新的访问令牌
            var newTokens = await GenerateNewTokensAsync(tokenResult.UserId, refreshDto.DeviceId);

            var result = new MobileRefreshTokenResultDto
            {
                AccessToken = newTokens.AccessToken,
                RefreshToken = newTokens.RefreshToken,
                TokenType = "Bearer",
                ExpiresIn = newTokens.ExpiresIn,
                RefreshExpiresIn = newTokens.RefreshExpiresIn,
                ServerTime = DateTime.UtcNow
            };

            return AppResponse<MobileRefreshTokenResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新Token失败：{RefreshToken}", refreshDto.RefreshToken);
            return AppResponse<MobileRefreshTokenResultDto>.Error("Token刷新失败");
        }
    }

    /// <summary>
    /// 移动端登出
    /// </summary>
    /// <param name="logoutDto">登出参数</param>
    /// <returns>登出结果</returns>
    [HttpPost("logout")]
    [Authorize]
    public async Task<AppResponse<bool>> LogoutAsync([FromBody] MobileLogoutDto logoutDto)
    {
        try
        {
            // 注销当前设备的Token
            await RevokeDeviceTokenAsync(logoutDto.DeviceId);
            
            // 记录登出日志
            await LogDeviceLogoutAsync(logoutDto);

            return AppResponse<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移动端登出失败");
            return AppResponse<bool>.Error("登出失败");
        }
    }

    /// <summary>
    /// 获取移动端用户信息
    /// </summary>
    /// <returns>用户信息</returns>
    [HttpGet("user-info")]
    [Authorize]
    public async Task<AppResponse<MobileUserInfoDto>> GetUserInfoAsync()
    {
        try
        {
            var userInfo = await _accountService.GetCurrentUserInfoAsync();
            var currentUser = await _accountService.GetCurrentUserInfoAsync();
            var permissions = await _accountService.GetUserPermissionsAsync(currentUser.Id);

            var result = new MobileUserInfoDto
            {
                Id = userInfo.Id,
                UserName = userInfo.UserName,
                RealName = userInfo.RealName,
                Avatar = userInfo.Avatar,
                Roles = userInfo.Roles,
                Permissions = GetMobilePermissions(permissions),
                TenantId = userInfo.TenantId,
                LastLoginTime = userInfo.LastLoginTime
            };

            return AppResponse<MobileUserInfoDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取移动端用户信息失败");
            return AppResponse<MobileUserInfoDto>.Error("获取用户信息失败");
        }
    }

    /// <summary>
    /// 检查Token有效性
    /// </summary>
    /// <returns>Token状态</returns>
    [HttpGet("token-status")]
    [Authorize]
    public async Task<AppResponse<MobileTokenStatusDto>> GetTokenStatusAsync()
    {
        try
        {
            // 获取当前Token信息
            var tokenInfo = await GetCurrentTokenInfoAsync();

            var result = new MobileTokenStatusDto
            {
                IsValid = true,
                ExpiresAt = tokenInfo.ExpiresAt,
                RemainingTime = (int)(tokenInfo.ExpiresAt - DateTime.UtcNow).TotalSeconds,
                NeedRefresh = (tokenInfo.ExpiresAt - DateTime.UtcNow).TotalMinutes < 30, // 30分钟内需要刷新
                ServerTime = DateTime.UtcNow
            };

            return AppResponse<MobileTokenStatusDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查Token状态失败");
            return AppResponse<MobileTokenStatusDto>.Error("Token状态检查失败");
        }
    }

    /// <summary>
    /// 获取设备登录历史
    /// </summary>
    /// <param name="pageSize">每页数量</param>
    /// <returns>登录历史</returns>
    [HttpGet("device-history")]
    [Authorize]
    public async Task<AppResponse<List<MobileDeviceHistoryDto>>> GetDeviceHistoryAsync([FromQuery] int pageSize = 10)
    {
        try
        {
            var history = await GetUserDeviceHistoryAsync(pageSize);
            return AppResponse<List<MobileDeviceHistoryDto>>.Success(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备历史失败");
            return AppResponse<List<MobileDeviceHistoryDto>>.Error("获取设备历史失败");
        }
    }

    #region 私有方法

    private bool ValidateDeviceInfo(MobileDeviceInfoDto deviceInfo)
    {
        return !string.IsNullOrEmpty(deviceInfo.DeviceId) &&
               !string.IsNullOrEmpty(deviceInfo.DeviceType) &&
               !string.IsNullOrEmpty(deviceInfo.AppVersion);
    }

    private async Task<MobileTokenResultDto> GenerateMobileTokenAsync(object loginResult, MobileDeviceInfoDto deviceInfo)
    {
        // 移动端Token有效期更长（7天）
        var accessTokenExpiry = DateTime.UtcNow.AddDays(7);
        var refreshTokenExpiry = DateTime.UtcNow.AddDays(30);

        // 实际实现中应该使用JWT生成Token
        var accessToken = Guid.NewGuid().ToString("N");
        var refreshToken = Guid.NewGuid().ToString("N");

        // 保存Token到缓存或数据库
        await SaveMobileTokenAsync(1, accessToken, refreshToken, deviceInfo, accessTokenExpiry, refreshTokenExpiry);

        return new MobileTokenResultDto
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            ExpiresIn = (int)(accessTokenExpiry - DateTime.UtcNow).TotalSeconds,
            RefreshExpiresIn = (int)(refreshTokenExpiry - DateTime.UtcNow).TotalSeconds
        };
    }

    private List<string> GetMobilePermissions(List<string> allPermissions)
    {
        // 过滤出移动端需要的权限
        var mobilePermissions = new List<string>
        {
            "project:view", "project:create",
            "check:view", "check:create", "check:execute",
            "danger:view", "danger:create", "danger:confirm",
            "file:upload"
        };

        return allPermissions.Where(p => mobilePermissions.Contains(p)).ToList();
    }

    private async Task<MobileAppConfigDto> GetMobileAppConfigAsync()
    {
        // 获取移动端应用配置
        return new MobileAppConfigDto
        {
            MaxFileSize = 10 * 1024 * 1024, // 10MB
            MaxBatchFiles = 20,
            SupportedImageFormats = new[] { "jpg", "jpeg", "png", "webp" },
            SupportedAudioFormats = new[] { "mp3", "wav", "m4a", "aac" },
            AIFeatureEnabled = true,
            OfflineModeEnabled = true,
            AutoSyncInterval = 300, // 5分钟
            LocationRequired = true
        };
    }

    // 其他私有方法的占位实现
    private async Task LogFailedLoginAttempt(MobileLoginDto loginDto) => await Task.Delay(1);
    private async Task RecordDeviceLoginAsync(Guid userId, MobileDeviceInfoDto deviceInfo) => await Task.Delay(1);

    private string GenerateSimpleToken()
    {
        // 简化的Token生成（实际项目中应该使用JWT）
        return Convert.ToBase64String(Guid.NewGuid().ToByteArray()).Replace("=", "").Replace("+", "").Replace("/", "");
    }
    private async Task<(bool IsValid, long UserId)> ValidateRefreshTokenAsync(string refreshToken) => (true, 1);
    private async Task<MobileTokenResultDto> GenerateNewTokensAsync(long userId, string deviceId) => new();
    private async Task RevokeDeviceTokenAsync(string deviceId) => await Task.Delay(1);
    private async Task LogDeviceLogoutAsync(MobileLogoutDto logoutDto) => await Task.Delay(1);
    private async Task<(DateTime ExpiresAt, bool IsValid)> GetCurrentTokenInfoAsync() => (DateTime.UtcNow.AddHours(1), true);
    private async Task<List<MobileDeviceHistoryDto>> GetUserDeviceHistoryAsync(int pageSize) => new();
    private async Task SaveMobileTokenAsync(long userId, string accessToken, string refreshToken, MobileDeviceInfoDto deviceInfo, DateTime accessExpiry, DateTime refreshExpiry) => await Task.Delay(1);

    #endregion
}

#region 内部DTO类

/// <summary>
/// 移动端Token结果内部DTO
/// </summary>
internal class MobileTokenResultDto
{
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public int ExpiresIn { get; set; }
    public int RefreshExpiresIn { get; set; }
}

/// <summary>
/// 移动端刷新Token结果DTO
/// </summary>
public class MobileRefreshTokenResultDto
{
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public string TokenType { get; set; } = "Bearer";
    public int ExpiresIn { get; set; }
    public int RefreshExpiresIn { get; set; }
    public DateTime ServerTime { get; set; }
}

/// <summary>
/// 移动端登出DTO
/// </summary>
public class MobileLogoutDto
{
    [Required]
    public string DeviceId { get; set; } = string.Empty;
    public bool LogoutAllDevices { get; set; } = false;
}

/// <summary>
/// 移动端Token状态DTO
/// </summary>
public class MobileTokenStatusDto
{
    public bool IsValid { get; set; }
    public DateTime ExpiresAt { get; set; }
    public int RemainingTime { get; set; }
    public bool NeedRefresh { get; set; }
    public DateTime ServerTime { get; set; }
}

/// <summary>
/// 移动端设备历史DTO
/// </summary>
public class MobileDeviceHistoryDto
{
    public string DeviceId { get; set; } = string.Empty;
    public string DeviceType { get; set; } = string.Empty;
    public string DeviceModel { get; set; } = string.Empty;
    public DateTime LoginTime { get; set; }
    public DateTime? LogoutTime { get; set; }
    public string? Location { get; set; }
    public bool IsCurrentDevice { get; set; }
}

#endregion