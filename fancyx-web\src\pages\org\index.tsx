import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';

const OrgIndex: React.FC = () => {
  const location = useLocation();

  // 如果访问 /org 根路径，重定向到部门管理
  React.useEffect(() => {
    if (location.pathname === '/org') {
      window.location.replace('/org/dept');
    }
  }, [location.pathname]);

  return (
    <div className="org-container">
      <Outlet />
    </div>
  );
};

export default OrgIndex;