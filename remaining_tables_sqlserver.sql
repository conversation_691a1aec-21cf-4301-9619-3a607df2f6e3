/*
 剩余表结构脚本 - SQL Server版本
 
 Source Server Type    : PostgreSQL
 Target Server Type    : SQL Server
 File Encoding         : UTF-8
 
 Date: 2025/07/29
 包含字典、菜单、角色、用户等核心系统表
*/

-- ============================
-- 字典管理表
-- ============================

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dict_data]') AND type in (N'U'))
DROP TABLE [dbo].[sys_dict_data]
GO

CREATE TABLE [dbo].[sys_dict_data] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [value] nvarchar(256) NOT NULL,
  [label] nvarchar(128) NULL,
  [dict_type] nvarchar(128) NOT NULL,
  [remark] nvarchar(512) NULL,
  [sort] int NOT NULL,
  [is_enabled] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典数据表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典值', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'value'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'显示文本', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'label'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典类型', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'dict_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序值', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'sort'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否开启', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'is_enabled'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dict_type]') AND type in (N'U'))
DROP TABLE [dbo].[sys_dict_type]
GO

CREATE TABLE [dbo].[sys_dict_type] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(128) NOT NULL,
  [dict_type] nvarchar(128) NOT NULL,
  [remark] nvarchar(512) NULL,
  [is_enabled] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典类型表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典类型', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'dict_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否开启', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'is_enabled'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 日志管理表
-- ============================

-- ----------------------------
-- Table structure for sys_login_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_login_log]') AND type in (N'U'))
DROP TABLE [dbo].[sys_login_log]
GO

CREATE TABLE [dbo].[sys_login_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [user_name] nvarchar(32) NOT NULL,
  [ip] nvarchar(32) NULL,
  [address] nvarchar(256) NULL,
  [os] nvarchar(64) NULL,
  [browser] nvarchar(512) NULL,
  [operation_msg] nvarchar(128) NULL,
  [is_success] bit NOT NULL,
  [session_id] nvarchar(36) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录日志', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'账号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'user_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'IP', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'ip'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'address'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'os'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'浏览器', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'browser'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作信息', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'operation_msg'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否成功', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'is_success'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'会话ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'session_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_business_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_business_log]') AND type in (N'U'))
DROP TABLE [dbo].[sys_business_log]
GO

CREATE TABLE [dbo].[sys_business_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [user_name] nvarchar(32) NOT NULL,
  [action] nvarchar(512) NOT NULL,
  [http_method] nvarchar(16) NOT NULL,
  [url] nvarchar(512) NOT NULL,
  [os] nvarchar(64) NULL,
  [browser] nvarchar(64) NULL,
  [node_name] nvarchar(128) NOT NULL,
  [ip] nvarchar(32) NULL,
  [address] nvarchar(256) NULL,
  [is_success] bit NOT NULL DEFAULT 1,
  [operation_msg] nvarchar(128) NULL,
  [mill_seconds] int NOT NULL DEFAULT 0,
  [request_id] nvarchar(255) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'业务日志表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'业务日志ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'账号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'user_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作方法，全名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'action'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'HTTP请求方式', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'http_method'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'url'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'os'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'浏览器', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'browser'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作节点名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'node_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'IP', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'ip'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'address'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否成功', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'is_success'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作信息', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'operation_msg'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'耗时，单位毫秒', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'mill_seconds'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求跟踪ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'request_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 主键约束
-- ============================
ALTER TABLE [dbo].[sys_dict_data] ADD CONSTRAINT [PK_sys_dict_data] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_dict_type] ADD CONSTRAINT [PK_sys_dict_type] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_login_log] ADD CONSTRAINT [PK_sys_login_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_business_log] ADD CONSTRAINT [PK_sys_business_log] PRIMARY KEY ([id])
GO
