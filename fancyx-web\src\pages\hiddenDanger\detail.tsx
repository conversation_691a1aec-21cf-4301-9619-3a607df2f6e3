// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Descriptions, Button, Card, Tag, Image } from 'antd';
import { getHiddenDangerById } from '@/api/hiddenDanger';
import { useParams, useNavigate } from 'react-router-dom';

const HiddenDangerDetail: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [data, setData] = useState<any>({});

  useEffect(() => {
    if (id) {
      getHiddenDangerById(id).then(res => {
        setData(res.data || {});
      });
    }
  }, [id]);

  return (
    <Card bordered={false} style={{ maxWidth: 800, margin: '0 auto' }}>
      <Descriptions title="隐患明细详情" bordered column={2}>
        <Descriptions.Item label="检查信息">{data.checkInfo}</Descriptions.Item>
        <Descriptions.Item label="隐患大类">{data.dangerCategory}</Descriptions.Item>
        <Descriptions.Item label="标准规范名称">{data.standardName}</Descriptions.Item>
        <Descriptions.Item label="问题等级">{data.problemLevel}</Descriptions.Item>
        <Descriptions.Item label="安全隐患">{data.safetyHazard}</Descriptions.Item>
        <Descriptions.Item label="法规条款">{data.regulationClause}</Descriptions.Item>
        <Descriptions.Item label="具体描述" span={2}>{data.description}</Descriptions.Item>
        <Descriptions.Item label="整改要求" span={2}>{data.rectificationRequirement}</Descriptions.Item>
        <Descriptions.Item label="可能产生后果" span={2}>{data.possibleConsequences}</Descriptions.Item>
        <Descriptions.Item label="整改建议" span={2}>{data.rectificationSuggestion}</Descriptions.Item>
        <Descriptions.Item label="是否需要复查">
          <Tag color={data.needReview ? 'orange' : 'green'}>
            {data.needReview ? '需要复查' : '无需复查'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="状态">
          <Tag color={data.status === 0 ? 'blue' : 'green'}>
            {data.status === 0 ? '待确认' : '正式记录'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">{data.creationTime}</Descriptions.Item>
        <Descriptions.Item label="创建人">{data.creatorName}</Descriptions.Item>
        {data.photos && data.photos.length > 0 && (
          <Descriptions.Item label="照片" span={2}>
            <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
              {data.photos.map((photo: string, index: number) => (
                <Image
                  key={index}
                  width={100}
                  height={100}
                  src={photo}
                  style={{ objectFit: 'cover' }}
                />
              ))}
            </div>
          </Descriptions.Item>
        )}
      </Descriptions>
      <div style={{ marginTop: 24, textAlign: 'center' }}>
        <Button onClick={() => navigate('/hidden-danger')}>返回列表</Button>
      </div>
    </Card>
  );
};

export default HiddenDangerDetail; 