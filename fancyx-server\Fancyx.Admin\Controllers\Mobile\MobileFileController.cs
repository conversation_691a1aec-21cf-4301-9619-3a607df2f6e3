using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Fancyx.Shared.Models;
using Fancyx.Core.Attributes;
using Fancyx.ObjectStorage;

namespace Fancyx.Admin.Controllers.Mobile;

/// <summary>
/// 移动端文件上传API
/// </summary>
[ApiController]
[Route("api/mobile/v1/files")]
public class MobileFileController : ControllerBase
{
    private readonly IObjectStorageService _storageService;
    private readonly ILogger<MobileFileController> _logger;
    
    // 移动端专用的文件配置
    private const long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private const long MAX_BATCH_SIZE = 50 * 1024 * 1024; // 50MB
    private static readonly string[] ALLOWED_IMAGE_TYPES = { ".jpg", ".jpeg", ".png", ".webp" };
    private static readonly string[] ALLOWED_AUDIO_TYPES = { ".mp3", ".wav", ".m4a", ".aac" };

    public MobileFileController(IObjectStorageService storageService, ILogger<MobileFileController> logger)
    {
        _storageService = storageService;
        _logger = logger;
    }

    /// <summary>
    /// 单文件上传（移动端优化）
    /// </summary>
    /// <param name="file">文件</param>
    /// <param name="fileType">文件类型（photo/voice/signature）</param>
    /// <param name="compress">是否压缩（仅对图片有效）</param>
    /// <returns>上传结果</returns>
    [HttpPost("upload")]
    public async Task<AppResponse<MobileFileUploadResultDto>> UploadFileAsync(
        IFormFile file, 
        [FromForm] string fileType = "photo", 
        [FromForm] bool compress = true)
    {
        try
        {
            // 文件验证
            var validationResult = ValidateFile(file, fileType);
            if (!validationResult.IsValid)
            {
                return AppResponse<MobileFileUploadResultDto>.Error(validationResult.ErrorMessage);
            }

            // 生成文件名
            var fileName = GenerateMobileFileName(file.FileName, fileType);
            var directory = $"mobile/{fileType}s/{DateTime.Now:yyyy/MM/dd}";

            // 上传文件
            var fullPath = $"{directory}/{fileName}";
            var uploadResult = await _storageService.UploadAsync(file.OpenReadStream(), fullPath);
            
            // 如果是图片且需要压缩，生成缩略图
            string? thumbnailUrl = null;
            if (fileType == "photo" && compress)
            {
                thumbnailUrl = await GenerateThumbnailAsync(file, directory, fileName);
            }

            var result = new MobileFileUploadResultDto
            {
                FileId = Guid.NewGuid().ToString(),
                OriginalName = file.FileName,
                FileName = fileName,
                FileUrl = uploadResult, // uploadResult 就是文件路径
                ThumbnailUrl = thumbnailUrl,
                FileSize = file.Length,
                FileType = fileType,
                MimeType = file.ContentType,
                UploadTime = DateTime.UtcNow,
                Directory = directory
            };

            return AppResponse<MobileFileUploadResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移动端文件上传失败：{FileName}", file?.FileName);
            return AppResponse<MobileFileUploadResultDto>.Error("文件上传失败，请重试");
        }
    }

    /// <summary>
    /// 批量文件上传
    /// </summary>
    /// <param name="files">文件列表</param>
    /// <param name="fileType">文件类型</param>
    /// <param name="compress">是否压缩</param>
    /// <returns>批量上传结果</returns>
    [HttpPost("batch-upload")]
    public async Task<AppResponse<MobileBatchUploadResultDto>> BatchUploadAsync(
        [FromForm] List<IFormFile> files,
        [FromForm] string fileType = "photo",
        [FromForm] bool compress = true)
    {
        try
        {
            // 批量验证
            var totalSize = files.Sum(f => f.Length);
            if (totalSize > MAX_BATCH_SIZE)
            {
                return AppResponse<MobileBatchUploadResultDto>.Error("批量上传文件总大小不能超过50MB");
            }

            if (files.Count > 20)
            {
                return AppResponse<MobileBatchUploadResultDto>.Error("单次最多上传20个文件");
            }

            var results = new List<MobileFileUploadResultDto>();
            var errors = new List<string>();

            foreach (var file in files)
            {
                try
                {
                    var validationResult = ValidateFile(file, fileType);
                    if (!validationResult.IsValid)
                    {
                        errors.Add($"{file.FileName}: {validationResult.ErrorMessage}");
                        continue;
                    }

                    var fileName = GenerateMobileFileName(file.FileName, fileType);
                    var directory = $"mobile/{fileType}s/{DateTime.Now:yyyy/MM/dd}";

                    var fullPath = $"{directory}/{fileName}";
                    var uploadResult = await _storageService.UploadAsync(file.OpenReadStream(), fullPath);
                    
                    string? thumbnailUrl = null;
                    if (fileType == "photo" && compress)
                    {
                        thumbnailUrl = await GenerateThumbnailAsync(file, directory, fileName);
                    }

                    results.Add(new MobileFileUploadResultDto
                    {
                        FileId = Guid.NewGuid().ToString(),
                        OriginalName = file.FileName,
                        FileName = fileName,
                        FileUrl = uploadResult, // uploadResult 就是文件路径
                        ThumbnailUrl = thumbnailUrl,
                        FileSize = file.Length,
                        FileType = fileType,
                        MimeType = file.ContentType,
                        UploadTime = DateTime.UtcNow,
                        Directory = directory
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "批量上传中文件失败：{FileName}", file.FileName);
                    errors.Add($"{file.FileName}: 上传失败");
                }
            }

            var batchResult = new MobileBatchUploadResultDto
            {
                TotalCount = files.Count,
                SuccessCount = results.Count,
                FailedCount = errors.Count,
                SuccessFiles = results,
                Errors = errors,
                BatchId = Guid.NewGuid().ToString(),
                UploadTime = DateTime.UtcNow
            };

            return AppResponse<MobileBatchUploadResultDto>.Success(batchResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量文件上传失败");
            return AppResponse<MobileBatchUploadResultDto>.Error("批量上传失败，请重试");
        }
    }

    /// <summary>
    /// 检查文件是否存在（避免重复上传）
    /// </summary>
    /// <param name="fileHash">文件MD5哈希值</param>
    /// <param name="fileSize">文件大小</param>
    /// <returns>检查结果</returns>
    [HttpPost("check-exists")]
    public async Task<AppResponse<MobileFileExistsResultDto>> CheckFileExistsAsync(
        [FromBody] MobileFileExistsRequestDto request)
    {
        try
        {
            // 这里可以实现文件去重逻辑
            // 检查数据库中是否已有相同哈希值和大小的文件
            var exists = await CheckFileExistsInDatabaseAsync(request.FileHash, request.FileSize);
            
            var result = new MobileFileExistsResultDto
            {
                Exists = exists.Exists,
                FileUrl = exists.FileUrl,
                ThumbnailUrl = exists.ThumbnailUrl,
                Message = exists.Exists ? "文件已存在，无需重复上传" : "文件不存在，可以上传"
            };

            return AppResponse<MobileFileExistsResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查文件是否存在失败：Hash={Hash}", request.FileHash);
            return AppResponse<MobileFileExistsResultDto>.Error("检查文件失败");
        }
    }

    /// <summary>
    /// 获取上传进度（用于大文件分片上传）
    /// </summary>
    /// <param name="uploadId">上传任务ID</param>
    /// <returns>上传进度</returns>
    [HttpGet("upload-progress/{uploadId}")]
    public async Task<AppResponse<MobileUploadProgressDto>> GetUploadProgressAsync(string uploadId)
    {
        try
        {
            // 实现上传进度查询逻辑
            var progress = await GetUploadProgressFromCacheAsync(uploadId);
            return AppResponse<MobileUploadProgressDto>.Success(progress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取上传进度失败：UploadId={UploadId}", uploadId);
            return AppResponse<MobileUploadProgressDto>.Error("获取上传进度失败");
        }
    }

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="fileUrl">文件URL</param>
    /// <returns>删除结果</returns>
    [HttpDelete]
    [HasPermission("file:delete")]
    public async Task<AppResponse<bool>> DeleteFileAsync([FromQuery] string fileUrl)
    {
        try
        {
            await _storageService.DeleteAsync(fileUrl);
            return AppResponse<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除文件失败：FileUrl={FileUrl}", fileUrl);
            return AppResponse<bool>.Error("删除文件失败");
        }
    }

    #region 私有方法

    private (bool IsValid, string ErrorMessage) ValidateFile(IFormFile file, string fileType)
    {
        if (file == null || file.Length == 0)
        {
            return (false, "文件不能为空");
        }

        if (file.Length > MAX_FILE_SIZE)
        {
            return (false, $"文件大小不能超过{MAX_FILE_SIZE / 1024 / 1024}MB");
        }

        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        
        switch (fileType.ToLower())
        {
            case "photo":
                if (!ALLOWED_IMAGE_TYPES.Contains(extension))
                {
                    return (false, "只支持 jpg, jpeg, png, webp 格式的图片");
                }
                break;
            case "voice":
                if (!ALLOWED_AUDIO_TYPES.Contains(extension))
                {
                    return (false, "只支持 mp3, wav, m4a, aac 格式的音频");
                }
                break;
            case "signature":
                if (!ALLOWED_IMAGE_TYPES.Contains(extension))
                {
                    return (false, "签名只支持图片格式");
                }
                break;
            default:
                return (false, "不支持的文件类型");
        }

        return (true, string.Empty);
    }

    private string GenerateMobileFileName(string originalFileName, string fileType)
    {
        var extension = Path.GetExtension(originalFileName);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
        var random = Random.Shared.Next(1000, 9999);
        return $"mobile_{fileType}_{timestamp}_{random}{extension}";
    }

    private async Task<string?> GenerateThumbnailAsync(IFormFile file, string directory, string fileName)
    {
        try
        {
            // 这里可以集成图片压缩库（如 ImageSharp）
            // 生成缩略图的逻辑
            var thumbnailName = $"thumb_{fileName}";
            
            // 简化实现：直接返回原图URL
            // 实际应用中应该生成真正的缩略图
            return null; // 实际实现时返回缩略图URL
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "生成缩略图失败：{FileName}", fileName);
            return null;
        }
    }

    private async Task<(bool Exists, string? FileUrl, string? ThumbnailUrl)> CheckFileExistsInDatabaseAsync(
        string fileHash, long fileSize)
    {
        // 实现文件去重查询逻辑
        // 这里应该查询数据库中是否有相同哈希值和大小的文件
        await Task.Delay(1); // 占位实现
        return (false, null, null);
    }

    private async Task<MobileUploadProgressDto> GetUploadProgressFromCacheAsync(string uploadId)
    {
        // 实现从缓存获取上传进度的逻辑
        await Task.Delay(1); // 占位实现
        return new MobileUploadProgressDto
        {
            UploadId = uploadId,
            Progress = 100,
            Status = "completed",
            Message = "上传完成"
        };
    }

    #endregion
}

/// <summary>
/// 移动端文件上传结果DTO
/// </summary>
public class MobileFileUploadResultDto
{
    public string FileId { get; set; } = string.Empty;
    public string OriginalName { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string FileUrl { get; set; } = string.Empty;
    public string? ThumbnailUrl { get; set; }
    public long FileSize { get; set; }
    public string FileType { get; set; } = string.Empty;
    public string MimeType { get; set; } = string.Empty;
    public DateTime UploadTime { get; set; }
    public string Directory { get; set; } = string.Empty;
}

/// <summary>
/// 移动端批量上传结果DTO
/// </summary>
public class MobileBatchUploadResultDto
{
    public int TotalCount { get; set; }
    public int SuccessCount { get; set; }
    public int FailedCount { get; set; }
    public List<MobileFileUploadResultDto> SuccessFiles { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public string BatchId { get; set; } = string.Empty;
    public DateTime UploadTime { get; set; }
}

/// <summary>
/// 移动端文件存在检查请求DTO
/// </summary>
public class MobileFileExistsRequestDto
{
    public string FileHash { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileType { get; set; } = string.Empty;
}

/// <summary>
/// 移动端文件存在检查结果DTO
/// </summary>
public class MobileFileExistsResultDto
{
    public bool Exists { get; set; }
    public string? FileUrl { get; set; }
    public string? ThumbnailUrl { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// 移动端上传进度DTO
/// </summary>
public class MobileUploadProgressDto
{
    public string UploadId { get; set; } = string.Empty;
    public int Progress { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime UpdateTime { get; set; } = DateTime.UtcNow;
}