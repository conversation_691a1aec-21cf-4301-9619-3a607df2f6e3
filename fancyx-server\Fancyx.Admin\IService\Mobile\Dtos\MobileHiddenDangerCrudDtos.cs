using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Mobile.Dtos;



/// <summary>
/// 移动端更新隐患明细DTO
/// </summary>
public class MobileUpdateHiddenDangerDto
{
    /// <summary>
    /// 隐患类别
    /// </summary>
    public string? DangerCategory { get; set; }

    /// <summary>
    /// 二级分类
    /// </summary>
    public string? SecondaryCategory { get; set; }

    /// <summary>
    /// 问题级别
    /// </summary>
    public string? ProblemLevel { get; set; }

    /// <summary>
    /// 问题描述
    /// </summary>
    [StringLength(2000, ErrorMessage = "问题描述长度不能超过2000字符")]
    public string? ProblemDescription { get; set; }

    /// <summary>
    /// 现场位置描述
    /// </summary>
    [StringLength(500, ErrorMessage = "现场位置描述长度不能超过500字符")]
    public string? LocationDescription { get; set; }

    /// <summary>
    /// 新增照片信息
    /// </summary>
    public List<MobilePhotoInfoDto> NewPhotos { get; set; } = new();

    /// <summary>
    /// 要删除的照片URL
    /// </summary>
    public List<string> DeletedPhotoUrls { get; set; } = new();

    /// <summary>
    /// 新增语音记录
    /// </summary>
    public List<MobileAudioInfoDto> NewAudioRecords { get; set; } = new();

    /// <summary>
    /// 要删除的语音记录URL
    /// </summary>
    public List<string> DeletedAudioUrls { get; set; } = new();

    /// <summary>
    /// 整改建议
    /// </summary>
    [StringLength(1000, ErrorMessage = "整改建议长度不能超过1000字符")]
    public string? RectificationSuggestion { get; set; }

    /// <summary>
    /// 整改期限
    /// </summary>
    public DateTime? RectificationDeadline { get; set; }

    /// <summary>
    /// 责任部门
    /// </summary>
    [StringLength(200, ErrorMessage = "责任部门长度不能超过200字符")]
    public string? ResponsibleDepartment { get; set; }

    /// <summary>
    /// 责任人
    /// </summary>
    [StringLength(50, ErrorMessage = "责任人姓名长度不能超过50字符")]
    public string? ResponsiblePerson { get; set; }

    /// <summary>
    /// 责任人联系方式
    /// </summary>
    [Phone(ErrorMessage = "请输入正确的联系方式")]
    public string? ResponsibleContact { get; set; }

    /// <summary>
    /// 影响范围
    /// </summary>
    [StringLength(500, ErrorMessage = "影响范围描述长度不能超过500字符")]
    public string? ImpactScope { get; set; }

    /// <summary>
    /// 可能后果
    /// </summary>
    [StringLength(500, ErrorMessage = "可能后果描述长度不能超过500字符")]
    public string? PotentialConsequences { get; set; }

    /// <summary>
    /// 临时措施
    /// </summary>
    [StringLength(500, ErrorMessage = "临时措施描述长度不能超过500字符")]
    public string? TemporaryMeasures { get; set; }

    /// <summary>
    /// 相关法规标准
    /// </summary>
    [StringLength(500, ErrorMessage = "相关法规标准长度不能超过500字符")]
    public string? RelatedRegulations { get; set; }

    /// <summary>
    /// 更新标签
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 更新理由
    /// </summary>
    [StringLength(500, ErrorMessage = "更新理由长度不能超过500字符")]
    public string? UpdateReason { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();

    /// <summary>
    /// 更新备注
    /// </summary>
    [StringLength(500, ErrorMessage = "更新备注长度不能超过500字符")]
    public string? UpdateRemarks { get; set; }
}

/// <summary>
/// 移动端隐患确认DTO
/// </summary>
public class MobileConfirmHiddenDangerDto
{
    /// <summary>
    /// 确认类型（确认存在/误报/已处理）
    /// </summary>
    [Required(ErrorMessage = "确认类型不能为空")]
    public string ConfirmationType { get; set; } = string.Empty;

    /// <summary>
    /// 确认意见
    /// </summary>
    [Required(ErrorMessage = "确认意见不能为空")]
    [StringLength(1000, ErrorMessage = "确认意见长度不能超过1000字符")]
    public string ConfirmationComment { get; set; } = string.Empty;

    /// <summary>
    /// 确认时的现场照片
    /// </summary>
    public List<MobilePhotoInfoDto> ConfirmationPhotos { get; set; } = new();

    /// <summary>
    /// 确认时的GPS位置
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();

    /// <summary>
    /// 确认时间
    /// </summary>
    [Required(ErrorMessage = "确认时间不能为空")]
    public DateTime ConfirmationTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 移动端整改记录DTO
/// </summary>
public class MobileRectificationRecordDto
{
    /// <summary>
    /// 整改状态（进行中/已完成/逾期）
    /// </summary>
    [Required(ErrorMessage = "整改状态不能为空")]
    public string RectificationStatus { get; set; } = string.Empty;

    /// <summary>
    /// 整改进度（百分比）
    /// </summary>
    [Range(0, 100, ErrorMessage = "整改进度必须在0-100之间")]
    public int RectificationProgress { get; set; }

    /// <summary>
    /// 整改说明
    /// </summary>
    [Required(ErrorMessage = "整改说明不能为空")]
    [StringLength(1000, ErrorMessage = "整改说明长度不能超过1000字符")]
    public string RectificationDescription { get; set; } = string.Empty;

    /// <summary>
    /// 整改措施
    /// </summary>
    [Required(ErrorMessage = "整改措施不能为空")]
    [StringLength(1000, ErrorMessage = "整改措施长度不能超过1000字符")]
    public string RectificationMeasures { get; set; } = string.Empty;

    /// <summary>
    /// 整改后现场照片
    /// </summary>
    public List<MobilePhotoInfoDto> RectificationPhotos { get; set; } = new();

    /// <summary>
    /// 实际完成时间
    /// </summary>
    public DateTime? ActualCompletionTime { get; set; }

    /// <summary>
    /// 整改费用（可选）
    /// </summary>
    public decimal? RectificationCost { get; set; }

    /// <summary>
    /// 验收人
    /// </summary>
    [StringLength(50, ErrorMessage = "验收人姓名长度不能超过50字符")]
    public string? AcceptancePerson { get; set; }

    /// <summary>
    /// 验收时间
    /// </summary>
    public DateTime? AcceptanceTime { get; set; }

    /// <summary>
    /// 验收意见
    /// </summary>
    [StringLength(500, ErrorMessage = "验收意见长度不能超过500字符")]
    public string? AcceptanceComment { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端AI识别请求DTO
/// </summary>
public class MobileAiRecognitionRequestDto
{
    /// <summary>
    /// 识别类型（photo/audio/mixed）
    /// </summary>
    [Required(ErrorMessage = "识别类型不能为空")]
    public string RecognitionType { get; set; } = string.Empty;

    /// <summary>
    /// 照片文件列表
    /// </summary>
    public List<IFormFile> PhotoFiles { get; set; } = new();

    /// <summary>
    /// 语音文件列表
    /// </summary>
    public List<IFormFile> AudioFiles { get; set; } = new();

    /// <summary>
    /// 文本描述（辅助识别）
    /// </summary>
    [StringLength(500, ErrorMessage = "文本描述长度不能超过500字符")]
    public string? TextDescription { get; set; }

    /// <summary>
    /// 上下文信息（项目类型、工程类别等）
    /// </summary>
    public MobileRecognitionContextDto? Context { get; set; }

    /// <summary>
    /// GPS位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端AI识别结果DTO
/// </summary>
public class MobileAiRecognitionResultDto
{
    /// <summary>
    /// 识别任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 识别状态（processing/completed/failed）
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 识别进度（0-100）
    /// </summary>
    public int Progress { get; set; }

    /// <summary>
    /// 识别结果列表
    /// </summary>
    public List<MobileRecognitionItemDto> Results { get; set; } = new();

    /// <summary>
    /// 整体置信度
    /// </summary>
    public double OverallConfidence { get; set; }

    /// <summary>
    /// 建议的隐患类别
    /// </summary>
    public string? SuggestedCategory { get; set; }

    /// <summary>
    /// 建议的问题级别
    /// </summary>
    public string? SuggestedLevel { get; set; }

    /// <summary>
    /// 识别是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 置信度分数（兼容性属性）
    /// </summary>
    public float ConfidenceScore { get; set; }

    /// <summary>
    /// 识别到的隐患列表（兼容性属性）
    /// </summary>
    public List<MobileRecognizedDangerDto> RecognizedDangers { get; set; } = new();

    /// <summary>
    /// 处理时间
    /// </summary>
    public TimeSpan ProcessingTime { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 生成的问题描述
    /// </summary>
    public string? GeneratedDescription { get; set; }

    /// <summary>
    /// 建议的整改措施
    /// </summary>
    public string? SuggestedRectification { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime ProcessTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误信息（如果识别失败）
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 移动端识别上下文DTO
/// </summary>
public class MobileRecognitionContextDto
{
    /// <summary>
    /// 项目类型
    /// </summary>
    public string? ProjectType { get; set; }

    /// <summary>
    /// 工程类别
    /// </summary>
    public string? EngineeringCategory { get; set; }

    /// <summary>
    /// 检查类型
    /// </summary>
    public string? CheckType { get; set; }

    /// <summary>
    /// 工作环境
    /// </summary>
    public string? WorkEnvironment { get; set; }

    /// <summary>
    /// 历史识别数据
    /// </summary>
    public List<string> HistoricalData { get; set; } = new();
}

/// <summary>
/// 移动端识别项目DTO
/// </summary>
public class MobileRecognitionItemDto
{
    /// <summary>
    /// 识别类型（safety_violation/quality_issue/equipment_failure等）
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 置信度（0-1）
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// 位置信息（在图片中的坐标等）
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 相关标签
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// 移动端隐患创建DTO
/// </summary>
public class MobileHiddenDangerCreateDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long ProjectId { get; set; }

    /// <summary>
    /// 检查ID
    /// </summary>
    public long? CheckId { get; set; }

    /// <summary>
    /// 隐患大类
    /// </summary>
    public string DangerCategory { get; set; } = string.Empty;

    /// <summary>
    /// 问题等级
    /// </summary>
    public string ProblemLevel { get; set; } = string.Empty;

    /// <summary>
    /// 安全隐患描述
    /// </summary>
    public string SafetyHazard { get; set; } = string.Empty;

    /// <summary>
    /// 具体描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 整改要求
    /// </summary>
    public string RectificationRequirement { get; set; } = string.Empty;

    /// <summary>
    /// 可能后果
    /// </summary>
    public string PossibleConsequences { get; set; } = string.Empty;

    /// <summary>
    /// 整改建议
    /// </summary>
    public string RectificationSuggestion { get; set; } = string.Empty;

    /// <summary>
    /// 照片列表
    /// </summary>
    public List<string> Photos { get; set; } = new();

    /// <summary>
    /// 位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端隐患更新DTO
/// </summary>
public class MobileHiddenDangerUpdateDto
{
    /// <summary>
    /// 隐患大类
    /// </summary>
    public string? DangerCategory { get; set; }

    /// <summary>
    /// 问题等级
    /// </summary>
    public string? ProblemLevel { get; set; }

    /// <summary>
    /// 安全隐患描述
    /// </summary>
    public string? SafetyHazard { get; set; }

    /// <summary>
    /// 具体描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 整改要求
    /// </summary>
    public string? RectificationRequirement { get; set; }

    /// <summary>
    /// 可能后果
    /// </summary>
    public string? PossibleConsequences { get; set; }

    /// <summary>
    /// 整改建议
    /// </summary>
    public string? RectificationSuggestion { get; set; }

    /// <summary>
    /// 照片列表
    /// </summary>
    public List<string>? Photos { get; set; }

    /// <summary>
    /// 更新原因
    /// </summary>
    public string? UpdateReason { get; set; }
}

/// <summary>
/// 移动端隐患状态DTO
/// </summary>
public class MobileDangerStatusDto
{
    /// <summary>
    /// 隐患ID
    /// </summary>
    public long DangerId { get; set; }

    /// <summary>
    /// 当前状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusDescription { get; set; } = string.Empty;

    /// <summary>
    /// 是否可以编辑
    /// </summary>
    public bool CanEdit { get; set; }

    /// <summary>
    /// 是否可以删除
    /// </summary>
    public bool CanDelete { get; set; }

    /// <summary>
    /// 是否需要复查
    /// </summary>
    public bool NeedReview { get; set; }

    /// <summary>
    /// 整改进度
    /// </summary>
    public int RectificationProgress { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdateTime { get; set; }

    /// <summary>
    /// 是否正在处理中
    /// </summary>
    public bool IsProcessing { get; set; }
}

/// <summary>
/// 移动端隐患确认DTO
/// </summary>
public class MobileHiddenDangerConfirmDto
{
    /// <summary>
    /// 确认意见
    /// </summary>
    public string ConfirmComment { get; set; } = string.Empty;

    /// <summary>
    /// 确认结果（通过/驳回）
    /// </summary>
    public string ConfirmResult { get; set; } = string.Empty;

    /// <summary>
    /// 确认人
    /// </summary>
    public string ConfirmBy { get; set; } = string.Empty;

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime ConfirmTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 移动端多媒体上传DTO
/// </summary>
public class MobileMultimediaUploadDto
{
    /// <summary>
    /// 关联的隐患ID
    /// </summary>
    public long DangerId { get; set; }

    /// <summary>
    /// 文件列表
    /// </summary>
    public List<MobileFileInfoDto> Files { get; set; } = new();

    /// <summary>
    /// 上传类型（photo/voice/video）
    /// </summary>
    public string UploadType { get; set; } = string.Empty;

    /// <summary>
    /// 上传说明
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 移动端文件信息DTO
/// </summary>
public class MobileFileInfoDto
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 文件URL
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;

    /// <summary>
    /// 缩略图URL
    /// </summary>
    public string? ThumbnailUrl { get; set; }
}

/// <summary>
/// 移动端批量上传结果DTO
/// </summary>
public class MobileBatchUploadResultDto
{
    /// <summary>
    /// 总文件数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功上传数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 成功上传的文件列表
    /// </summary>
    public List<MobileFileInfoDto> SuccessFiles { get; set; } = new();

    /// <summary>
    /// 失败的文件列表
    /// </summary>
    public List<MobileUploadErrorDto> FailedFiles { get; set; } = new();

    /// <summary>
    /// 上传时间
    /// </summary>
    public DateTime UploadTime { get; set; } = DateTime.UtcNow;
}



/// <summary>
/// 移动端批量操作DTO
/// </summary>
public class MobileBatchOperationDto
{
    /// <summary>
    /// 操作类型（delete/confirm/export等）
    /// </summary>
    public string OperationType { get; set; } = string.Empty;

    /// <summary>
    /// 目标ID列表
    /// </summary>
    public List<long> TargetIds { get; set; } = new();

    /// <summary>
    /// 操作参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 操作原因
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// 操作人
    /// </summary>
    public string OperatorName { get; set; } = string.Empty;
}

/// <summary>
/// 移动端导出请求DTO
/// </summary>
public class MobileExportRequestDto
{
    /// <summary>
    /// 导出类型（excel/pdf/word）
    /// </summary>
    public string ExportType { get; set; } = string.Empty;

    /// <summary>
    /// 导出格式
    /// </summary>
    public string ExportFormat { get; set; } = string.Empty;

    /// <summary>
    /// 查询条件
    /// </summary>
    public MobileDangerQueryDto? QueryCondition { get; set; }

    /// <summary>
    /// 导出字段
    /// </summary>
    public List<string> ExportFields { get; set; } = new();

    /// <summary>
    /// 是否包含图片
    /// </summary>
    public bool IncludeImages { get; set; } = true;

    /// <summary>
    /// 模板ID
    /// </summary>
    public string? TemplateId { get; set; }
}

/// <summary>
/// 移动端统计查询DTO
/// </summary>
public class MobileStatisticsQueryDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long? ProjectId { get; set; }

    /// <summary>
    /// 统计类型（daily/weekly/monthly）
    /// </summary>
    public string StatisticsType { get; set; } = string.Empty;

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 隐患类别
    /// </summary>
    public string? DangerCategory { get; set; }

    /// <summary>
    /// 问题等级
    /// </summary>
    public string? ProblemLevel { get; set; }

    /// <summary>
    /// 分组字段
    /// </summary>
    public List<string> GroupByFields { get; set; } = new();
}





/// <summary>
/// 移动端照片信息DTO
/// </summary>
public class MobilePhotoInfoDto
{
    /// <summary>
    /// 照片URL
    /// </summary>
    [Required(ErrorMessage = "照片URL不能为空")]
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// 缩略图URL
    /// </summary>
    public string? ThumbnailUrl { get; set; }

    /// <summary>
    /// 照片描述
    /// </summary>
    [StringLength(200, ErrorMessage = "照片描述长度不能超过200字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 拍摄时间
    /// </summary>
    public DateTime CaptureTime { get; set; }

    /// <summary>
    /// 拍摄位置
    /// </summary>
    public MobileLocationDto? CaptureLocation { get; set; }

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 图片尺寸
    /// </summary>
    public string? Dimensions { get; set; }

    /// <summary>
    /// AI识别结果
    /// </summary>
    public string? AiAnalysis { get; set; }
}

/// <summary>
/// 移动端语音信息DTO
/// </summary>
public class MobileAudioInfoDto
{
    /// <summary>
    /// 语音文件URL
    /// </summary>
    [Required(ErrorMessage = "语音文件URL不能为空")]
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// 语音描述
    /// </summary>
    [StringLength(200, ErrorMessage = "语音描述长度不能超过200字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 录制时间
    /// </summary>
    public DateTime RecordTime { get; set; }

    /// <summary>
    /// 录制位置
    /// </summary>
    public MobileLocationDto? RecordLocation { get; set; }

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 时长（秒）
    /// </summary>
    public int Duration { get; set; }

    /// <summary>
    /// 语音转文本结果
    /// </summary>
    public string? TranscriptionText { get; set; }

    /// <summary>
    /// 转换置信度
    /// </summary>
    public double? TranscriptionConfidence { get; set; }
}

/// <summary>
/// 移动端批量隐患操作DTO
/// </summary>
public class MobileBatchHiddenDangerOperationDto
{
    /// <summary>
    /// 隐患ID列表
    /// </summary>
    [Required(ErrorMessage = "请选择要操作的隐患")]
    public List<long> DangerIds { get; set; } = new();

    /// <summary>
    /// 操作类型（delete/export/confirm/rectify/close）
    /// </summary>
    [Required(ErrorMessage = "请选择操作类型")]
    public string OperationType { get; set; } = string.Empty;

    /// <summary>
    /// 操作参数（JSON格式）
    /// </summary>
    public string? OperationParams { get; set; }

    /// <summary>
    /// 操作原因
    /// </summary>
    [StringLength(500, ErrorMessage = "操作原因长度不能超过500字符")]
    public string? Reason { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端批量多媒体上传DTO
/// </summary>
public class MobileBatchMultimediaUploadDto
{
    /// <summary>
    /// 关联隐患ID
    /// </summary>
    [Required(ErrorMessage = "关联隐患ID不能为空")]
    public long HiddenDangerId { get; set; }

    /// <summary>
    /// 照片文件列表
    /// </summary>
    public List<IFormFile> PhotoFiles { get; set; } = new();

    /// <summary>
    /// 语音文件列表
    /// </summary>
    public List<IFormFile> AudioFiles { get; set; } = new();

    /// <summary>
    /// 是否启用AI识别
    /// </summary>
    public bool EnableAiRecognition { get; set; } = true;

    /// <summary>
    /// 是否压缩文件
    /// </summary>
    public bool CompressFiles { get; set; } = true;

    /// <summary>
    /// 媒体文件描述列表
    /// </summary>
    public List<string>? Descriptions { get; set; }

    /// <summary>
    /// 上传位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端多媒体上传结果DTO
/// </summary>
public class MobileMultimediaUploadResultDto
{
    /// <summary>
    /// 成功上传的照片列表
    /// </summary>
    public List<MobilePhotoInfoDto> SuccessPhotos { get; set; } = new();

    /// <summary>
    /// 成功上传的语音列表
    /// </summary>
    public List<MobileAudioInfoDto> SuccessAudios { get; set; } = new();

    /// <summary>
    /// 失败的文件列表
    /// </summary>
    public List<MobileUploadErrorDto> FailedFiles { get; set; } = new();

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 上传批次ID
    /// </summary>
    public string BatchId { get; set; } = string.Empty;

    /// <summary>
    /// AI识别任务ID（如果启用了AI识别）
    /// </summary>
    public string? AiTaskId { get; set; }

    /// <summary>
    /// 上传时间
    /// </summary>
    public DateTime UploadTime { get; set; } = DateTime.UtcNow;
}