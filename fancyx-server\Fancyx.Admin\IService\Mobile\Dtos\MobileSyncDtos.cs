namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端同步数据DTO
/// </summary>
public class MobileSyncDataDto
{
    /// <summary>
    /// 客户端时间戳
    /// </summary>
    public DateTime ClientTimestamp { get; set; }

    /// <summary>
    /// 同步的检查任务
    /// </summary>
    public List<MobileSyncCheckDto> CheckTasks { get; set; } = new();

    /// <summary>
    /// 同步的隐患记录
    /// </summary>
    public List<MobileSyncDangerDto> DangerRecords { get; set; } = new();

    /// <summary>
    /// 需要上传的文件列表
    /// </summary>
    public List<MobileSyncFileDto> FileUploads { get; set; } = new();

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端同步检查DTO
/// </summary>
public class MobileSyncCheckDto
{
    /// <summary>
    /// 客户端临时ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 服务端检查ID（如果已存在）
    /// </summary>
    public long? ServerId { get; set; }

    /// <summary>
    /// 检查数据
    /// </summary>
    public MobileCheckCompleteDto CheckData { get; set; } = new();

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModifiedTime { get; set; }

    /// <summary>
    /// 同步状态（pending/synced/conflict）
    /// </summary>
    public string SyncStatus { get; set; } = string.Empty;
}

/// <summary>
/// 移动端同步隐患DTO
/// </summary>
public class MobileSyncDangerDto
{
    /// <summary>
    /// 客户端临时ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 服务端隐患ID（如果已存在）
    /// </summary>
    public Guid? ServerId { get; set; }

    /// <summary>
    /// 隐患数据
    /// </summary>
    public MobileCreateHiddenDangerDto DangerData { get; set; } = new();

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModifiedTime { get; set; }

    /// <summary>
    /// 同步状态
    /// </summary>
    public string SyncStatus { get; set; } = string.Empty;
}

/// <summary>
/// 移动端同步文件DTO
/// </summary>
public class MobileSyncFileDto
{
    /// <summary>
    /// 客户端文件ID
    /// </summary>
    public string ClientFileId { get; set; } = string.Empty;

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件路径（本地路径）
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    public string FileType { get; set; } = string.Empty;

    /// <summary>
    /// 文件MD5
    /// </summary>
    public string? FileMD5 { get; set; }

    /// <summary>
    /// 关联的记录ID
    /// </summary>
    public string? RelatedRecordId { get; set; }

    /// <summary>
    /// 关联的记录类型（check/danger）
    /// </summary>
    public string? RelatedRecordType { get; set; }

    /// <summary>
    /// 上传状态
    /// </summary>
    public string UploadStatus { get; set; } = string.Empty;
}

/// <summary>
/// 移动端同步结果DTO
/// </summary>
public class MobileSyncResultDto
{
    /// <summary>
    /// 同步是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 同步的记录数量
    /// </summary>
    public int SyncedCount { get; set; }

    /// <summary>
    /// 失败的记录数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 服务器时间戳
    /// </summary>
    public DateTime ServerTimestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 同步消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误详情
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 冲突的记录
    /// </summary>
    public List<MobileSyncConflictDto> Conflicts { get; set; } = new();
}

/// <summary>
/// 移动端同步冲突DTO
/// </summary>
public class MobileSyncConflictDto
{
    /// <summary>
    /// 客户端记录ID
    /// </summary>
    public string ClientRecordId { get; set; } = string.Empty;

    /// <summary>
    /// 服务端记录ID
    /// </summary>
    public string ServerRecordId { get; set; } = string.Empty;

    /// <summary>
    /// 冲突类型
    /// </summary>
    public string ConflictType { get; set; } = string.Empty;

    /// <summary>
    /// 冲突描述
    /// </summary>
    public string ConflictDescription { get; set; } = string.Empty;

    /// <summary>
    /// 客户端数据
    /// </summary>
    public object? ClientData { get; set; }

    /// <summary>
    /// 服务端数据
    /// </summary>
    public object? ServerData { get; set; }
}
