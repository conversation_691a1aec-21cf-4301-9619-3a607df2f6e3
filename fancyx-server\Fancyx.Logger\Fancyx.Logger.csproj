﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
		<PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Fancyx.Cap\Fancyx.Cap.csproj" />
	  <ProjectReference Include="..\Fancyx.Core\Fancyx.Core.csproj" />
	  <ProjectReference Include="..\Fancyx.Repository\Fancyx.Repository.csproj" />
	</ItemGroup>
</Project>
