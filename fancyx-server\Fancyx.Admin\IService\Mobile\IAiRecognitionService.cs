using Fancyx.Admin.IService.Mobile.Dtos;

namespace Fancyx.Admin.IService.Mobile
{
    /// <summary>
    /// AI识别服务接口
    /// </summary>
    public interface IAiRecognitionService
    {
        /// <summary>
        /// 处理移动端AI识别请求
        /// </summary>
        /// <param name="recognitionDto">识别请求</param>
        /// <returns>识别结果</returns>
        Task<MobileAiRecognitionResultDto> ProcessMobileRecognitionAsync(MobileAiRecognitionDto recognitionDto);

        /// <summary>
        /// 获取AI识别任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态</returns>
        Task<MobileAiRecognitionResultDto> GetRecognitionTaskStatusAsync(string taskId);
    }
}
