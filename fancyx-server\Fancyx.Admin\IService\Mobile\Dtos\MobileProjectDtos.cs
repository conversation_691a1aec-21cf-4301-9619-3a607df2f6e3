namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端项目查询DTO
/// </summary>
public class MobileProjectQueryDto : PageSearch
{
    /// <summary>
    /// 项目名称（关键字搜索）
    /// </summary>
    public string? ProjectName { get; set; }
    
    /// <summary>
    /// 所属区域
    /// </summary>
    public string? Region { get; set; }
    
    /// <summary>
    /// 项目状态（进行中/已完成/暂停）
    /// </summary>
    public string? Status { get; set; }
    
    /// <summary>
    /// 负责人ID（当前用户相关项目）
    /// </summary>
    public long? ManagerId { get; set; }
}

/// <summary>
/// 移动端项目列表DTO（精简版）
/// </summary>
public class MobileProjectListDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 项目编号
    /// </summary>
    public string ProjectNo { get; set; } = string.Empty;
    
    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;
    
    /// <summary>
    /// 所属区域
    /// </summary>
    public string Region { get; set; } = string.Empty;
    
    /// <summary>
    /// 项目类型
    /// </summary>
    public string ProjectType { get; set; } = string.Empty;
    
    /// <summary>
    /// 现场负责人
    /// </summary>
    public string SiteManager { get; set; } = string.Empty;
    
    /// <summary>
    /// 负责人电话
    /// </summary>
    public string ManagerPhone { get; set; } = string.Empty;
    
    /// <summary>
    /// 项目状态
    /// </summary>
    public string Status { get; set; } = string.Empty;
    
    /// <summary>
    /// 项目进度
    /// </summary>
    public string Progress { get; set; } = string.Empty;
    
    /// <summary>
    /// 隐患统计
    /// </summary>
    public MobileProjectDangerStats DangerStats { get; set; } = new();
    
    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime? LastCheckTime { get; set; }
}

/// <summary>
/// 移动端项目详情DTO
/// </summary>
public class MobileProjectDetailDto
{
    /// <summary>
    /// 项目基本信息
    /// </summary>
    public MobileProjectListDto BasicInfo { get; set; } = new();
    
    /// <summary>
    /// 项目地址
    /// </summary>
    public string ProjectAddress { get; set; } = string.Empty;
    
    /// <summary>
    /// 工程类型
    /// </summary>
    public string EngineeringType { get; set; } = string.Empty;
    
    /// <summary>
    /// 工程类别
    /// </summary>
    public string EngineeringCategory { get; set; } = string.Empty;
    
    /// <summary>
    /// 建设单位
    /// </summary>
    public string ConstructionUnit { get; set; } = string.Empty;
    
    /// <summary>
    /// 施工单位
    /// </summary>
    public string ConstructionCompany { get; set; } = string.Empty;
    
    /// <summary>
    /// 监理单位
    /// </summary>
    public string SupervisionUnit { get; set; } = string.Empty;
    
    /// <summary>
    /// 开工日期
    /// </summary>
    public DateTime? StartDate { get; set; }
    
    /// <summary>
    /// 竣工日期
    /// </summary>
    public DateTime? EndDate { get; set; }
    
    /// <summary>
    /// 最近检查记录
    /// </summary>
    public List<MobileRecentCheckDto> RecentChecks { get; set; } = new();
    
    /// <summary>
    /// 项目统计信息
    /// </summary>
    public MobileProjectStatisticsDto Statistics { get; set; } = new();
}

/// <summary>
/// 移动端项目隐患统计
/// </summary>
public class MobileProjectDangerStats
{
    /// <summary>
    /// 总隐患数
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// 待整改数量
    /// </summary>
    public int PendingCount { get; set; }
    
    /// <summary>
    /// 重大隐患数量
    /// </summary>
    public int MajorCount { get; set; }
    
    /// <summary>
    /// 较大隐患数量
    /// </summary>
    public int ModerateCount { get; set; }
    
    /// <summary>
    /// 一般隐患数量
    /// </summary>
    public int MinorCount { get; set; }
}

/// <summary>
/// 移动端项目统计DTO
/// </summary>
public class MobileProjectStatisticsDto
{
    /// <summary>
    /// 总项目数
    /// </summary>
    public int TotalProjects { get; set; }

    /// <summary>
    /// 活跃项目数
    /// </summary>
    public int ActiveProjects { get; set; }

    /// <summary>
    /// 已完成项目数
    /// </summary>
    public int CompletedProjects { get; set; }

    /// <summary>
    /// 待处理项目数
    /// </summary>
    public int PendingProjects { get; set; }

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; }

    /// <summary>
    /// 检查次数统计
    /// </summary>
    public MobileCheckCountStats CheckStats { get; set; } = new();

    /// <summary>
    /// 隐患统计
    /// </summary>
    public MobileProjectDangerStats DangerStats { get; set; } = new();

    /// <summary>
    /// 整改完成率
    /// </summary>
    public decimal CompletionRate { get; set; }

    /// <summary>
    /// 本月检查趋势
    /// </summary>
    public List<MobileDailyCheckTrendDto> CheckTrend { get; set; } = new();
}

/// <summary>
/// 移动端检查统计
/// </summary>
public class MobileCheckCountStats
{
    /// <summary>
    /// 总检查次数
    /// </summary>
    public int TotalChecks { get; set; }
    
    /// <summary>
    /// 本月检查次数
    /// </summary>
    public int MonthlyChecks { get; set; }
    
    /// <summary>
    /// 本周检查次数
    /// </summary>
    public int WeeklyChecks { get; set; }
}



/// <summary>
/// 移动端最近检查DTO
/// </summary>
public class MobileRecentCheckDto
{
    /// <summary>
    /// 检查ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 检查日期
    /// </summary>
    public DateTime CheckDate { get; set; }
    
    /// <summary>
    /// 检查类型
    /// </summary>
    public string CheckType { get; set; } = string.Empty;
    
    /// <summary>
    /// 二级分类
    /// </summary>
    public string SecondaryCategory { get; set; } = string.Empty;
    
    /// <summary>
    /// 检查组长
    /// </summary>
    public string CheckLeader { get; set; } = string.Empty;
    
    /// <summary>
    /// 发现隐患数量
    /// </summary>
    public int DangerCount { get; set; }
    
    /// <summary>
    /// 检查状态
    /// </summary>
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// 移动端日检查趋势DTO
/// </summary>
public class MobileDailyCheckTrendDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }
    
    /// <summary>
    /// 检查次数
    /// </summary>
    public int CheckCount { get; set; }
    
    /// <summary>
    /// 发现隐患数量
    /// </summary>
    public int DangerCount { get; set; }
}





