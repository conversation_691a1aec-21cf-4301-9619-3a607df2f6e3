using AutoMapper;
using Fancyx.Admin.Entities.Project;
using Fancyx.Admin.IService.Project.Dtos;

namespace Fancyx.Admin.Profiles
{
    /// <summary>
    /// 项目映射配置
    /// </summary>
    public class ProjectAutoMapperProfile : Profile
    {
        public ProjectAutoMapperProfile()
        {
            // Entity to DTO
            CreateMap<ProjectDO, ProjectDto>();
            CreateMap<ProjectDO, ProjectListDto>();
            CreateMap<ProjectCheckDO, ProjectCheckDto>();
            CreateMap<ProjectCheckDO, ProjectCheckListDto>();
            CreateMap<HiddenDangerDO, HiddenDangerDto>();
            CreateMap<HiddenDangerDO, HiddenDangerListDto>();

            // DTO to Entity
            CreateMap<CreateProjectDto, ProjectDO>();
            CreateMap<UpdateProjectDto, ProjectDO>();
            CreateMap<CreateProjectCheckDto, ProjectCheckDO>();
            CreateMap<UpdateProjectCheckDto, ProjectCheckDO>();
            CreateMap<CreateHiddenDangerDto, HiddenDangerDO>();
            CreateMap<UpdateHiddenDangerDto, HiddenDangerDO>();
        }
    }
}