import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';

const SystemIndex: React.FC = () => {
  const location = useLocation();

  // 如果访问 /system 根路径，重定向到用户管理
  React.useEffect(() => {
    if (location.pathname === '/system') {
      window.location.replace('/system/user');
    }
  }, [location.pathname]);

  return (
    <div className="system-container">
      <Outlet />
    </div>
  );
};

export default SystemIndex;