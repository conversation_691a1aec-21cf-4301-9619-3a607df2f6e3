import httpClient from '@/utils/httpClient';

// 项目检查列表查询
export function getProjectCheckList(params: any) {
  return httpClient.get('/api/project-check/list', { params });
}

// 获取项目检查详情
export function getProjectCheckById(id: number) {
  return httpClient.get(`/api/project-check/${id}`);
}

// 创建项目检查
export function createProjectCheck(data: any) {
  return httpClient.post('/api/project-check', data);
}

// 更新项目检查
export function updateProjectCheck(id: number, data: any) {
  return httpClient.put(`/api/project-check/${id}`, data);
}

// 删除项目检查
export function deleteProjectCheck(id: number) {
  return httpClient.delete(`/api/project-check/${id}`);
} 