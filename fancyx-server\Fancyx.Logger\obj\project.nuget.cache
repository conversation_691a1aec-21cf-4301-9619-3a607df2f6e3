{"version": 2, "dgSpecHash": "uOEiDwtQvu4=", "success": true, "projectFilePath": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Logger\\Fancyx.Logger.csproj", "expectedPackageFiles": ["D:\\开发环境\\nuget\\autofac\\8.2.0\\autofac.8.2.0.nupkg.sha512", "D:\\开发环境\\nuget\\autofac.extensions.dependencyinjection\\10.0.0\\autofac.extensions.dependencyinjection.10.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\autofac.extras.dynamicproxy\\7.1.0\\autofac.extras.dynamicproxy.7.1.0.nupkg.sha512", "D:\\开发环境\\nuget\\automapper\\12.0.1\\automapper.12.0.1.nupkg.sha512", "D:\\开发环境\\nuget\\automapper.extensions.microsoft.dependencyinjection\\12.0.1\\automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512", "D:\\开发环境\\nuget\\castle.core\\5.2.1\\castle.core.5.2.1.nupkg.sha512", "D:\\开发环境\\nuget\\castle.core.asyncinterceptor\\2.1.0\\castle.core.asyncinterceptor.2.1.0.nupkg.sha512", "D:\\开发环境\\nuget\\consul\\1.7.14.4\\consul.1.7.14.4.nupkg.sha512", "D:\\开发环境\\nuget\\dotnetcore.cap\\8.3.4\\dotnetcore.cap.8.3.4.nupkg.sha512", "D:\\开发环境\\nuget\\dotnetcore.cap.dashboard\\8.3.4\\dotnetcore.cap.dashboard.8.3.4.nupkg.sha512", "D:\\开发环境\\nuget\\dotnetcore.cap.postgresql\\8.3.4\\dotnetcore.cap.postgresql.8.3.4.nupkg.sha512", "D:\\开发环境\\nuget\\dotnetcore.cap.redisstreams\\8.3.4\\dotnetcore.cap.redisstreams.8.3.4.nupkg.sha512", "D:\\开发环境\\nuget\\freeredis\\1.3.6\\freeredis.1.3.6.nupkg.sha512", "D:\\开发环境\\nuget\\freescheduler\\2.0.33\\freescheduler.2.0.33.nupkg.sha512", "D:\\开发环境\\nuget\\freesql\\3.5.209\\freesql.3.5.209.nupkg.sha512", "D:\\开发环境\\nuget\\freesql.cloud\\2.0.1\\freesql.cloud.2.0.1.nupkg.sha512", "D:\\开发环境\\nuget\\freesql.dbcontext\\3.5.209\\freesql.dbcontext.3.5.209.nupkg.sha512", "D:\\开发环境\\nuget\\freesql.provider.postgresql\\3.5.209\\freesql.provider.postgresql.3.5.209.nupkg.sha512", "D:\\开发环境\\nuget\\idlebus\\1.5.3\\idlebus.1.5.3.nupkg.sha512", "D:\\开发环境\\nuget\\ip2region.net\\2.0.2\\ip2region.net.2.0.2.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.aspnetcore.authentication.jwtbearer\\8.0.6\\microsoft.aspnetcore.authentication.jwtbearer.8.0.6.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.aspnetcore.hosting.abstractions\\2.3.0\\microsoft.aspnetcore.hosting.abstractions.2.3.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.aspnetcore.hosting.server.abstractions\\2.3.0\\microsoft.aspnetcore.hosting.server.abstractions.2.3.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.aspnetcore.http.abstractions\\2.3.0\\microsoft.aspnetcore.http.abstractions.2.3.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.aspnetcore.http.extensions\\2.3.0\\microsoft.aspnetcore.http.extensions.2.3.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.aspnetcore.http.features\\2.3.0\\microsoft.aspnetcore.http.features.2.3.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.aspnetcore.jsonpatch\\8.0.14\\microsoft.aspnetcore.jsonpatch.8.0.14.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.aspnetcore.mvc.newtonsoftjson\\8.0.14\\microsoft.aspnetcore.mvc.newtonsoftjson.8.0.14.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.aspnetcore.staticfiles\\2.3.0\\microsoft.aspnetcore.staticfiles.2.3.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.entityframeworkcore\\6.0.36\\microsoft.entityframeworkcore.6.0.36.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.entityframeworkcore.abstractions\\6.0.36\\microsoft.entityframeworkcore.abstractions.6.0.36.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.entityframeworkcore.analyzers\\6.0.36\\microsoft.entityframeworkcore.analyzers.6.0.36.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.entityframeworkcore.relational\\6.0.36\\microsoft.entityframeworkcore.relational.6.0.36.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.caching.abstractions\\6.0.1\\microsoft.extensions.caching.abstractions.6.0.1.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.caching.memory\\6.0.3\\microsoft.extensions.caching.memory.6.0.3.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.configuration.binder\\8.0.0\\microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.dependencyinjection\\9.0.0\\microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.dependencyinjection.abstractions\\9.0.0\\microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.dependencymodel\\9.0.3\\microsoft.extensions.dependencymodel.9.0.3.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.diagnostics.abstractions\\8.0.1\\microsoft.extensions.diagnostics.abstractions.8.0.1.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.fileproviders.physical\\8.0.0\\microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.filesystemglobbing\\8.0.0\\microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.hosting.abstractions\\8.0.1\\microsoft.extensions.hosting.abstractions.8.0.1.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.logging\\8.0.0\\microsoft.extensions.logging.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.logging.abstractions\\8.0.2\\microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.options\\8.0.2\\microsoft.extensions.options.8.0.2.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.extensions.webencoders\\8.0.11\\microsoft.extensions.webencoders.8.0.11.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.identitymodel.abstractions\\7.1.2\\microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.identitymodel.jsonwebtokens\\7.1.2\\microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.identitymodel.logging\\7.1.2\\microsoft.identitymodel.logging.7.1.2.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.identitymodel.protocols\\7.1.2\\microsoft.identitymodel.protocols.7.1.2.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.identitymodel.protocols.openidconnect\\7.1.2\\microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.identitymodel.tokens\\7.1.2\\microsoft.identitymodel.tokens.7.1.2.nupkg.sha512", "D:\\开发环境\\nuget\\microsoft.net.http.headers\\2.3.0\\microsoft.net.http.headers.2.3.0.nupkg.sha512", "D:\\开发环境\\nuget\\nettopologysuite\\2.0.0\\nettopologysuite.2.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\nettopologysuite.io.postgis\\2.1.0\\nettopologysuite.io.postgis.2.1.0.nupkg.sha512", "D:\\开发环境\\nuget\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "D:\\开发环境\\nuget\\newtonsoft.json.bson\\1.0.2\\newtonsoft.json.bson.1.0.2.nupkg.sha512", "D:\\开发环境\\nuget\\npgsql\\8.0.5\\npgsql.8.0.5.nupkg.sha512", "D:\\开发环境\\nuget\\npgsql.legacypostgis\\5.0.18\\npgsql.legacypostgis.5.0.18.nupkg.sha512", "D:\\开发环境\\nuget\\npgsql.nettopologysuite\\5.0.18\\npgsql.nettopologysuite.5.0.18.nupkg.sha512", "D:\\开发环境\\nuget\\pipelines.sockets.unofficial\\2.2.8\\pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "D:\\开发环境\\nuget\\serilog\\3.1.1\\serilog.3.1.1.nupkg.sha512", "D:\\开发环境\\nuget\\serilog.aspnetcore\\8.0.0\\serilog.aspnetcore.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\serilog.extensions.hosting\\8.0.0\\serilog.extensions.hosting.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\serilog.extensions.logging\\8.0.0\\serilog.extensions.logging.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\serilog.formatting.compact\\2.0.0\\serilog.formatting.compact.2.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\serilog.settings.configuration\\8.0.0\\serilog.settings.configuration.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\serilog.sinks.async\\1.5.0\\serilog.sinks.async.1.5.0.nupkg.sha512", "D:\\开发环境\\nuget\\serilog.sinks.console\\5.0.0\\serilog.sinks.console.5.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\serilog.sinks.debug\\2.0.0\\serilog.sinks.debug.2.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\serilog.sinks.file\\5.0.0\\serilog.sinks.file.5.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\stackexchange.redis\\2.8.16\\stackexchange.redis.2.8.16.nupkg.sha512", "D:\\开发环境\\nuget\\system.buffers\\4.6.0\\system.buffers.4.6.0.nupkg.sha512", "D:\\开发环境\\nuget\\system.collections.immutable\\6.0.1\\system.collections.immutable.6.0.1.nupkg.sha512", "D:\\开发环境\\nuget\\system.diagnostics.diagnosticsource\\8.0.1\\system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512", "D:\\开发环境\\nuget\\system.diagnostics.eventlog\\6.0.0\\system.diagnostics.eventlog.6.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\system.identitymodel.tokens.jwt\\7.1.2\\system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512", "D:\\开发环境\\nuget\\system.io.pipelines\\5.0.1\\system.io.pipelines.5.0.1.nupkg.sha512", "D:\\开发环境\\nuget\\system.memory\\4.5.3\\system.memory.4.5.3.nupkg.sha512", "D:\\开发环境\\nuget\\system.text.encodings.web\\8.0.0\\system.text.encodings.web.8.0.0.nupkg.sha512", "D:\\开发环境\\nuget\\uaparser\\3.1.47\\uaparser.3.1.47.nupkg.sha512", "D:\\开发环境\\nuget\\workqueue\\1.3.0\\workqueue.1.3.0.nupkg.sha512"], "logs": []}