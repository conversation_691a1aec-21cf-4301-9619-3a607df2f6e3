﻿"restore":{"projectUniqueName":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj","projectName":"Fancyx.Repository","projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\Fancyx.Repository.csproj","outputPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Repository\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"FreeSql":{"target":"Package","version":"[3.5.209, )"},"FreeSql.Cloud":{"target":"Package","version":"[2.0.1, )"},"FreeSql.DbContext":{"target":"Package","version":"[3.5.209, )"},"FreeSql.Provider.PostgreSQL":{"target":"Package","version":"[3.5.209, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}