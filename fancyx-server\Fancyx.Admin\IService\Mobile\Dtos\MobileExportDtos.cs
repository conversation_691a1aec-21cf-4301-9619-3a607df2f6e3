namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端导出结果DTO
/// </summary>
public class MobileExportResultDto
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件URL
    /// </summary>
    public string FileUrl { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 导出时间
    /// </summary>
    public DateTime ExportTime { get; set; }

    /// <summary>
    /// 导出状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息（如果导出失败）
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 导出是否成功
    /// </summary>
    public bool Success { get; set; } = true;

    /// <summary>
    /// 导出记录数量
    /// </summary>
    public int RecordCount { get; set; }

    /// <summary>
    /// 导出消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
}



