using Microsoft.AspNetCore.Mvc;
using Fancyx.Admin.IService.Project;
using Fancyx.Admin.IService.Mobile.Dtos;
using Fancyx.Core.Attributes;
using Fancyx.Shared.Models;
using System.Text.RegularExpressions;

namespace Fancyx.Admin.Controllers.Mobile;

/// <summary>
/// 移动端项目管理API
/// </summary>
[ApiController]
[Route("api/mobile/v1/projects")]
public class MobileProjectController : ControllerBase
{
    private readonly IProjectService _projectService;
    private readonly ILogger<MobileProjectController> _logger;

    public MobileProjectController(IProjectService projectService, ILogger<MobileProjectController> logger)
    {
        _projectService = projectService;
        _logger = logger;
    }

    /// <summary>
    /// 获取项目简要列表（移动端专用）
    /// </summary>
    /// <param name="queryDto">查询参数</param>
    /// <returns>项目简要信息列表</returns>
    [HttpGet("simple")]
    public async Task<AppResponse<PagedResult<MobileProjectListDto>>> GetSimpleProjectListAsync([FromQuery] MobileProjectQueryDto queryDto)
    {
        try
        {
            var result = await _projectService.GetMobileProjectListAsync(queryDto);
            return AppResponse<PagedResult<MobileProjectListDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取移动端项目列表失败");
            return AppResponse<PagedResult<MobileProjectListDto>>.Error("获取项目列表失败");
        }
    }

    /// <summary>
    /// 获取项目详情（移动端优化）
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>项目详情</returns>
    [HttpGet("{id}/detail")]
    public async Task<AppResponse<MobileProjectDetailDto>> GetProjectDetailAsync(long id)
    {
        try
        {
            var result = await _projectService.GetMobileProjectDetailAsync(id);
            return AppResponse<MobileProjectDetailDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目详情失败，项目ID：{ProjectId}", id);
            return AppResponse<MobileProjectDetailDto>.Error("获取项目详情失败");
        }
    }

    /// <summary>
    /// 移动端创建项目
    /// </summary>
    /// <param name="createDto">创建参数</param>
    /// <returns>项目ID</returns>
    [HttpPost]
    [HasPermission("project:create")]
    public async Task<AppResponse<long>> CreateProjectAsync([FromBody] MobileCreateProjectDto createDto)
    {
        try
        {
            // 验证必填字段
            var validationResult = ValidateProjectData(createDto);
            if (!validationResult.IsValid)
            {
                return AppResponse<long>.Error(validationResult.ErrorMessage);
            }

            // 验证GPS位置信息
            if (createDto.Location != null && !IsValidLocation(createDto.Location))
            {
                return AppResponse<long>.Error("GPS位置信息无效");
            }

            var result = await _projectService.CreateMobileProjectAsync(createDto);
            return AppResponse<long>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移动端创建项目失败");
            return AppResponse<long>.Error("创建项目失败，请重试");
        }
    }

    /// <summary>
    /// 移动端更新项目
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <param name="updateDto">更新参数</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    [HasPermission("project:update")]
    public async Task<AppResponse<bool>> UpdateProjectAsync(long id, [FromBody] MobileUpdateProjectDto updateDto)
    {
        try
        {
            var validationResult = ValidateProjectUpdateData(updateDto);
            if (!validationResult.IsValid)
            {
                return AppResponse<bool>.Error(validationResult.ErrorMessage);
            }

            var result = await _projectService.UpdateMobileProjectAsync(id, updateDto);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移动端更新项目失败，项目ID：{ProjectId}", id);
            return AppResponse<bool>.Error("更新项目失败");
        }
    }

    /// <summary>
    /// 移动端删除项目
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <param name="deleteReason">删除原因</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [HasPermission("project:delete")]
    public async Task<AppResponse<bool>> DeleteProjectAsync(long id, [FromQuery] string? deleteReason = null)
    {
        try
        {
            // 检查项目是否有关联的检查任务
            var hasChecks = await _projectService.HasActiveChecksAsync(id);
            if (hasChecks)
            {
                return AppResponse<bool>.Error("项目存在活跃的检查任务，无法删除");
            }

            var result = await _projectService.DeleteMobileProjectAsync(id, deleteReason);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移动端删除项目失败，项目ID：{ProjectId}", id);
            return AppResponse<bool>.Error("删除项目失败");
        }
    }

    /// <summary>
    /// 快速创建检查任务（移动端专用）
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <param name="createDto">创建参数</param>
    /// <returns>检查任务ID</returns>
    [HttpPost("{id}/quick-check")]
    [HasPermission("check:create")]
    public async Task<AppResponse<long>> QuickCreateCheckAsync(long id, [FromBody] MobileQuickCheckDto createDto)
    {
        try
        {
            var result = await _projectService.QuickCreateCheckAsync(id, createDto);
            return AppResponse<long>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "快速创建检查任务失败，项目ID：{ProjectId}", id);
            return AppResponse<long>.Error("创建检查任务失败");
        }
    }

    /// <summary>
    /// 获取项目统计信息
    /// </summary>
    /// <param name="id">项目ID</param>
    /// <returns>统计信息</returns>
    [HttpGet("{id}/statistics")]
    public async Task<AppResponse<MobileProjectStatisticsDto>> GetProjectStatisticsAsync(long id)
    {
        try
        {
            var result = await _projectService.GetMobileProjectStatisticsAsync(id);
            return AppResponse<MobileProjectStatisticsDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目统计信息失败，项目ID：{ProjectId}", id);
            return AppResponse<MobileProjectStatisticsDto>.Error("获取统计信息失败");
        }
    }

    /// <summary>
    /// 批量操作项目
    /// </summary>
    /// <param name="batchDto">批量操作参数</param>
    /// <returns>操作结果</returns>
    [HttpPost("batch-operation")]
    [HasPermission("project:batch")]
    public async Task<AppResponse<MobileBatchOperationResultDto>> BatchOperationAsync([FromBody] MobileBatchProjectOperationDto batchDto)
    {
        try
        {
            if (batchDto.ProjectIds == null || batchDto.ProjectIds.Count == 0)
            {
                return AppResponse<MobileBatchOperationResultDto>.Error("请选择要操作的项目");
            }

            if (batchDto.ProjectIds.Count > 50)
            {
                return AppResponse<MobileBatchOperationResultDto>.Error("单次最多操作50个项目");
            }

            var result = await _projectService.BatchOperationMobileProjectsAsync(batchDto);
            return AppResponse<MobileBatchOperationResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量操作项目失败");
            return AppResponse<MobileBatchOperationResultDto>.Error("批量操作失败");
        }
    }

    /// <summary>
    /// 同步项目到移动端
    /// </summary>
    /// <param name="syncDto">同步参数</param>
    /// <returns>同步结果</returns>
    [HttpPost("sync")]
    public async Task<AppResponse<MobileProjectSyncResultDto>> SyncProjectsAsync([FromBody] MobileProjectSyncRequestDto syncDto)
    {
        try
        {
            var result = await _projectService.SyncMobileProjectsAsync(syncDto);
            return AppResponse<MobileProjectSyncResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步项目失败");
            return AppResponse<MobileProjectSyncResultDto>.Error("同步失败");
        }
    }

    #region 私有方法

    private (bool IsValid, string ErrorMessage) ValidateProjectData(MobileCreateProjectDto createDto)
    {
        if (string.IsNullOrWhiteSpace(createDto.ProjectName))
            return (false, "项目名称不能为空");

        if (string.IsNullOrWhiteSpace(createDto.ProjectAddress))
            return (false, "项目地址不能为空");

        if (string.IsNullOrWhiteSpace(createDto.Region))
            return (false, "所属区域不能为空");

        if (string.IsNullOrWhiteSpace(createDto.ProjectType))
            return (false, "项目类型不能为空");

        if (string.IsNullOrWhiteSpace(createDto.SiteManager))
            return (false, "现场负责人不能为空");

        if (string.IsNullOrWhiteSpace(createDto.ManagerPhone))
            return (false, "负责人电话不能为空");

        // 验证手机号格式
        if (!Regex.IsMatch(createDto.ManagerPhone, @"^1[3-9]\d{9}$"))
            return (false, "请输入正确的手机号码");

        return (true, string.Empty);
    }

    private (bool IsValid, string ErrorMessage) ValidateProjectUpdateData(MobileUpdateProjectDto updateDto)
    {
        // 更新时的验证逻辑（可以比创建时宽松）
        if (!string.IsNullOrWhiteSpace(updateDto.ManagerPhone) &&
            !Regex.IsMatch(updateDto.ManagerPhone, @"^1[3-9]\d{9}$"))
            return (false, "请输入正确的手机号码");

        return (true, string.Empty);
    }

    private bool IsValidLocation(MobileLocationDto location)
    {
        return location.Latitude >= -90 && location.Latitude <= 90 &&
               location.Longitude >= -180 && location.Longitude <= 180 &&
               location.Accuracy > 0;
    }

    #endregion
}