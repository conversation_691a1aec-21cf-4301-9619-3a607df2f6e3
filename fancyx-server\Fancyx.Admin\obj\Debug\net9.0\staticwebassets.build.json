{"Version": 1, "Hash": "Oq1vX6R19ln9Y8aCApbmTPtdks39O8MLgbCHS28Xjm0=", "Source": "Fancyx.Admin", "BasePath": "_content/Fancyx.Admin", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Fancyx.Admin\\wwwroot", "Source": "Fancyx.Admin", "ContentRoot": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\wwwroot\\", "BasePath": "_content/Fancyx.Admin", "Pattern": "**"}], "Assets": [{"Identity": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\wwwroot\\avatar\\female.png", "SourceId": "Fancyx.Admin", "SourceType": "Discovered", "ContentRoot": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\wwwroot\\", "BasePath": "_content/Fancyx.Admin", "RelativePath": "avatar/female#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jpo3bp7plx", "Integrity": "1z6NCY+ulYkG6MWc2qDfiphlBqzicMk0408cegG9GrY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\avatar\\female.png", "FileLength": 6504, "LastWriteTime": "2025-07-15T08:47:09+00:00"}, {"Identity": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\wwwroot\\avatar\\male.png", "SourceId": "Fancyx.Admin", "SourceType": "Discovered", "ContentRoot": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\wwwroot\\", "BasePath": "_content/Fancyx.Admin", "RelativePath": "avatar/male#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "arke4br1v2", "Integrity": "ah+Oyo7u1oO3l5c1yXWGQ0btO+Io1gSCgHqlSo2mwcg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\avatar\\male.png", "FileLength": 10521, "LastWriteTime": "2025-07-15T08:47:09+00:00"}], "Endpoints": [{"Route": "avatar/female.jpo3bp7plx.png", "AssetFile": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\wwwroot\\avatar\\female.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6504"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1z6NCY+ulYkG6MWc2qDfiphlBqzicMk0408cegG9GrY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:47:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jpo3bp7plx"}, {"Name": "label", "Value": "avatar/female.png"}, {"Name": "integrity", "Value": "sha256-1z6NCY+ulYkG6MWc2qDfiphlBqzicMk0408cegG9GrY="}]}, {"Route": "avatar/female.png", "AssetFile": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\wwwroot\\avatar\\female.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6504"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1z6NCY+ulYkG6MWc2qDfiphlBqzicMk0408cegG9GrY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:47:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1z6NCY+ulYkG6MWc2qDfiphlBqzicMk0408cegG9GrY="}]}, {"Route": "avatar/male.arke4br1v2.png", "AssetFile": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\wwwroot\\avatar\\male.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10521"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ah+Oyo7u1oO3l5c1yXWGQ0btO+Io1gSCgHqlSo2mwcg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:47:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "arke4br1v2"}, {"Name": "label", "Value": "avatar/male.png"}, {"Name": "integrity", "Value": "sha256-ah+Oyo7u1oO3l5c1yXWGQ0btO+Io1gSCgHqlSo2mwcg="}]}, {"Route": "avatar/male.png", "AssetFile": "D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Admin\\wwwroot\\avatar\\male.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10521"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ah+Oyo7u1oO3l5c1yXWGQ0btO+Io1gSCgHqlSo2mwcg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:47:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ah+Oyo7u1oO3l5c1yXWGQ0btO+Io1gSCgHqlSo2mwcg="}]}]}