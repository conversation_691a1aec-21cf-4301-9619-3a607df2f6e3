using Fancyx.Shared.Models;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 隐患明细查询条件 DTO
    /// </summary>
    public class HiddenDangerQueryDto : PageSearch
    {
        /// <summary>
        /// 检查ID
        /// </summary>
        public long? ProjectCheckId { get; set; }

        /// <summary>
        /// 隐患大类
        /// </summary>
        public string DangerCategory { get; set; }

        /// <summary>
        /// 问题等级
        /// </summary>
        public string ProblemLevel { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 是否需要复查
        /// </summary>
        public bool? NeedReview { get; set; }
    }
}