/*
 核心系统表结构脚本 - SQL Server版本
 
 Source Server Type    : PostgreSQL
 Target Server Type    : SQL Server
 File Encoding         : UTF-8
 
 Date: 2025/07/29
 包含菜单、角色、用户等核心系统表
*/

-- ============================
-- 菜单管理表
-- ============================

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_menu]') AND type in (N'U'))
DROP TABLE [dbo].[sys_menu]
GO

CREATE TABLE [dbo].[sys_menu] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [title] nvarchar(32) NOT NULL,
  [icon] nvarchar(64) NULL,
  [path] nvarchar(256) NULL,
  [component] nvarchar(256) NULL,
  [menu_type] int NOT NULL,
  [permission] nvarchar(128) NOT NULL,
  [parent_id] uniqueidentifier NULL,
  [sort] int NOT NULL,
  [display] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'显示标题/名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'title'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'图标', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'icon'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'路由/地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'path'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'组件地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'component'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'功能类型', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'menu_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'授权码', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'permission'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'父级ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'parent_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'sort'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否隐藏', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'display'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 通知管理表
-- ============================

-- ----------------------------
-- Table structure for sys_notification
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_notification]') AND type in (N'U'))
DROP TABLE [dbo].[sys_notification]
GO

CREATE TABLE [dbo].[sys_notification] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [title] nvarchar(100) NOT NULL,
  [description] nvarchar(500) NULL,
  [employee_id] uniqueidentifier NOT NULL,
  [is_readed] bit NOT NULL,
  [readed_time] datetime2(6) NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知标题', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'title'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知员工', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'employee_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否已读(1已读0未读)', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'is_readed'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'已读时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'readed_time'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 角色管理表
-- ============================

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role]') AND type in (N'U'))
DROP TABLE [dbo].[sys_role]
GO

CREATE TABLE [dbo].[sys_role] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [role_name] nvarchar(64) NOT NULL,
  [remark] nvarchar(512) NULL,
  [power_data_type] int NOT NULL,
  [tenant_id] nvarchar(18) NULL,
  [is_enabled] bit NOT NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'role_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'数据权限类型', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'power_data_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否启用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'is_enabled'
GO

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role_menu]') AND type in (N'U'))
DROP TABLE [dbo].[sys_role_menu]
GO

CREATE TABLE [dbo].[sys_role_menu] (
  [id] uniqueidentifier NOT NULL,
  [menu_id] uniqueidentifier NOT NULL,
  [role_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色菜单表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_menu'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_menu', @level2type=N'COLUMN', @level2name=N'menu_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_menu', @level2type=N'COLUMN', @level2name=N'role_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_menu', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role_dept]') AND type in (N'U'))
DROP TABLE [dbo].[sys_role_dept]
GO

CREATE TABLE [dbo].[sys_role_dept] (
  [id] uniqueidentifier NOT NULL,
  [role_id] uniqueidentifier NOT NULL,
  [dept_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色部门关联表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept', @level2type=N'COLUMN', @level2name=N'role_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept', @level2type=N'COLUMN', @level2name=N'dept_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 用户管理表
-- ============================

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_user]') AND type in (N'U'))
DROP TABLE [dbo].[sys_user]
GO

CREATE TABLE [dbo].[sys_user] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [user_name] nvarchar(32) NOT NULL,
  [password] nvarchar(512) NOT NULL,
  [password_salt] nvarchar(256) NOT NULL,
  [avatar] nvarchar(256) NULL,
  [nick_name] nvarchar(64) NOT NULL,
  [sex] int NOT NULL,
  [is_enabled] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'user_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'密码', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'password'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'密码盐', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'password_salt'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'头像', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'avatar'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'昵称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'nick_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'性别', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'sex'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否启用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'is_enabled'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_user_role]') AND type in (N'U'))
DROP TABLE [dbo].[sys_user_role]
GO

CREATE TABLE [dbo].[sys_user_role] (
  [id] uniqueidentifier NOT NULL,
  [user_id] uniqueidentifier NOT NULL,
  [role_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户角色关联表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user_role'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user_role', @level2type=N'COLUMN', @level2name=N'user_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user_role', @level2type=N'COLUMN', @level2name=N'role_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user_role', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_tenant
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_tenant]') AND type in (N'U'))
DROP TABLE [dbo].[sys_tenant]
GO

CREATE TABLE [dbo].[sys_tenant] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(255) NOT NULL,
  [tenant_id] nvarchar(18) NOT NULL,
  [remark] nvarchar(512) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户主键ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户标识', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant', @level2type=N'COLUMN', @level2name=N'remark'
GO

-- ============================
-- 主键约束
-- ============================
ALTER TABLE [dbo].[sys_menu] ADD CONSTRAINT [PK_sys_menu] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_notification] ADD CONSTRAINT [PK_sys_notification] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_role] ADD CONSTRAINT [PK_sys_role] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_role_menu] ADD CONSTRAINT [PK_sys_role_menu] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_role_dept] ADD CONSTRAINT [PK_sys_role_dept] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_user] ADD CONSTRAINT [PK_sys_user] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_user_role] ADD CONSTRAINT [PK_sys_user_role] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_tenant] ADD CONSTRAINT [PK_sys_tenant] PRIMARY KEY ([id])
GO

-- ============================
-- 索引
-- ============================
CREATE UNIQUE NONCLUSTERED INDEX [IX_sys_tenant_tenant_id] ON [dbo].[sys_tenant] ([tenant_id] ASC)
GO
