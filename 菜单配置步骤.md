# 通过管理后台添加项目菜单配置

## 1. 添加项目管理文件夹
- 菜单名称：项目管理
- 菜单类型：文件夹 (1)
- 路由地址：/project
- 图标：project
- 父级菜单：无（顶级）
- 排序：10
- 是否显示：是
- 组件地址：留空

## 2. 添加项目列表菜单
- 菜单名称：项目列表
- 菜单类型：菜单 (2)
- 路由地址：/project/list
- 图标：list
- 父级菜单：项目管理
- 排序：1
- 是否显示：是
- 组件地址：pages/project/list
- 权限码：Project.List

## 3. 添加项目编辑菜单（重要！）
- 菜单名称：项目编辑
- 菜单类型：菜单 (2)
- 路由地址：/project/edit
- 图标：edit
- 父级菜单：项目管理
- 排序：2
- 是否显示：否（不在侧边栏显示，但需要路由）
- 组件地址：pages/project/edit
- 权限码：Project.Edit

## 4. 添加项目详情菜单
- 菜单名称：项目详情
- 菜单类型：菜单 (2)
- 路由地址：/project/detail/:id
- 图标：detail
- 父级菜单：项目管理
- 排序：3
- 是否显示：否
- 组件地址：pages/project/detail
- 权限码：Project.Detail

## 5. 添加按钮权限
为项目列表页面的操作按钮添加权限控制：
- 新增项目按钮：权限码 Project.Add
- 编辑项目按钮：权限码 Project.Update
- 删除项目按钮：权限码 Project.Delete
- 查看项目按钮：权限码 Project.View

## 6. 分配权限给角色
在角色管理中，将新添加的菜单权限分配给相应的角色。

## 注意事项
1. **路由地址必须准确**：确保与前端路由完全匹配
2. **组件地址格式**：使用相对路径，如 `pages/project/edit`
3. **Display字段**：编辑和详情页面设为false，不在侧边栏显示
4. **权限分配**：添加菜单后记得给用户角色分配相应权限