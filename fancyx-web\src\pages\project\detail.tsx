// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Descriptions, <PERSON><PERSON>, Card } from 'antd';
import { getProjectById } from '@/api/project';
import { useParams, useNavigate } from 'react-router-dom';

const ProjectDetail: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [data, setData] = useState<any>({});

  useEffect(() => {
    if (id) {
      getProjectById(Number(id)).then(res => {
        setData(res.data || {});
      });
    }
  }, [id]);

  return (
    <Card bordered={false} style={{ maxWidth: 800, margin: '0 auto' }}>
      <Descriptions title="项目详情" bordered column={2}>
        <Descriptions.Item label="项目编号">{data.projectNo}</Descriptions.Item>
        <Descriptions.Item label="项目名称">{data.projectName}</Descriptions.Item>
        <Descriptions.Item label="项目地址">{data.projectAddress}</Descriptions.Item>
        <Descriptions.Item label="所属区域">{data.region}</Descriptions.Item>
        <Descriptions.Item label="项目类型">{data.projectType}</Descriptions.Item>
        <Descriptions.Item label="关联合同">{data.relatedContract}</Descriptions.Item>
        <Descriptions.Item label="负责人">{data.siteManager}</Descriptions.Item>
        <Descriptions.Item label="负责人电话">{data.managerPhone}</Descriptions.Item>
        <Descriptions.Item label="负责人职位">{data.managerPosition}</Descriptions.Item>
        <Descriptions.Item label="工程类型">{data.engineeringType}</Descriptions.Item>
        <Descriptions.Item label="工程类别">{data.engineeringCategory}</Descriptions.Item>
        <Descriptions.Item label="建设单位">{data.constructionUnit}</Descriptions.Item>
        <Descriptions.Item label="监理单位">{data.supervisionUnit}</Descriptions.Item>
        <Descriptions.Item label="设计单位">{data.designUnit}</Descriptions.Item>
        <Descriptions.Item label="施工单位">{data.constructionCompany}</Descriptions.Item>
        <Descriptions.Item label="施工许可证号">{data.constructionPermitNo}</Descriptions.Item>
        <Descriptions.Item label="危化品从业人数">{data.hazardousChemicalWorkers}</Descriptions.Item>
        <Descriptions.Item label="备注">{data.remarks}</Descriptions.Item>
        <Descriptions.Item label="开工日期">{data.startDate}</Descriptions.Item>
        <Descriptions.Item label="竣工日期">{data.endDate}</Descriptions.Item>
        <Descriptions.Item label="创建时间">{data.creationTime}</Descriptions.Item>
        <Descriptions.Item label="创建人">{data.creatorName}</Descriptions.Item>
      </Descriptions>
      <div style={{ marginTop: 24, textAlign: 'center' }}>
        <Button onClick={() => navigate('/project/list')}>返回列表</Button>
      </div>
    </Card>
  );
};

export default ProjectDetail; 