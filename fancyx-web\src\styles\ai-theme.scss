// AI科技风格主题配置
$ai-primary: #7E57C2;
$ai-secondary: #9575CD;
$ai-accent: #673AB7;

// AI科技背景色系
$ai-bg-start: #1a1a2e;
$ai-bg-middle: #16213e;
$ai-bg-end: #0f3460;
$ai-bg-overlay: rgba(126, 87, 194, 0.8);

// 科技霓虹色彩
$ai-neon-blue: #00f5ff;
$ai-electric-purple: #7c4dff;
$ai-glow-pink: #ff6ec7;
$ai-tech-green: #00ff88;

// 文字颜色
$ai-text-primary: #ffffff;
$ai-text-secondary: rgba(255, 255, 255, 0.8);
$ai-text-muted: rgba(255, 255, 255, 0.6);
$ai-text-dark: #4A4A4A;

// 动画配置
$ai-transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
$ai-transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$ai-transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

// 发光效果
$ai-glow-primary: 0 0 20px rgba(126, 87, 194, 0.3);
$ai-glow-neon: 0 0 15px rgba(0, 245, 255, 0.4);
$ai-glow-electric: 0 0 25px rgba(124, 77, 255, 0.5);

// 边框圆角
$ai-border-radius-sm: 4px;
$ai-border-radius-md: 8px;
$ai-border-radius-lg: 12px;
$ai-border-radius-xl: 16px;

// 间距系统
$ai-spacing-xs: 4px;
$ai-spacing-sm: 8px;
$ai-spacing-md: 16px;
$ai-spacing-lg: 24px;
$ai-spacing-xl: 32px;
$ai-spacing-xxl: 48px;

// 字体大小
$ai-font-xs: 12px;
$ai-font-sm: 14px;
$ai-font-md: 16px;
$ai-font-lg: 18px;
$ai-font-xl: 22px;
$ai-font-xxl: 28px;
$ai-font-title: 32px;

// 阴影系统
$ai-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
$ai-shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
$ai-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
$ai-shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.25);

// 粒子动画配置
$ai-particle-count: 50;
$ai-particle-speed: 20s;
$ai-particle-size-min: 1px;
$ai-particle-size-max: 3px;

// 渐变定义
$ai-gradient-primary: linear-gradient(135deg, $ai-bg-start 0%, $ai-bg-middle 50%, $ai-bg-end 100%);
$ai-gradient-overlay: linear-gradient(135deg, rgba(126, 87, 194, 0.9) 0%, rgba(103, 58, 183, 0.7) 100%);
$ai-gradient-button: linear-gradient(135deg, $ai-primary 0%, $ai-accent 100%);
$ai-gradient-neon: linear-gradient(90deg, $ai-neon-blue 0%, $ai-electric-purple 50%, $ai-glow-pink 100%);