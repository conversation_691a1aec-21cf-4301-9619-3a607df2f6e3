{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Fancyx.Redis/1.0.0": {"dependencies": {"Fancyx.Core": "1.0.0", "FreeRedis": "1.3.7", "RedLock.net": "2.3.2"}, "runtime": {"Fancyx.Redis.dll": {}}}, "Autofac/8.2.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net8.0/Autofac.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.0.0"}}}, "Autofac.Extensions.DependencyInjection/10.0.0": {"dependencies": {"Autofac": "8.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.0.0"}}}, "Autofac.Extras.DynamicProxy/7.1.0": {"dependencies": {"Autofac": "8.2.0", "Castle.Core": "5.2.1"}, "runtime": {"lib/net6.0/Autofac.Extras.DynamicProxy.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.0.0"}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "Castle.Core/5.2.1": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"dependencies": {"Castle.Core": "5.2.1"}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FreeRedis/1.3.7": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/netstandard2.0/FreeRedis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "IP2Region.Net/2.0.2": {"runtime": {"lib/net7.0/IP2Region.Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.6": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.624.26909"}}}, "Microsoft.AspNetCore.JsonPatch/8.0.14": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1425.11221"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.14": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.14", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1425.11221"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {}, "Microsoft.Extensions.DependencyModel/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.3", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging/2.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.NETCore.Platforms/2.0.0": {}, "Microsoft.Win32.Registry/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "Pipelines.Sockets.Unofficial/1.0.7": {"dependencies": {"System.Buffers": "4.4.0", "System.IO.Pipelines": "4.5.1"}, "runtime": {"lib/netcoreapp2.1/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.7.56431"}}}, "RedLock.net/2.3.2": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "Microsoft.Extensions.Logging": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "StackExchange.Redis": "2.0.513"}, "runtime": {"lib/netstandard2.0/RedLockNet.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/netstandard2.0/RedLockNet.SERedis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StackExchange.Redis/2.0.513": {"dependencies": {"Pipelines.Sockets.Unofficial": "1.0.7", "System.Diagnostics.PerformanceCounter": "4.5.0", "System.IO.Pipelines": "4.5.1", "System.Threading.Channels": "4.5.0"}, "runtime": {"lib/netstandard2.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.513.63329"}}}, "System.Buffers/4.4.0": {}, "System.Configuration.ConfigurationManager/4.5.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.5.0", "System.Security.Permissions": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {}, "System.Diagnostics.EventLog/6.0.0": {}, "System.Diagnostics.PerformanceCounter/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "Microsoft.Win32.Registry": "4.5.0", "System.Configuration.ConfigurationManager": "4.5.0", "System.Security.Principal.Windows": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "System.IO.Pipelines/4.5.1": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Security.Principal.Windows": "4.5.0"}}, "System.Security.Cryptography.ProtectedData/4.5.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Permissions/4.5.0": {"dependencies": {"System.Security.AccessControl": "4.5.0"}, "runtime": {"lib/netstandard2.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.26515.6"}}}, "System.Security.Principal.Windows/4.5.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.0.0"}}, "System.Threading.Channels/4.5.0": {}, "UAParser/3.1.47": {"runtime": {"lib/netcoreapp2.0/UAParser.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Fancyx.Core/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "Autofac": "8.2.0", "Autofac.Extensions.DependencyInjection": "10.0.0", "Autofac.Extras.DynamicProxy": "7.1.0", "Castle.Core": "5.2.1", "Castle.Core.AsyncInterceptor": "2.1.0", "IP2Region.Net": "2.0.2", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.6", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.14", "Microsoft.Extensions.DependencyModel": "9.0.3", "Newtonsoft.Json": "13.0.3", "UAParser": "3.1.47"}, "runtime": {"Fancyx.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Fancyx.Redis/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Autofac/8.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-T+4+W4byzyUOarCbIcFRbxpYKC+cndfQm/+VeWpB60P2MCN0JMsewUhZqvH5Ooe936HQjn5uHvEY9tq6BfbiIw==", "path": "autofac/8.2.0", "hashPath": "autofac.8.2.0.nupkg.sha512"}, "Autofac.Extensions.DependencyInjection/10.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZjR/onUlP7BzQ7VBBigQepWLAyAzi3VRGX3pP6sBqkPRiT61fsTZqbTpRUKxo30TMgbs1o3y6bpLbETix4SJog==", "path": "autofac.extensions.dependencyinjection/10.0.0", "hashPath": "autofac.extensions.dependencyinjection.10.0.0.nupkg.sha512"}, "Autofac.Extras.DynamicProxy/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Da6Szv7A1LK/cTdeoyqS45zb/BC5vep8+86f6C1oh2UhZaYtiijlNfLWamp3lxe0uUQ33kFe1dDCjsvfwJWzLg==", "path": "autofac.extras.dynamicproxy/7.1.0", "hashPath": "autofac.extras.dynamicproxy.7.1.0.nupkg.sha512"}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "Castle.Core/5.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-wHARzQA695jwwKreOzNsq54KiGqKP38tv8hi8e2FXDEC/sA6BtrX90tVPDkOfVu13PbEzr00TCV8coikl+D1Iw==", "path": "castle.core/5.2.1", "hashPath": "castle.core.5.2.1.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "path": "castle.core.asyncinterceptor/2.1.0", "hashPath": "castle.core.asyncinterceptor.2.1.0.nupkg.sha512"}, "FreeRedis/1.3.7": {"type": "package", "serviceable": true, "sha512": "sha512-hGd+3nKU5zA8vcUUMB0yCRTClqIg+Ll0P8x5dsaEuBCQH3P5rNjK0Vj3zBG+XIYwOsGhUg+KG3T0fYgh0pdK4g==", "path": "freeredis/1.3.7", "hashPath": "freeredis.1.3.7.nupkg.sha512"}, "IP2Region.Net/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-vkxITIPkUdju0+vPm9FYU4+sBvvOkoM7GvPw8R81RYlbkKDwKAkO9P4vZvzeRJEC8SrYtMLcsDJizx+e/JnRmA==", "path": "ip2region.net/2.0.2", "hashPath": "ip2region.net.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-devoUZd8YqikCheBVYpIyvp9psM2Y2ZhOvq1zL2YSjIoq3FUQH8LpLkGak+8oAi/5DGqX8KWyLpZJSUXtOAVCw==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.6", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/8.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-S0ZPiwLvRCYWYz7WTxaTZIyBEYOTPR+Rujdiwz4gUGauWpAhXu846I29xjOTBPX1d5HUcBNlJ46cgDDAev94Gg==", "path": "microsoft.aspnetcore.jsonpatch/8.0.14", "hashPath": "microsoft.aspnetcore.jsonpatch.8.0.14.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-322+FAPeMslfd320h8sASJSJ3h5s4dVr6W0xypr28Vz/cbMFjVCLHOou418cc2Xh0kXRzMy3irD3jBzSA+EiDA==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/8.0.14", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.8.0.14.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Am6l4Vpn3/K32daEqZI+FFr96OlZkgwK2LcT3pZ2zWubR5zTPW3/FkO1Rat9kb7oQOa4rxgl9LJHc5tspCWfg==", "path": "microsoft.bcl.asyncinterfaces/1.1.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-194P+NOekDXrPHmM2R8678T4bRfZ2isQXDDAsXKE5qI0QLUnXbwB0upljAkyxk+Kkt1DV1tV+9tHOtHEEh3ksw==", "path": "microsoft.extensions.dependencymodel/9.0.3", "hashPath": "microsoft.extensions.dependencymodel.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VP10syWV/vxYYMKgZ2eDESmUsz3gPxvBn5J6tkVN8lI4M+dF43RN8fWsEPbcAneDmZrHl3Pv23z05nmyGkJlpg==", "path": "microsoft.extensions.logging/2.0.0", "hashPath": "microsoft.extensions.logging.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6ZCllUYGFukkymSTx3Yr0G/ajRxoNJp7/FqSxSB4fGISST54ifBhgu4Nc0ItGi3i6DqwuNd8SUyObmiC++AO2Q==", "path": "microsoft.extensions.logging.abstractions/2.0.0", "hashPath": "microsoft.extensions.logging.abstractions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "path": "microsoft.identitymodel.abstractions/7.1.2", "hashPath": "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "path": "microsoft.identitymodel.logging/7.1.2", "hashPath": "microsoft.identitymodel.logging.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "path": "microsoft.identitymodel.tokens/7.1.2", "hashPath": "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VdLJOCXhZaEMY7Hm2GKiULmn7IEPFE4XC5LPSfBVCUIA8YLZVh846gtfBJalsPQF2PlzdD7ecX7DZEulJ402ZQ==", "path": "microsoft.netcore.platforms/2.0.0", "hashPath": "microsoft.netcore.platforms.2.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-+FWlwd//+Tt56316p00hVePBCouXyEzT86Jb3+AuRotTND0IYn0OO3obs1gnQEs/txEnt+rF2JBGLItTG+Be6A==", "path": "microsoft.win32.registry/4.5.0", "hashPath": "microsoft.win32.registry.4.5.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/1.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-n+YKaKVd9ZSa7uVeT0gyOT8eoeCWbiTdpVtnHkjthqmNopVPc4+efRQUReO+WH3WFyVKxgn5QS4BLi2Q+sioqA==", "path": "pipelines.sockets.unofficial/1.0.7", "hashPath": "pipelines.sockets.unofficial.1.0.7.nupkg.sha512"}, "RedLock.net/2.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-jlrALAArm4dCE292U3EtRoMnVKJ9M6sunbZn/oG5OuzlGtTpusXBfvDrnGWbgGDlWV027f5E9H5CiVnPxiq8+g==", "path": "redlock.net/2.3.2", "hashPath": "redlock.net.2.3.2.nupkg.sha512"}, "StackExchange.Redis/2.0.513": {"type": "package", "serviceable": true, "sha512": "sha512-Prh45gWLOE1FYHe40sJj+s7Ray31ZEtcBfoNv8C85a59FTY9zKYOv/kPEeOYRV4CKXIa7MGoc0OUlp9xX8jN5g==", "path": "stackexchange.redis/2.0.513", "hashPath": "stackexchange.redis.2.0.513.nupkg.sha512"}, "System.Buffers/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "path": "system.buffers/4.4.0", "hashPath": "system.buffers.4.4.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UIFvaFfuKhLr9u5tWMxmVoDPkFeD+Qv8gUuap4aZgVGYSYMdERck4OhLN/2gulAc0nYTEigWXSJNNWshrmxnng==", "path": "system.configuration.configurationmanager/4.5.0", "hashPath": "system.configuration.configurationmanager.4.5.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-JUO5/moXgchWZBMBElgmPebZPKCgwW8kY3dFwVJavaNR2ftcc/YjXXGjOaCjly2KBXT7Ld5l/GTkMVzNv41yZA==", "path": "system.diagnostics.performancecounter/4.5.0", "hashPath": "system.diagnostics.performancecounter.4.5.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "path": "system.identitymodel.tokens.jwt/7.1.2", "hashPath": "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512"}, "System.IO.Pipelines/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-oY5m31iOIZhvW0C69YTdKQWIbiYjFLgxn9NFXA7XeWD947uEk0zOi9fLbGtYgbs1eF7kTQ4zl9IeGQHthz+m+A==", "path": "system.io.pipelines/4.5.1", "hashPath": "system.io.pipelines.4.5.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vW8Eoq0TMyz5vAG/6ce483x/CP83fgm4SJe5P8Tb1tZaobcvPrbMEL7rhH1DRdrYbbb6F0vq3OlzmK0Pkwks5A==", "path": "system.security.accesscontrol/4.5.0", "hashPath": "system.security.accesscontrol.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "path": "system.security.cryptography.protecteddata/4.5.0", "hashPath": "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512"}, "System.Security.Permissions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "path": "system.security.permissions/4.5.0", "hashPath": "system.security.permissions.4.5.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-U77HfRXlZlOeIXd//Yoj6Jnk8AXlbeisf1oq1os+hxOGVnuG+lGSfGqTwTZBoORFF6j/0q7HXIl8cqwQ9aUGqQ==", "path": "system.security.principal.windows/4.5.0", "hashPath": "system.security.principal.windows.4.5.0.nupkg.sha512"}, "System.Threading.Channels/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEH06N0rIGmRT4LOKQ2BmUO0IxfvmIY/PaouSq+DFQku72OL8cxfw8W99uGpTCFf2vx2QHLRSh374iSM3asdTA==", "path": "system.threading.channels/4.5.0", "hashPath": "system.threading.channels.4.5.0.nupkg.sha512"}, "UAParser/3.1.47": {"type": "package", "serviceable": true, "sha512": "sha512-I68Jl/Vs5RQZdz9BbmYtnXgujg0jVd61LhKbyNZOCm9lBxZFGxLbiQo6yFj21VYi7DzPvEvrVOmeC6v41AoLfw==", "path": "uaparser/3.1.47", "hashPath": "uaparser.3.1.47.nupkg.sha512"}, "Fancyx.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}