﻿"restore":{"projectUniqueName":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj","projectName":"Fancyx.Cap","projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\Fancyx.Cap.csproj","outputPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Cap\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"DotNetCore.CAP":{"target":"Package","version":"[8.3.4, )"},"DotNetCore.CAP.Dashboard":{"target":"Package","version":"[8.3.4, )"},"DotNetCore.CAP.PostgreSql":{"target":"Package","version":"[8.3.4, )"},"DotNetCore.CAP.RedisStreams":{"target":"Package","version":"[8.3.4, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}