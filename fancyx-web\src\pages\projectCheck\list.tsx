// @ts-nocheck
import React, { useRef } from 'react';
import { Button, Modal, message, Form, Input, DatePicker } from 'antd';
import SmartTable from '@/components/SmartTable';
import type { SmartTableRef } from '@/components/SmartTable/type';
import { getProjectCheckList, deleteProjectCheck } from '@/api/projectCheck';
import { useNavigate } from 'react-router-dom';

const ProjectCheckList: React.FC = () => {
  const tableRef = useRef<SmartTableRef>(null);
  const navigate = useNavigate();

  // 删除项目检查
  const handleDelete = (id: number) => {
    Modal.confirm({
      title: '确认删除该项目检查？',
      onOk: async () => {
        await deleteProjectCheck(id);
        message.success('删除成功');
        tableRef.current?.reload();
      },
    });
  };

  // 新增隐患明细
  const handleCreateHiddenDanger = (record: any) => {
    navigate(`/hidden-danger/edit?projectCheckId=${record.id}&checkInfo=${encodeURIComponent(record.projectName + ' - ' + record.checkType)}`);
  };

  // 点击行进入明细页面
  const handleRowClick = (record: any) => {
    navigate(`/project-check/detail/${record.id}`);
  };

  // 表格列定义
  const columns = [
    { title: '项目名称', dataIndex: 'projectName', key: 'projectName' },
    { title: '检查类型', dataIndex: 'checkType', key: 'checkType' },
    { title: '二级分类', dataIndex: 'secondaryCategory', key: 'secondaryCategory' },
    { title: '检查日期', dataIndex: 'checkDate', key: 'checkDate' },
    { title: '检查组长', dataIndex: 'checkLeader', key: 'checkLeader' },
    { title: '委托单位', dataIndex: 'clientUnit', key: 'clientUnit' },
    { title: '创建时间', dataIndex: 'creationTime', key: 'creationTime' },
    { title: '创建人', dataIndex: 'creatorName', key: 'creatorName' },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <>
          <Button type="link" onClick={(e) => { e.stopPropagation(); navigate(`/project-check/edit/${record.id}`); }}>编辑</Button>
          <Button type="link" onClick={(e) => { e.stopPropagation(); handleCreateHiddenDanger(record); }}>新增隐患明细</Button>
          <Button type="link" danger onClick={(e) => { e.stopPropagation(); handleDelete(record.id); }}>删除</Button>
        </>
      ),
    },
  ];

  // 查询项
  const searchItems: React.ReactNode[] = [
    <Form.Item name="projectName" label="项目名称" key="projectName">
      <Input placeholder="请输入项目名称" />
    </Form.Item>,
    <Form.Item name="checkType" label="检查类型" key="checkType">
      <Input placeholder="请输入检查类型" />
    </Form.Item>,
    <Form.Item name="checkLeader" label="检查组长" key="checkLeader">
      <Input placeholder="请输入检查组长" />
    </Form.Item>,
    <Form.Item name="checkDate" label="检查日期" key="checkDate">
      <DatePicker.RangePicker />
    </Form.Item>,
  ];

  // 工具栏
  const toolbar: React.ReactNode[] = [
    <Button type="primary" key="add" onClick={() => navigate('/project-check/edit')}>新增检查</Button>,
  ];

  return (
    <SmartTable
      ref={tableRef}
      columns={columns}
      request={getProjectCheckList}
      searchItems={searchItems}
      toolbar={toolbar}
      rowKey="id"
      onRow={(record) => ({
        onClick: () => handleRowClick(record),
        style: { cursor: 'pointer' }
      })}
    />
  );
};

export default ProjectCheckList; 