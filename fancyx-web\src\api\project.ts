import httpClient from '@/utils/httpClient';

// 项目列表查询
export function getProjectList(params: any) {
  return httpClient.get('/api/Project/list', { params });
}

// 获取项目详情
export function getProjectById(id: number) {
  return httpClient.get(`/api/Project/${id}`);
}

// 创建项目
export function createProject(data: any) {
  return httpClient.post('/api/Project', data);
}

// 更新项目
export function updateProject(id: number, data: any) {
  return httpClient.put(`/api/Project/${id}`, data);
}

// 删除项目
export function deleteProject(id: number) {
  return httpClient.delete(`/api/Project/${id}`);
}

// 生成项目编号
export function generateProjectNo() {
  return httpClient.get('/api/Project/generate-no');
} 