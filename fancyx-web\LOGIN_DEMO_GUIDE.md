# 大模型隐患识别系统 - 登录页面演示指南

## 🚀 快速开始

### 1. 启动项目
```bash
yarn dev
```

### 2. 访问登录页面
打开浏览器访问：`http://localhost:8081`

## 🔐 演示登录

由于后端API服务未运行，系统已配置模拟登录功能：

### 演示账号
- **用户名**: `admin`
- **密码**: `123456`

### 其他测试场景
- 输入错误密码可以测试错误处理动画
- 留空字段可以测试表单验证
- 可以测试"记住密码"功能

## 🎨 页面特性展示

### AI科技风格设计
- ✨ 深蓝紫色渐变背景
- 🌟 20个动态粒子动画
- 💫 霓虹发光效果
- 🔮 现代化毛玻璃效果

### 交互动画
- 📱 页面加载淡入动画
- ⚡ 输入框聚焦发光效果
- 🎯 按钮悬停和点击动画
- 🔄 登录加载状态动画
- ❌ 错误时的抖动反馈

### 响应式设计
- 💻 桌面端：完整双栏布局
- 📱 移动端：单栏适配布局
- 🎮 触摸友好的交互元素

## 🛠️ 技术特性

### 性能优化
- 硬件加速动画
- 内存优化的粒子系统
- 响应式图片加载
- CSS变量系统

### 可访问性
- 键盘导航支持
- 屏幕阅读器兼容
- 高对比度模式
- 减少动画偏好支持

### 浏览器兼容性
- Chrome 91+
- Firefox 89+
- Safari 14.1+
- Edge 91+

## 🔧 开发说明

### 模拟登录模式
- 开发环境下自动启用
- API连接失败时自动降级
- 生产环境下禁用

### 代理配置
项目已配置Vite代理解决CORS问题：
```typescript
proxy: {
  '/api': {
    target: 'http://localhost:5000',
    changeOrigin: true,
    secure: false,
  },
}
```

### 环境变量
```env
VITE_API_BASE_URL=          # 使用相对路径利用代理
VITE_OSS_DOMAIN=http://localhost:5000/
```

## 📱 移动端测试

### Chrome DevTools
1. 按F12打开开发者工具
2. 点击设备模拟器图标
3. 选择不同设备尺寸测试

### 推荐测试设备
- iPhone 12 Pro (390x844)
- iPad (768x1024)
- Galaxy S20 (360x800)
- Desktop (1920x1080)

## 🎯 功能演示清单

- [ ] 页面加载动画效果
- [ ] 粒子背景动画
- [ ] 品牌信息展示
- [ ] 输入框交互动画
- [ ] 表单验证提示
- [ ] 登录按钮动画
- [ ] 成功登录跳转
- [ ] 错误处理动画
- [ ] 响应式布局适配
- [ ] 键盘导航功能

## 🚨 注意事项

1. **后端服务**: 如需连接真实API，请确保后端服务在5000端口运行
2. **CORS配置**: 后端需要配置CORS允许前端域名访问
3. **网络代理**: 如果使用代理，请检查代理配置是否正确

## 📞 技术支持

如遇到问题，请检查：
1. Node.js版本 >= 16
2. Yarn版本 >= 1.22
3. 浏览器版本是否支持
4. 网络连接是否正常

---

🎉 **享受全新的AI风格登录体验！**