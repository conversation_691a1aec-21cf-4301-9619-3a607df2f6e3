﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="DotNetCore.CAP" Version="8.3.4" />
		<PackageReference Include="DotNetCore.CAP.RedisStreams" Version="8.3.4" />
		<PackageReference Include="DotNetCore.CAP.PostgreSql" Version="8.3.4" />
		<PackageReference Include="DotNetCore.CAP.Dashboard" Version="8.3.4" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Fancyx.Core\Fancyx.Core.csproj" />
	</ItemGroup>
</Project>
