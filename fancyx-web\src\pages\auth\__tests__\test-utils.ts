import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from '@/store';
import { AuthProvider } from '@/components/AuthProvider';

// 自定义渲染函数，包含所有必要的Provider
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <AuthProvider>
          {children}
        </AuthProvider>
      </BrowserRouter>
    </Provider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options });

// 导出所有testing-library的功能
export * from '@testing-library/react';

// 覆盖render方法
export { customRender as render };

// 测试工具函数
export const mockAuthProvider = (overrides = {}) => {
  const defaultMock = {
    pwdLogin: jest.fn(),
    logout: jest.fn(),
    isAuthenticated: jest.fn(() => false),
    userInfo: null,
    ...overrides,
  };

  jest.mock('@/components/AuthProvider', () => ({
    AuthProvider: ({ children }: { children: React.ReactNode }) => children,
    useAuthProvider: () => defaultMock,
  }));

  return defaultMock;
};

// 模拟不同的视口尺寸
export const mockViewport = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });

  // 触发resize事件
  window.dispatchEvent(new Event('resize'));
};

// 模拟媒体查询
export const mockMediaQuery = (query: string, matches: boolean) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(q => ({
      matches: q === query ? matches : false,
      media: q,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
};

// 模拟用户代理
export const mockUserAgent = (userAgent: string) => {
  Object.defineProperty(window.navigator, 'userAgent', {
    writable: true,
    value: userAgent,
  });
};

// 模拟性能API
export const mockPerformanceAPI = () => {
  const mockPerformance = {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn(() => []),
    getEntriesByName: jest.fn(() => []),
  };

  Object.defineProperty(window, 'performance', {
    value: mockPerformance,
  });

  return mockPerformance;
};

// 等待动画完成
export const waitForAnimation = (duration = 1000) => {
  return new Promise(resolve => setTimeout(resolve, duration));
};

// 检查CSS类是否存在
export const hasCSSClass = (element: Element, className: string): boolean => {
  return element.classList.contains(className);
};

// 检查CSS样式属性
export const getCSSProperty = (element: Element, property: string): string => {
  return window.getComputedStyle(element).getPropertyValue(property);
};

// 模拟键盘事件
export const createKeyboardEvent = (key: string, type = 'keydown') => {
  return new KeyboardEvent(type, {
    key,
    code: key,
    bubbles: true,
    cancelable: true,
  });
};

// 模拟鼠标事件
export const createMouseEvent = (type: string, options = {}) => {
  return new MouseEvent(type, {
    bubbles: true,
    cancelable: true,
    ...options,
  });
};