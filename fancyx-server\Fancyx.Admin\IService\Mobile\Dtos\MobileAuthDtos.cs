using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端登录DTO
/// </summary>
public class MobileLoginDto
{
    /// <summary>
    /// 用户名
    /// </summary>
    [Required]
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    [Required]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 设备信息
    /// </summary>
    [Required]
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();

    /// <summary>
    /// 位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 验证码（如果需要）
    /// </summary>
    public string? CaptchaCode { get; set; }

    /// <summary>
    /// 验证码Key
    /// </summary>
    public string? CaptchaKey { get; set; }
}

/// <summary>
/// 移动端登录结果DTO
/// </summary>
public class MobileLoginResultDto
{
    /// <summary>
    /// 访问令牌
    /// </summary>
    public string AccessToken { get; set; } = string.Empty;

    /// <summary>
    /// 刷新令牌
    /// </summary>
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// 令牌类型
    /// </summary>
    public string TokenType { get; set; } = "Bearer";

    /// <summary>
    /// 过期时间（秒）
    /// </summary>
    public int ExpiresIn { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public MobileUserInfoDto UserInfo { get; set; } = new();

    /// <summary>
    /// 应用配置
    /// </summary>
    public MobileAppConfigDto AppConfig { get; set; } = new();

    /// <summary>
    /// 服务器时间
    /// </summary>
    public DateTime ServerTime { get; set; } = DateTime.Now;
}

/// <summary>
/// 移动端用户信息DTO
/// </summary>
public class MobileUserInfoDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 真实姓名
    /// </summary>
    public string RealName { get; set; } = string.Empty;

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 角色列表
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// 权限列表
    /// </summary>
    public List<string> Permissions { get; set; } = new();

    /// <summary>
    /// 租户ID
    /// </summary>
    public Guid? TenantId { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string? Position { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }
}

/// <summary>
/// 移动端应用配置DTO
/// </summary>
public class MobileAppConfigDto
{
    /// <summary>
    /// 最大文件大小（字节）
    /// </summary>
    public long MaxFileSize { get; set; } = 10 * 1024 * 1024; // 10MB

    /// <summary>
    /// 最大批量文件数
    /// </summary>
    public int MaxBatchFiles { get; set; } = 20;

    /// <summary>
    /// 支持的图片格式
    /// </summary>
    public string[] SupportedImageFormats { get; set; } = { "jpg", "jpeg", "png", "webp" };

    /// <summary>
    /// 支持的音频格式
    /// </summary>
    public string[] SupportedAudioFormats { get; set; } = { "mp3", "wav", "m4a", "aac" };

    /// <summary>
    /// AI功能是否启用
    /// </summary>
    public bool AIFeatureEnabled { get; set; } = true;

    /// <summary>
    /// 离线模式是否启用
    /// </summary>
    public bool OfflineModeEnabled { get; set; } = true;

    /// <summary>
    /// 自动同步间隔（秒）
    /// </summary>
    public int AutoSyncInterval { get; set; } = 300; // 5分钟

    /// <summary>
    /// 是否需要位置信息
    /// </summary>
    public bool LocationRequired { get; set; } = true;

    /// <summary>
    /// 应用版本信息
    /// </summary>
    public MobileAppVersionDto? VersionInfo { get; set; }
}

/// <summary>
/// 移动端应用版本信息DTO
/// </summary>
public class MobileAppVersionDto
{
    /// <summary>
    /// 当前版本
    /// </summary>
    public string CurrentVersion { get; set; } = string.Empty;

    /// <summary>
    /// 最新版本
    /// </summary>
    public string LatestVersion { get; set; } = string.Empty;

    /// <summary>
    /// 是否需要更新
    /// </summary>
    public bool UpdateRequired { get; set; }

    /// <summary>
    /// 是否强制更新
    /// </summary>
    public bool ForceUpdate { get; set; }

    /// <summary>
    /// 更新说明
    /// </summary>
    public string? UpdateDescription { get; set; }

    /// <summary>
    /// 下载链接
    /// </summary>
    public string? DownloadUrl { get; set; }
}

/// <summary>
/// 移动端刷新Token DTO
/// </summary>
public class MobileRefreshTokenDto
{
    /// <summary>
    /// 刷新令牌
    /// </summary>
    [Required]
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// 设备ID
    /// </summary>
    [Required]
    public string DeviceId { get; set; } = string.Empty;
}
