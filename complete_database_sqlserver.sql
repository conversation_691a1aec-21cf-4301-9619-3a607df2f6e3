/*
 完整数据库创建脚本 - SQL Server版本
 
 Source Server Type    : PostgreSQL
 Target Server Type    : SQL Server
 File Encoding         : UTF-8
 
 Date: 2025/07/29
 Converted from PostgreSQL to SQL Server
 
 说明：
 1. 此脚本包含所有表结构的SQL Server版本
 2. 已完成PostgreSQL到SQL Server的数据类型转换
 3. 建议在执行前备份现有数据库
*/

-- ========================================
-- 项目管理模块表
-- ========================================

-- ----------------------------
-- Table structure for sys_project
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_project]') AND type in (N'U'))
DROP TABLE [dbo].[sys_project]
GO

CREATE TABLE [dbo].[sys_project] (
  [id] bigint NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [project_no] nvarchar(20) NOT NULL,
  [project_name] nvarchar(200) NOT NULL,
  [project_address] nvarchar(500) NOT NULL,
  [region] nvarchar(50) NOT NULL,
  [project_type] nvarchar(20) NOT NULL,
  [related_contract] nvarchar(100) NULL,
  [site_manager] nvarchar(50) NOT NULL,
  [manager_phone] nvarchar(20) NOT NULL,
  [manager_position] nvarchar(50) NULL,
  [start_date] datetime2(6) NULL,
  [end_date] datetime2(6) NULL,
  [project_status] int NOT NULL DEFAULT 1,
  [description] nvarchar(1000) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_project_check
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_project_check]') AND type in (N'U'))
DROP TABLE [dbo].[sys_project_check]
GO

CREATE TABLE [dbo].[sys_project_check] (
  [id] bigint NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [project_id] bigint NOT NULL,
  [check_type] nvarchar(20) NOT NULL,
  [secondary_category] nvarchar(50) NOT NULL,
  [check_date] datetime2(6) NOT NULL,
  [client_unit] nvarchar(200) NOT NULL,
  [check_leader] nvarchar(50) NOT NULL,
  [check_members] nvarchar(500) NULL,
  [project_progress] nvarchar(100) NULL,
  [check_content] nvarchar(2000) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_hidden_danger
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_hidden_danger]') AND type in (N'U'))
DROP TABLE [dbo].[sys_hidden_danger]
GO

CREATE TABLE [dbo].[sys_hidden_danger] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [project_check_id] bigint NOT NULL,
  [danger_category] nvarchar(100) NOT NULL,
  [standard_id] bigint NULL,
  [standard_name] nvarchar(200) NULL,
  [problem_level] nvarchar(20) NOT NULL,
  [safety_hazard] nvarchar(500) NOT NULL,
  [regulation_clause] nvarchar(500) NULL,
  [description] nvarchar(2000) NOT NULL,
  [rectification_requirement] nvarchar(2000) NOT NULL,
  [possible_consequences] nvarchar(1000) NULL,
  [rectification_suggestion] nvarchar(500) NULL,
  [need_review] bit NOT NULL DEFAULT 0,
  [photos] nvarchar(2000) NULL,
  [status] int NOT NULL DEFAULT 1,
  [rectification_deadline] datetime2(6) NULL,
  [rectification_person] nvarchar(100) NULL,
  [rectification_status] int NOT NULL DEFAULT 0,
  [review_status] int NOT NULL DEFAULT 0,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_ai_platform_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_ai_platform_config]') AND type in (N'U'))
DROP TABLE [dbo].[sys_ai_platform_config]
GO

CREATE TABLE [dbo].[sys_ai_platform_config] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [platform_name] nvarchar(100) NOT NULL,
  [api_url] nvarchar(500) NOT NULL,
  [api_key] nvarchar(500) NOT NULL,
  [model_name] nvarchar(100) NULL,
  [timeout] int NOT NULL DEFAULT 30,
  [status] int NOT NULL DEFAULT 0,
  [remarks] nvarchar(1000) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_standard
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_standard]') AND type in (N'U'))
DROP TABLE [dbo].[sys_standard]
GO

CREATE TABLE [dbo].[sys_standard] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [name] nvarchar(200) NOT NULL,
  [type] nvarchar(50) NOT NULL,
  [danger_category] nvarchar(100) NOT NULL,
  [regulation_clause] nvarchar(500) NULL,
  [requirements] nvarchar(2000) NULL,
  [status] int NOT NULL DEFAULT 0,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_business_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_business_log]') AND type in (N'U'))
DROP TABLE [dbo].[sys_business_log]
GO

CREATE TABLE [dbo].[sys_business_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [user_name] nvarchar(32) NOT NULL,
  [action] nvarchar(512) NOT NULL,
  [http_method] nvarchar(16) NOT NULL,
  [url] nvarchar(512) NOT NULL,
  [os] nvarchar(64) NULL,
  [browser] nvarchar(64) NULL,
  [node_name] nvarchar(128) NOT NULL,
  [ip] nvarchar(32) NULL,
  [address] nvarchar(256) NULL,
  [is_success] bit NOT NULL DEFAULT 1,
  [operation_msg] nvarchar(128) NULL,
  [mill_seconds] int NOT NULL DEFAULT 0,
  [request_id] nvarchar(255) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role_dept]') AND type in (N'U'))
DROP TABLE [dbo].[sys_role_dept]
GO

CREATE TABLE [dbo].[sys_role_dept] (
  [id] uniqueidentifier NOT NULL,
  [role_id] uniqueidentifier NOT NULL,
  [dept_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_tenant
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_tenant]') AND type in (N'U'))
DROP TABLE [dbo].[sys_tenant]
GO

CREATE TABLE [dbo].[sys_tenant] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(255) NOT NULL,
  [tenant_id] nvarchar(18) NOT NULL,
  [remark] nvarchar(512) NULL
)
GO

-- ========================================
-- 日志和访问记录表
-- ========================================

-- ----------------------------
-- Table structure for api_access_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[api_access_log]') AND type in (N'U'))
DROP TABLE [dbo].[api_access_log]
GO

CREATE TABLE [dbo].[api_access_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [path] nvarchar(255) NULL,
  [method] nvarchar(255) NULL,
  [ip] nvarchar(32) NULL,
  [request_time] datetime2(6) NOT NULL,
  [response_time] datetime2(6) NULL,
  [duration] bigint NULL,
  [user_id] uniqueidentifier NULL,
  [user_name] nvarchar(255) NULL,
  [request_body] nvarchar(255) NULL,
  [response_body] ntext NULL,
  [browser] nvarchar(512) NULL,
  [query_string] nvarchar(255) NULL,
  [trace_id] nvarchar(255) NULL,
  [operate_type] nvarchar(255) NULL,
  [operate_name] nvarchar(255) NULL,
  [tenant_id] nvarchar(255) NULL
)
GO

-- ----------------------------
-- Table structure for exception_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[exception_log]') AND type in (N'U'))
DROP TABLE [dbo].[exception_log]
GO

CREATE TABLE [dbo].[exception_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [exception_type] nvarchar(255) NULL,
  [message] ntext NULL,
  [stack_trace] ntext NULL,
  [inner_exception] ntext NULL,
  [request_path] nvarchar(255) NULL,
  [request_method] nvarchar(255) NULL,
  [user_id] uniqueidentifier NULL,
  [user_name] nvarchar(255) NULL,
  [ip] nvarchar(32) NULL,
  [browser] nvarchar(512) NULL,
  [trace_id] nvarchar(255) NULL,
  [is_handled] bit NOT NULL,
  [handled_time] datetime2(6) NULL,
  [handled_by] nvarchar(255) NULL,
  [tenant_id] nvarchar(255) NULL
)
GO

-- ----------------------------
-- Table structure for log_record
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[log_record]') AND type in (N'U'))
DROP TABLE [dbo].[log_record]
GO

CREATE TABLE [dbo].[log_record] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [type] nvarchar(255) NOT NULL,
  [sub_type] nvarchar(255) NOT NULL,
  [biz_no] nvarchar(255) NOT NULL,
  [content] nvarchar(255) NOT NULL,
  [browser] nvarchar(512) NULL,
  [ip] nvarchar(32) NULL,
  [trace_id] nvarchar(255) NULL,
  [tenant_id] nvarchar(255) NULL,
  [user_id] uniqueidentifier NULL,
  [user_name] nvarchar(255) NULL
)
GO

-- ========================================
-- 组织架构表
-- ========================================

-- ----------------------------
-- Table structure for org_employee
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[org_employee]') AND type in (N'U'))
DROP TABLE [dbo].[org_employee]
GO

CREATE TABLE [dbo].[org_employee] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [code] nvarchar(64) NOT NULL,
  [name] nvarchar(64) NOT NULL,
  [sex] int NOT NULL,
  [phone] nvarchar(16) NOT NULL,
  [id_no] nvarchar(32) NULL,
  [front_id_no_url] nvarchar(512) NULL,
  [back_id_no_url] nvarchar(512) NULL,
  [birthday] datetime2(6) NULL,
  [address] nvarchar(512) NULL,
  [email] nvarchar(64) NULL,
  [in_time] datetime2(6) NOT NULL,
  [out_time] datetime2(6) NULL,
  [status] int NOT NULL,
  [user_id] uniqueidentifier NULL,
  [dept_id] uniqueidentifier NULL,
  [position_id] uniqueidentifier NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for org_position
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[org_position]') AND type in (N'U'))
DROP TABLE [dbo].[org_position]
GO

CREATE TABLE [dbo].[org_position] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [code] nvarchar(32) NOT NULL,
  [name] nvarchar(64) NOT NULL,
  [level] int NOT NULL,
  [status] int NOT NULL,
  [description] nvarchar(512) NULL,
  [group_id] uniqueidentifier NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for org_position_group
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[org_position_group]') AND type in (N'U'))
DROP TABLE [dbo].[org_position_group]
GO

CREATE TABLE [dbo].[org_position_group] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [group_name] nvarchar(64) NOT NULL,
  [remark] nvarchar(512) NULL,
  [parent_id] uniqueidentifier NULL,
  [parent_ids] nvarchar(1024) NULL,
  [sort] int NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ========================================
-- 系统配置表
-- ========================================

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_config]') AND type in (N'U'))
DROP TABLE [dbo].[sys_config]
GO

CREATE TABLE [dbo].[sys_config] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(256) NOT NULL,
  [key] nvarchar(128) NOT NULL,
  [value] nvarchar(255) NOT NULL,
  [group_key] nvarchar(64) NULL,
  [remark] nvarchar(512) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dept]') AND type in (N'U'))
DROP TABLE [dbo].[sys_dept]
GO

CREATE TABLE [dbo].[sys_dept] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [code] nvarchar(32) NOT NULL,
  [name] nvarchar(64) NOT NULL,
  [sort] int NOT NULL,
  [description] nvarchar(512) NULL,
  [status] int NOT NULL,
  [curator_id] uniqueidentifier NULL,
  [email] nvarchar(64) NULL,
  [phone] nvarchar(64) NULL,
  [parent_id] uniqueidentifier NULL,
  [parent_ids] nvarchar(1024) NULL,
  [layer] int NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ========================================
-- 字典管理表
-- ========================================

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dict_data]') AND type in (N'U'))
DROP TABLE [dbo].[sys_dict_data]
GO

CREATE TABLE [dbo].[sys_dict_data] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [value] nvarchar(256) NOT NULL,
  [label] nvarchar(128) NULL,
  [dict_type] nvarchar(128) NOT NULL,
  [remark] nvarchar(512) NULL,
  [sort] int NOT NULL,
  [is_enabled] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dict_type]') AND type in (N'U'))
DROP TABLE [dbo].[sys_dict_type]
GO

CREATE TABLE [dbo].[sys_dict_type] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(128) NOT NULL,
  [dict_type] nvarchar(128) NOT NULL,
  [remark] nvarchar(512) NULL,
  [is_enabled] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ========================================
-- 日志管理表
-- ========================================

-- ----------------------------
-- Table structure for sys_login_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_login_log]') AND type in (N'U'))
DROP TABLE [dbo].[sys_login_log]
GO

CREATE TABLE [dbo].[sys_login_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [user_name] nvarchar(32) NOT NULL,
  [ip] nvarchar(32) NULL,
  [address] nvarchar(256) NULL,
  [os] nvarchar(64) NULL,
  [browser] nvarchar(512) NULL,
  [operation_msg] nvarchar(128) NULL,
  [is_success] bit NOT NULL,
  [session_id] nvarchar(36) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ========================================
-- 菜单管理表
-- ========================================

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_menu]') AND type in (N'U'))
DROP TABLE [dbo].[sys_menu]
GO

CREATE TABLE [dbo].[sys_menu] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [title] nvarchar(32) NOT NULL,
  [icon] nvarchar(64) NULL,
  [path] nvarchar(256) NULL,
  [component] nvarchar(256) NULL,
  [menu_type] int NOT NULL,
  [permission] nvarchar(128) NOT NULL,
  [parent_id] uniqueidentifier NULL,
  [sort] int NOT NULL,
  [display] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_notification
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_notification]') AND type in (N'U'))
DROP TABLE [dbo].[sys_notification]
GO

CREATE TABLE [dbo].[sys_notification] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [title] nvarchar(100) NOT NULL,
  [description] nvarchar(500) NULL,
  [employee_id] uniqueidentifier NOT NULL,
  [is_readed] bit NOT NULL,
  [readed_time] datetime2(6) NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ========================================
-- 角色管理表
-- ========================================

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role]') AND type in (N'U'))
DROP TABLE [dbo].[sys_role]
GO

CREATE TABLE [dbo].[sys_role] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [role_name] nvarchar(64) NOT NULL,
  [remark] nvarchar(512) NULL,
  [power_data_type] int NOT NULL,
  [tenant_id] nvarchar(18) NULL,
  [is_enabled] bit NOT NULL
)
GO

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role_menu]') AND type in (N'U'))
DROP TABLE [dbo].[sys_role_menu]
GO

CREATE TABLE [dbo].[sys_role_menu] (
  [id] uniqueidentifier NOT NULL,
  [menu_id] uniqueidentifier NOT NULL,
  [role_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ========================================
-- 用户管理表
-- ========================================

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_user]') AND type in (N'U'))
DROP TABLE [dbo].[sys_user]
GO

CREATE TABLE [dbo].[sys_user] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [user_name] nvarchar(32) NOT NULL,
  [password] nvarchar(512) NOT NULL,
  [password_salt] nvarchar(256) NOT NULL,
  [avatar] nvarchar(256) NULL,
  [nick_name] nvarchar(64) NOT NULL,
  [sex] int NOT NULL,
  [is_enabled] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_user_role]') AND type in (N'U'))
DROP TABLE [dbo].[sys_user_role]
GO

CREATE TABLE [dbo].[sys_user_role] (
  [id] uniqueidentifier NOT NULL,
  [user_id] uniqueidentifier NOT NULL,
  [role_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- ========================================
-- 表注释 (主要表)
-- ========================================

-- 项目管理模块表注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目检查表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'隐患表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'AI平台配置表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准规范表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard'
GO

-- 系统核心表注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'业务日志表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色部门关联表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'员工表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位分组', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统配置', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典数据表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典类型表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录日志', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色菜单表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_menu'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户角色关联表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user_role'
GO

-- ========================================
-- 主键约束
-- ========================================

-- 项目管理模块主键
ALTER TABLE [dbo].[sys_project] ADD CONSTRAINT [PK_sys_project] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_project_check] ADD CONSTRAINT [PK_sys_project_check] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_hidden_danger] ADD CONSTRAINT [PK_sys_hidden_danger] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_ai_platform_config] ADD CONSTRAINT [PK_sys_ai_platform_config] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_standard] ADD CONSTRAINT [PK_sys_standard] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_business_log] ADD CONSTRAINT [PK_sys_business_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_role_dept] ADD CONSTRAINT [PK_sys_role_dept] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_tenant] ADD CONSTRAINT [PK_sys_tenant] PRIMARY KEY ([id])
GO

-- 日志和访问记录主键
ALTER TABLE [dbo].[api_access_log] ADD CONSTRAINT [PK_api_access_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[exception_log] ADD CONSTRAINT [PK_exception_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[log_record] ADD CONSTRAINT [PK_log_record] PRIMARY KEY ([id])
GO

-- 组织架构主键
ALTER TABLE [dbo].[org_employee] ADD CONSTRAINT [PK_org_employee] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_position] ADD CONSTRAINT [PK_org_position] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_position_group] ADD CONSTRAINT [PK_org_position_group] PRIMARY KEY ([id])
GO

-- 系统配置主键
ALTER TABLE [dbo].[sys_config] ADD CONSTRAINT [PK_sys_config] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_dept] ADD CONSTRAINT [PK_sys_dept] PRIMARY KEY ([id])
GO

-- 字典管理主键
ALTER TABLE [dbo].[sys_dict_data] ADD CONSTRAINT [PK_sys_dict_data] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_dict_type] ADD CONSTRAINT [PK_sys_dict_type] PRIMARY KEY ([id])
GO

-- 日志管理主键
ALTER TABLE [dbo].[sys_login_log] ADD CONSTRAINT [PK_sys_login_log] PRIMARY KEY ([id])
GO

-- 菜单管理主键
ALTER TABLE [dbo].[sys_menu] ADD CONSTRAINT [PK_sys_menu] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_notification] ADD CONSTRAINT [PK_sys_notification] PRIMARY KEY ([id])
GO

-- 角色管理主键
ALTER TABLE [dbo].[sys_role] ADD CONSTRAINT [PK_sys_role] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_role_menu] ADD CONSTRAINT [PK_sys_role_menu] PRIMARY KEY ([id])
GO

-- 用户管理主键
ALTER TABLE [dbo].[sys_user] ADD CONSTRAINT [PK_sys_user] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_user_role] ADD CONSTRAINT [PK_sys_user_role] PRIMARY KEY ([id])
GO

-- ========================================
-- 索引
-- ========================================
CREATE UNIQUE NONCLUSTERED INDEX [IX_sys_tenant_tenant_id] ON [dbo].[sys_tenant] ([tenant_id] ASC)
GO

-- ========================================
-- 外键约束 (可选 - 如需要请取消注释)
-- ========================================
-- ALTER TABLE [dbo].[sys_project_check] ADD CONSTRAINT [FK_sys_project_check_project] FOREIGN KEY ([project_id]) REFERENCES [dbo].[sys_project] ([id])
-- GO
-- ALTER TABLE [dbo].[sys_hidden_danger] ADD CONSTRAINT [FK_sys_hidden_danger_project_check] FOREIGN KEY ([project_check_id]) REFERENCES [dbo].[sys_project_check] ([id])
-- GO
-- ALTER TABLE [dbo].[sys_hidden_danger] ADD CONSTRAINT [FK_sys_hidden_danger_standard] FOREIGN KEY ([standard_id]) REFERENCES [dbo].[sys_standard] ([id])
-- GO

PRINT '数据库表结构创建完成！'
GO
