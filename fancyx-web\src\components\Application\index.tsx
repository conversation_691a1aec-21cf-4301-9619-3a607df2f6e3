import { App } from 'antd';
import React, { createContext, useContext, useEffect } from 'react';
import { setGlobalMessage } from '@/utils/httpClient';

export interface ApplicationContextType {
  ossDomain: string;
}

const ApplicationContext = createContext<ApplicationContextType>({ ossDomain: '' });
const Application = ({ children }: { children: React.ReactNode }) => {
  const ossDomain = import.meta.env.VITE_OSS_DOMAIN;

  return (
    <ApplicationContext.Provider value={{ ossDomain }}>
      <App>
        <AppContent>{children}</AppContent>
      </App>
    </ApplicationContext.Provider>
  );
};

const AppContent = ({ children }: { children: React.ReactNode }) => {
  const { message } = App.useApp();

  useEffect(() => {
    // 设置全局 message 处理函数
    setGlobalMessage(message.error);
  }, [message]);

  return <>{children}</>;
};

export default Application;

// eslint-disable-next-line react-refresh/only-export-components
export const useApplication = () => useContext(ApplicationContext);
