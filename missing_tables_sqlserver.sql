/*
 补充缺失表的创建脚本 - SQL Server版本
 
 Source Server Type    : PostgreSQL
 Target Server Type    : SQL Server
 File Encoding         : UTF-8
 
 Date: 2025/07/29
 Converted from PostgreSQL to SQL Server
*/

-- ============================
-- 新增表结构 - 项目管理模块
-- ============================

-- ----------------------------
-- Table structure for sys_project
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_project]') AND type in (N'U'))
DROP TABLE [dbo].[sys_project]
GO

CREATE TABLE [dbo].[sys_project] (
  [id] bigint NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [project_no] nvarchar(20) NOT NULL,
  [project_name] nvarchar(200) NOT NULL,
  [project_address] nvarchar(500) NOT NULL,
  [region] nvarchar(50) NOT NULL,
  [project_type] nvarchar(20) NOT NULL,
  [related_contract] nvarchar(100) NULL,
  [site_manager] nvarchar(50) NOT NULL,
  [manager_phone] nvarchar(20) NOT NULL,
  [manager_position] nvarchar(50) NULL,
  [start_date] datetime2(6) NULL,
  [end_date] datetime2(6) NULL,
  [project_status] int NOT NULL DEFAULT 1,
  [description] nvarchar(1000) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目编号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'project_no'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'project_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'project_address'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'所属区域', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'region'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目类型（生产安全、质量安全）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'project_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联合同', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'related_contract'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'现场负责人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'site_manager'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'负责人电话', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'manager_phone'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'负责人职务', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'manager_position'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'开工日期', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'start_date'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'竣工日期', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'end_date'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目状态', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'project_status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_project_check
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_project_check]') AND type in (N'U'))
DROP TABLE [dbo].[sys_project_check]
GO

CREATE TABLE [dbo].[sys_project_check] (
  [id] bigint NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [project_id] bigint NOT NULL,
  [check_type] nvarchar(20) NOT NULL,
  [secondary_category] nvarchar(50) NOT NULL,
  [check_date] datetime2(6) NOT NULL,
  [client_unit] nvarchar(200) NOT NULL,
  [check_leader] nvarchar(50) NOT NULL,
  [check_members] nvarchar(500) NULL,
  [project_progress] nvarchar(100) NULL,
  [check_content] nvarchar(2000) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目检查表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检查ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'项目ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'project_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检查类型（生产安全、质量安全）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'check_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'二级分类', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'secondary_category'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检查日期', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'check_date'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'委托单位', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'client_unit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检查组长', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'check_leader'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检查组员', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'check_members'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工程进度', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'project_progress'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检查内容', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'check_content'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_project_check', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_hidden_danger
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_hidden_danger]') AND type in (N'U'))
DROP TABLE [dbo].[sys_hidden_danger]
GO

CREATE TABLE [dbo].[sys_hidden_danger] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [project_check_id] bigint NOT NULL,
  [danger_category] nvarchar(100) NOT NULL,
  [standard_id] bigint NULL,
  [standard_name] nvarchar(200) NULL,
  [problem_level] nvarchar(20) NOT NULL,
  [safety_hazard] nvarchar(500) NOT NULL,
  [regulation_clause] nvarchar(500) NULL,
  [description] nvarchar(2000) NOT NULL,
  [rectification_requirement] nvarchar(2000) NOT NULL,
  [possible_consequences] nvarchar(1000) NULL,
  [rectification_suggestion] nvarchar(500) NULL,
  [need_review] bit NOT NULL DEFAULT 0,
  [photos] nvarchar(2000) NULL,
  [status] int NOT NULL DEFAULT 1,
  [rectification_deadline] datetime2(6) NULL,
  [rectification_person] nvarchar(100) NULL,
  [rectification_status] int NOT NULL DEFAULT 0,
  [review_status] int NOT NULL DEFAULT 0,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'隐患表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'隐患ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'检查ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'project_check_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'隐患大类', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'danger_category'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准规范ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'standard_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准规范名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'standard_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'问题等级（一般、较大、重大）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'problem_level'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'安全隐患', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'safety_hazard'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'法规条款', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'regulation_clause'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'具体描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'整改要求', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'rectification_requirement'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'可能产生后果', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'possible_consequences'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'整改建议', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'rectification_suggestion'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否需要复查', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'need_review'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'现场照片（JSON格式）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'photos'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'隐患状态', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'整改期限', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'rectification_deadline'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'整改责任人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'rectification_person'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'整改状态', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'rectification_status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'复查状态', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'review_status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_hidden_danger', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_ai_platform_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_ai_platform_config]') AND type in (N'U'))
DROP TABLE [dbo].[sys_ai_platform_config]
GO

CREATE TABLE [dbo].[sys_ai_platform_config] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [platform_name] nvarchar(100) NOT NULL,
  [api_url] nvarchar(500) NOT NULL,
  [api_key] nvarchar(500) NOT NULL,
  [model_name] nvarchar(100) NULL,
  [timeout] int NOT NULL DEFAULT 30,
  [status] int NOT NULL DEFAULT 0,
  [remarks] nvarchar(1000) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'AI平台配置表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'AI平台配置ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'平台名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config', @level2type=N'COLUMN', @level2name=N'platform_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'API地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config', @level2type=N'COLUMN', @level2name=N'api_url'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'API Key', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config', @level2type=N'COLUMN', @level2name=N'api_key'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'模型名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config', @level2type=N'COLUMN', @level2name=N'model_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求超时时间（秒）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config', @level2type=N'COLUMN', @level2name=N'timeout'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态（0启用，1停用）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config', @level2type=N'COLUMN', @level2name=N'remarks'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_ai_platform_config', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_standard
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_standard]') AND type in (N'U'))
DROP TABLE [dbo].[sys_standard]
GO

CREATE TABLE [dbo].[sys_standard] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL DEFAULT 0,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [name] nvarchar(200) NOT NULL,
  [type] nvarchar(50) NOT NULL,
  [danger_category] nvarchar(100) NOT NULL,
  [regulation_clause] nvarchar(500) NULL,
  [requirements] nvarchar(2000) NULL,
  [status] int NOT NULL DEFAULT 0,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准规范表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'标准类型', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard', @level2type=N'COLUMN', @level2name=N'type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'隐患大类', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard', @level2type=N'COLUMN', @level2name=N'danger_category'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'法规条款', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard', @level2type=N'COLUMN', @level2name=N'regulation_clause'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'具体要求', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard', @level2type=N'COLUMN', @level2name=N'requirements'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态（0启用，1停用）', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_standard', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 主键约束
-- ============================
ALTER TABLE [dbo].[sys_project] ADD CONSTRAINT [PK_sys_project] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_project_check] ADD CONSTRAINT [PK_sys_project_check] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_hidden_danger] ADD CONSTRAINT [PK_sys_hidden_danger] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_ai_platform_config] ADD CONSTRAINT [PK_sys_ai_platform_config] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_standard] ADD CONSTRAINT [PK_sys_standard] PRIMARY KEY ([id])
GO

-- ============================
-- 外键约束 (可选 - 如需要请取消注释)
-- ============================
-- ALTER TABLE [dbo].[sys_project_check] ADD CONSTRAINT [FK_sys_project_check_project] FOREIGN KEY ([project_id]) REFERENCES [dbo].[sys_project] ([id])
-- GO
-- ALTER TABLE [dbo].[sys_hidden_danger] ADD CONSTRAINT [FK_sys_hidden_danger_project_check] FOREIGN KEY ([project_check_id]) REFERENCES [dbo].[sys_project_check] ([id])
-- GO
-- ALTER TABLE [dbo].[sys_hidden_danger] ADD CONSTRAINT [FK_sys_hidden_danger_standard] FOREIGN KEY ([standard_id]) REFERENCES [dbo].[sys_standard] ([id])
-- GO

-- ----------------------------
-- Table structure for sys_business_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_business_log]') AND type in (N'U'))
DROP TABLE [dbo].[sys_business_log]
GO

CREATE TABLE [dbo].[sys_business_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [user_name] nvarchar(32) NOT NULL,
  [action] nvarchar(512) NOT NULL,
  [http_method] nvarchar(16) NOT NULL,
  [url] nvarchar(512) NOT NULL,
  [os] nvarchar(64) NULL,
  [browser] nvarchar(64) NULL,
  [node_name] nvarchar(128) NOT NULL,
  [ip] nvarchar(32) NULL,
  [address] nvarchar(256) NULL,
  [is_success] bit NOT NULL DEFAULT 1,
  [operation_msg] nvarchar(128) NULL,
  [mill_seconds] int NOT NULL DEFAULT 0,
  [request_id] nvarchar(255) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'业务日志表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'业务日志ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'账号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'user_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作方法，全名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'action'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'HTTP请求方式', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'http_method'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'url'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'os'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'浏览器', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'browser'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作节点名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'node_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'IP', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'ip'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'address'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否成功', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'is_success'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作信息', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'operation_msg'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'耗时，单位毫秒', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'mill_seconds'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'请求跟踪ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'request_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_business_log', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role_dept]') AND type in (N'U'))
DROP TABLE [dbo].[sys_role_dept]
GO

CREATE TABLE [dbo].[sys_role_dept] (
  [id] uniqueidentifier NOT NULL,
  [role_id] uniqueidentifier NOT NULL,
  [dept_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色部门关联表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept', @level2type=N'COLUMN', @level2name=N'role_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept', @level2type=N'COLUMN', @level2name=N'dept_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_dept', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_tenant
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_tenant]') AND type in (N'U'))
DROP TABLE [dbo].[sys_tenant]
GO

CREATE TABLE [dbo].[sys_tenant] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(255) NOT NULL,
  [tenant_id] nvarchar(18) NOT NULL,
  [remark] nvarchar(512) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户主键ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant', @level2type=N'COLUMN', @level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户标识', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_tenant', @level2type=N'COLUMN', @level2name=N'remark'
GO

-- ============================
-- 主键约束
-- ============================
ALTER TABLE [dbo].[sys_project] ADD CONSTRAINT [PK_sys_project] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_project_check] ADD CONSTRAINT [PK_sys_project_check] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_hidden_danger] ADD CONSTRAINT [PK_sys_hidden_danger] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_ai_platform_config] ADD CONSTRAINT [PK_sys_ai_platform_config] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_standard] ADD CONSTRAINT [PK_sys_standard] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_business_log] ADD CONSTRAINT [PK_sys_business_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_role_dept] ADD CONSTRAINT [PK_sys_role_dept] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_tenant] ADD CONSTRAINT [PK_sys_tenant] PRIMARY KEY ([id])
GO

-- ============================
-- 索引
-- ============================
CREATE UNIQUE NONCLUSTERED INDEX [IX_sys_tenant_tenant_id] ON [dbo].[sys_tenant] ([tenant_id] ASC)
GO

-- ============================
-- 外键约束 (可选 - 如需要请取消注释)
-- ============================
-- ALTER TABLE [dbo].[sys_project_check] ADD CONSTRAINT [FK_sys_project_check_project] FOREIGN KEY ([project_id]) REFERENCES [dbo].[sys_project] ([id])
-- GO
-- ALTER TABLE [dbo].[sys_hidden_danger] ADD CONSTRAINT [FK_sys_hidden_danger_project_check] FOREIGN KEY ([project_check_id]) REFERENCES [dbo].[sys_project_check] ([id])
-- GO
-- ALTER TABLE [dbo].[sys_hidden_danger] ADD CONSTRAINT [FK_sys_hidden_danger_standard] FOREIGN KEY ([standard_id]) REFERENCES [dbo].[sys_standard] ([id])
-- GO
