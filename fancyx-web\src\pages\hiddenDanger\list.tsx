// @ts-nocheck
import React, { useRef } from 'react';
import { Button, Modal, message, Form, Input, Select, Tag } from 'antd';
import SmartTable from '@/components/SmartTable';
import type { SmartTableRef } from '@/components/SmartTable/type';
import { getHiddenDangerList, deleteHiddenDanger, confirmHiddenDanger } from '@/api/hiddenDanger';
import { useNavigate } from 'react-router-dom';

const HiddenDangerList: React.FC = () => {
  const tableRef = useRef<SmartTableRef>(null);
  const navigate = useNavigate();

  // 删除隐患明细
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除该隐患明细？',
      onOk: async () => {
        await deleteHiddenDanger(id);
        message.success('删除成功');
        tableRef.current?.reload();
      },
    });
  };

  // 确认隐患明细
  const handleConfirm = (id: string) => {
    Modal.confirm({
      title: '确认该隐患明细？',
      onOk: async () => {
        await confirmHiddenDanger(id);
        message.success('确认成功');
        tableRef.current?.reload();
      },
    });
  };

  // 表格列定义
  const columns = [
    { title: '检查信息', dataIndex: 'checkInfo', key: 'checkInfo' },
    { title: '隐患大类', dataIndex: 'dangerCategory', key: 'dangerCategory' },
    { title: '问题等级', dataIndex: 'problemLevel', key: 'problemLevel' },
    { title: '安全隐患', dataIndex: 'safetyHazard', key: 'safetyHazard' },
    { title: '具体描述', dataIndex: 'description', key: 'description' },
    { 
      title: '是否需要复查', 
      dataIndex: 'needReview', 
      key: 'needReview',
      render: (needReview: boolean) => (
        <Tag color={needReview ? 'orange' : 'green'}>
          {needReview ? '需要复查' : '无需复查'}
        </Tag>
      )
    },
    { 
      title: '状态', 
      dataIndex: 'status', 
      key: 'status',
      render: (status: number) => (
        <Tag color={status === 0 ? 'blue' : 'green'}>
          {status === 0 ? '待确认' : '正式记录'}
        </Tag>
      )
    },
    { title: '创建时间', dataIndex: 'creationTime', key: 'creationTime' },
    { title: '创建人', dataIndex: 'creatorName', key: 'creatorName' },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <>
          <Button type="link" onClick={() => navigate(`/hidden-danger/detail/${record.id}`)}>详情</Button>
          <Button type="link" onClick={() => navigate(`/hidden-danger/edit/${record.id}`)}>编辑</Button>
          {record.status === 0 && (
            <Button type="link" onClick={() => handleConfirm(record.id)}>确认</Button>
          )}
          <Button type="link" danger onClick={() => handleDelete(record.id)}>删除</Button>
        </>
      ),
    },
  ];

  // 查询项
  const searchItems: React.ReactNode[] = [
    <Form.Item name="checkInfo" label="检查信息" key="checkInfo">
      <Input placeholder="请输入检查信息" />
    </Form.Item>,
    <Form.Item name="dangerCategory" label="隐患大类" key="dangerCategory">
      <Input placeholder="请输入隐患大类" />
    </Form.Item>,
    <Form.Item name="problemLevel" label="问题等级" key="problemLevel">
      <Input placeholder="请输入问题等级" />
    </Form.Item>,
    <Form.Item name="status" label="状态" key="status">
      <Select placeholder="请选择状态">
        <Select.Option value={0}>待确认</Select.Option>
        <Select.Option value={1}>正式记录</Select.Option>
      </Select>
    </Form.Item>,
  ];

  // 工具栏
  const toolbar: React.ReactNode[] = [
    <Button type="primary" key="add" onClick={() => navigate('/hidden-danger/edit')}>新增隐患</Button>,
  ];

  return (
    <SmartTable
      ref={tableRef}
      columns={columns}
      request={getHiddenDangerList}
      searchItems={searchItems}
      toolbar={toolbar}
      rowKey="id"
    />
  );
};

export default HiddenDangerList; 