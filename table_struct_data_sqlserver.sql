/*
 完整数据库结构和数据脚本 - SQL Server版本
 
 Source Server Type    : PostgreSQL
 Target Server Type    : SQL Server
 File Encoding         : UTF-8
 
 Date: 2025/07/29
 Converted from PostgreSQL to SQL Server
*/

-- ============================
-- 日志和访问记录表
-- ============================

-- ----------------------------
-- Table structure for api_access_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[api_access_log]') AND type in (N'U'))
DROP TABLE [dbo].[api_access_log]
GO

CREATE TABLE [dbo].[api_access_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [path] nvarchar(255) NULL,
  [method] nvarchar(255) NULL,
  [ip] nvarchar(32) NULL,
  [request_time] datetime2(6) NOT NULL,
  [response_time] datetime2(6) NULL,
  [duration] bigint NULL,
  [user_id] uniqueidentifier NULL,
  [user_name] nvarchar(255) NULL,
  [request_body] nvarchar(255) NULL,
  [response_body] ntext NULL,
  [browser] nvarchar(512) NULL,
  [query_string] nvarchar(255) NULL,
  [trace_id] nvarchar(255) NULL,
  [operate_type] nvarchar(255) NULL, -- 注意：PostgreSQL的int4[]数组类型转换为字符串存储
  [operate_name] nvarchar(255) NULL,
  [tenant_id] nvarchar(255) NULL
)
GO

-- ----------------------------
-- Table structure for exception_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[exception_log]') AND type in (N'U'))
DROP TABLE [dbo].[exception_log]
GO

CREATE TABLE [dbo].[exception_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [exception_type] nvarchar(255) NULL,
  [message] ntext NULL,
  [stack_trace] ntext NULL,
  [inner_exception] ntext NULL,
  [request_path] nvarchar(255) NULL,
  [request_method] nvarchar(255) NULL,
  [user_id] uniqueidentifier NULL,
  [user_name] nvarchar(255) NULL,
  [ip] nvarchar(32) NULL,
  [browser] nvarchar(512) NULL,
  [trace_id] nvarchar(255) NULL,
  [is_handled] bit NOT NULL,
  [handled_time] datetime2(6) NULL,
  [handled_by] nvarchar(255) NULL,
  [tenant_id] nvarchar(255) NULL
)
GO

-- ----------------------------
-- Table structure for log_record
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[log_record]') AND type in (N'U'))
DROP TABLE [dbo].[log_record]
GO

CREATE TABLE [dbo].[log_record] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [type] nvarchar(255) NOT NULL,
  [sub_type] nvarchar(255) NOT NULL,
  [biz_no] nvarchar(255) NOT NULL,
  [content] nvarchar(255) NOT NULL,
  [browser] nvarchar(512) NULL,
  [ip] nvarchar(32) NULL,
  [trace_id] nvarchar(255) NULL,
  [tenant_id] nvarchar(255) NULL,
  [user_id] uniqueidentifier NULL,
  [user_name] nvarchar(255) NULL
)
GO

-- ============================
-- 组织架构表
-- ============================

-- ----------------------------
-- Table structure for org_employee
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[org_employee]') AND type in (N'U'))
DROP TABLE [dbo].[org_employee]
GO

CREATE TABLE [dbo].[org_employee] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [code] nvarchar(64) NOT NULL,
  [name] nvarchar(64) NOT NULL,
  [sex] int NOT NULL,
  [phone] nvarchar(16) NOT NULL,
  [id_no] nvarchar(32) NULL,
  [front_id_no_url] nvarchar(512) NULL,
  [back_id_no_url] nvarchar(512) NULL,
  [birthday] datetime2(6) NULL,
  [address] nvarchar(512) NULL,
  [email] nvarchar(64) NULL,
  [in_time] datetime2(6) NOT NULL,
  [out_time] datetime2(6) NULL,
  [status] int NOT NULL,
  [user_id] uniqueidentifier NULL,
  [dept_id] uniqueidentifier NULL,
  [position_id] uniqueidentifier NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'员工表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'姓名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'性别', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'sex'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'手机号码', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'phone'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'身份证', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'id_no'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'身份证正面', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'front_id_no_url'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'身份证背面', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'back_id_no_url'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'生日', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'birthday'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'现住址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'address'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'邮箱', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'email'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入职时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'in_time'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'离职时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'out_time'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态 1正常2离职', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联用户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'user_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'dept_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'position_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for org_position
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[org_position]') AND type in (N'U'))
DROP TABLE [dbo].[org_position]
GO

CREATE TABLE [dbo].[org_position] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [code] nvarchar(32) NOT NULL,
  [name] nvarchar(64) NOT NULL,
  [level] int NOT NULL,
  [status] int NOT NULL,
  [description] nvarchar(512) NULL,
  [group_id] uniqueidentifier NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位编号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职级', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'level'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态：1正常2停用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位分组', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'group_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for org_position_group
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[org_position_group]') AND type in (N'U'))
DROP TABLE [dbo].[org_position_group]
GO

CREATE TABLE [dbo].[org_position_group] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [group_name] nvarchar(64) NOT NULL,
  [remark] nvarchar(512) NULL,
  [parent_id] uniqueidentifier NULL,
  [parent_ids] nvarchar(1024) NULL,
  [sort] int NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位分组', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'分组名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'group_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'父ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'parent_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'层级父ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'parent_ids'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序值', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'sort'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 系统配置表
-- ============================

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_config]') AND type in (N'U'))
DROP TABLE [dbo].[sys_config]
GO

CREATE TABLE [dbo].[sys_config] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(256) NOT NULL,
  [key] nvarchar(128) NOT NULL,
  [value] nvarchar(255) NOT NULL,
  [group_key] nvarchar(64) NULL,
  [remark] nvarchar(512) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统配置', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'配置名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'配置键名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'key'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'配置键值', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'value'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'组别', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'group_key'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dept]') AND type in (N'U'))
DROP TABLE [dbo].[sys_dept]
GO

CREATE TABLE [dbo].[sys_dept] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [code] nvarchar(32) NOT NULL,
  [name] nvarchar(64) NOT NULL,
  [sort] int NOT NULL,
  [description] nvarchar(512) NULL,
  [status] int NOT NULL,
  [curator_id] uniqueidentifier NULL,
  [email] nvarchar(64) NULL,
  [phone] nvarchar(64) NULL,
  [parent_id] uniqueidentifier NULL,
  [parent_ids] nvarchar(1024) NULL,
  [layer] int NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门编号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'sort'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态：1正常2停用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'负责人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'curator_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'邮箱', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'email'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'电话', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'phone'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'父ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'parent_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'层级父ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'parent_ids'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'层级', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'layer'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 主键约束
-- ============================
ALTER TABLE [dbo].[api_access_log] ADD CONSTRAINT [PK_api_access_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[exception_log] ADD CONSTRAINT [PK_exception_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[log_record] ADD CONSTRAINT [PK_log_record] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_employee] ADD CONSTRAINT [PK_org_employee] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_position] ADD CONSTRAINT [PK_org_position] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_position_group] ADD CONSTRAINT [PK_org_position_group] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_config] ADD CONSTRAINT [PK_sys_config] PRIMARY KEY ([id])
GO
-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dict_data]') AND type in (N'U'))
DROP TABLE [dbo].[sys_dict_data]
GO

CREATE TABLE [dbo].[sys_dict_data] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [value] nvarchar(128) NOT NULL,
  [label] nvarchar(128) NOT NULL,
  [type] nvarchar(64) NOT NULL,
  [description] nvarchar(512) NULL,
  [sort] int NOT NULL,
  [is_enabled] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典数据表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典值', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'value'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典标签', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'label'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典类型', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'sort'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否启用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'is_enabled'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_data', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dict_type]') AND type in (N'U'))
DROP TABLE [dbo].[sys_dict_type]
GO

CREATE TABLE [dbo].[sys_dict_type] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(128) NOT NULL,
  [type] nvarchar(64) NOT NULL,
  [description] nvarchar(512) NULL,
  [is_enabled] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典类型表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'字典类型', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否启用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'is_enabled'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dict_type', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_login_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_login_log]') AND type in (N'U'))
DROP TABLE [dbo].[sys_login_log]
GO

CREATE TABLE [dbo].[sys_login_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [user_name] nvarchar(32) NOT NULL,
  [ip] nvarchar(32) NULL,
  [location] nvarchar(255) NULL,
  [browser] nvarchar(512) NULL,
  [os] nvarchar(255) NULL,
  [status] int NOT NULL,
  [message] nvarchar(255) NULL,
  [login_time] datetime2(6) NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录日志表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'user_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'IP地址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'ip'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录地点', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'location'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'浏览器', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'browser'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'操作系统', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'os'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录状态', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'提示消息', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登录时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'login_time'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_login_log', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_menu]') AND type in (N'U'))
DROP TABLE [dbo].[sys_menu]
GO

CREATE TABLE [dbo].[sys_menu] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(64) NOT NULL,
  [icon] nvarchar(128) NULL,
  [path] nvarchar(255) NULL,
  [component] nvarchar(255) NULL,
  [type] int NOT NULL,
  [permission] nvarchar(128) NULL,
  [parent_id] uniqueidentifier NULL,
  [sort] int NOT NULL,
  [is_visible] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单图标', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'icon'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'路由路径', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'path'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'组件路径', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'component'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单类型', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'权限标识', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'permission'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'父菜单ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'parent_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'显示顺序', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'sort'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否可见', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'is_visible'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_menu', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_notification
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_notification]') AND type in (N'U'))
DROP TABLE [dbo].[sys_notification]
GO

CREATE TABLE [dbo].[sys_notification] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [title] nvarchar(255) NOT NULL,
  [content] ntext NULL,
  [type] int NOT NULL,
  [is_read] bit NOT NULL,
  [read_time] datetime2(6) NULL,
  [user_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知标题', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'title'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知内容', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'content'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'通知类型', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否已读', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'is_read'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'阅读时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'read_time'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'user_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_notification', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role]') AND type in (N'U'))
DROP TABLE [dbo].[sys_role]
GO

CREATE TABLE [dbo].[sys_role] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [name] nvarchar(64) NOT NULL,
  [description] nvarchar(512) NULL,
  [sort] int NOT NULL,
  [data_scope] nvarchar(255) NULL,
  [is_enabled] bit NOT NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'显示顺序', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'sort'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'数据范围', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'data_scope'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否启用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role', @level2type=N'COLUMN', @level2name=N'is_enabled'
GO

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_role_menu]') AND type in (N'U'))
DROP TABLE [dbo].[sys_role_menu]
GO

CREATE TABLE [dbo].[sys_role_menu] (
  [id] uniqueidentifier NOT NULL,
  [menu_id] uniqueidentifier NOT NULL,
  [role_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色菜单关联表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_menu'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'菜单ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_menu', @level2type=N'COLUMN', @level2name=N'menu_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_menu', @level2type=N'COLUMN', @level2name=N'role_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_role_menu', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_user]') AND type in (N'U'))
DROP TABLE [dbo].[sys_user]
GO

CREATE TABLE [dbo].[sys_user] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [user_name] nvarchar(32) NOT NULL,
  [password] nvarchar(512) NOT NULL,
  [password_salt] nvarchar(256) NOT NULL,
  [avatar] nvarchar(256) NULL,
  [nick_name] nvarchar(64) NOT NULL,
  [sex] int NOT NULL,
  [is_enabled] bit NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'user_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'密码', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'password'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'密码盐', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'password_salt'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'头像', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'avatar'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'昵称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'nick_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'性别', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'sex'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否启用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'is_enabled'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_user_role]') AND type in (N'U'))
DROP TABLE [dbo].[sys_user_role]
GO

CREATE TABLE [dbo].[sys_user_role] (
  [id] uniqueidentifier NOT NULL,
  [user_id] uniqueidentifier NOT NULL,
  [role_id] uniqueidentifier NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户角色关联表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user_role'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user_role', @level2type=N'COLUMN', @level2name=N'user_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'角色ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user_role', @level2type=N'COLUMN', @level2name=N'role_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_user_role', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 主键约束 (更新)
-- ============================
ALTER TABLE [dbo].[api_access_log] ADD CONSTRAINT [PK_api_access_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[exception_log] ADD CONSTRAINT [PK_exception_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[log_record] ADD CONSTRAINT [PK_log_record] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_employee] ADD CONSTRAINT [PK_org_employee] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_position] ADD CONSTRAINT [PK_org_position] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_position_group] ADD CONSTRAINT [PK_org_position_group] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_config] ADD CONSTRAINT [PK_sys_config] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_dept] ADD CONSTRAINT [PK_sys_dept] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_dict_data] ADD CONSTRAINT [PK_sys_dict_data] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_dict_type] ADD CONSTRAINT [PK_sys_dict_type] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_login_log] ADD CONSTRAINT [PK_sys_login_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_menu] ADD CONSTRAINT [PK_sys_menu] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_notification] ADD CONSTRAINT [PK_sys_notification] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_role] ADD CONSTRAINT [PK_sys_role] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_role_menu] ADD CONSTRAINT [PK_sys_role_menu] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_user] ADD CONSTRAINT [PK_sys_user] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_user_role] ADD CONSTRAINT [PK_sys_user_role] PRIMARY KEY ([id])
GO

-- ============================
-- 数据插入脚本 (从PostgreSQL转换)
-- ============================

-- ----------------------------
-- Records of org_employee
-- ----------------------------
INSERT INTO [org_employee] VALUES ('6869907c-9c93-beac-0062-34172a640e0e', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-07-05 20:52:12.138175', '2025-07-05 21:45:13.910127', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', 0, NULL, NULL, '001', 'tom', 1, '18211114444', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-05 12:51:58.7', NULL, 1, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Records of org_position
-- ----------------------------
INSERT INTO [org_position] VALUES ('686a9bac-aad5-2c54-003c-a0450445a76f', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-07-06 15:52:12.404982', NULL, NULL, 0, NULL, NULL, '001', '前端开发工程师', 1, 1, NULL, '685f2f1d-7ef6-c114-0022-88b738cf5c1c', NULL);

-- ----------------------------
-- Records of org_position_group
-- ----------------------------
INSERT INTO [org_position_group] VALUES ('685f2f1d-7ef6-c114-0022-88b738cf5c1c', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-06-27 23:54:05.887842', NULL, NULL, '前端', NULL, '685f2cd3-7ef6-c114-0022-88b50a861368', '685f2cd3-7ef6-c114-0022-88b50a861368', 0, NULL);
INSERT INTO [org_position_group] VALUES ('685f2cd3-7ef6-c114-0022-88b50a861368', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-06-27 23:44:19.081278', '2025-06-28 15:57:31.895397', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '软件', '软件研发岗位', NULL, NULL, 1, NULL);

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO [sys_dept] VALUES ('6861a4ed-de18-91c8-009e-ef9b36642c3c', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-06-29 20:41:17.23825', NULL, NULL, 0, NULL, NULL, 'root', '风汐科技有限公司', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO [sys_dept] VALUES ('6861a543-de18-91c8-009e-ef9c48eee11a', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-06-29 20:42:43.252466', '2025-06-29 20:52:55.936051', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', 0, NULL, NULL, 'IT-01', '研发部', 1, '', 1, NULL, '<EMAIL>', '18211112222', '6861a4ed-de18-91c8-009e-ef9b36642c3c', '6861a4ed-de18-91c8-009e-ef9b36642c3c', 2, NULL);

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO [sys_dict_data] VALUES ('68602622-e22b-e780-00f8-5c9d688361b4', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-06-28 17:28:02.919064', '2025-06-28 17:28:28.158885', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '1', 'L1', 'positionLevel', NULL, 1, 1, NULL);
INSERT INTO [sys_dict_data] VALUES ('6860262d-e22b-e780-00f8-5c9e382875ed', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-06-28 17:28:13.873205', '2025-06-28 17:28:31.396049', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2', 'L2', 'positionLevel', NULL, 2, 1, NULL);
INSERT INTO [sys_dict_data] VALUES ('68602667-e22b-e780-00f8-5c9f2187a1d8', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-06-28 17:29:11.307524', NULL, NULL, '3', 'L3', 'positionLevel', NULL, 3, 1, NULL);

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO [sys_dict_type] VALUES ('6860261b-e22b-e780-00f8-5c9b126fd27d', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-06-28 17:27:55.475365', NULL, NULL, '职位职级', 'positionLevel', NULL, 1, NULL);

-- ----------------------------
-- Records of sys_menu (Part 1 - Main Menus)
-- ----------------------------
INSERT INTO [sys_menu] VALUES ('3a132d0c-0a70-b4c5-1ffd-1088c23ae02a', '3a132908-ca06-34de-164e-21c96505a036', '2024-06-15 15:49:13.507', NULL, NULL, '系统管理', 'antd:SettingOutlined', '/system', NULL, 1, 'System', NULL, 2, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a13a4fe-6f74-733b-a628-6125c0325481', '3a13a4f2-568e-41fe-55e7-210cc37b6d8a', '2024-07-08 22:48:47.742', NULL, NULL, '组织架构', 'antd:TeamOutlined', '/org', NULL, 1, 'Org', NULL, 1, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a132d16-df35-09cb-9f50-0a83e8290575', '3a132908-ca06-34de-164e-21c96505a036', '2024-06-15 16:01:03.301', '2025-06-27 22:33:01.930601', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '用户管理', '', '/system/user', 'system/user', 2, '', '3a132d0c-0a70-b4c5-1ffd-1088c23ae02a', 1, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a132d1f-2026-432a-885f-bf6b10bec15c', '3a132908-ca06-34de-164e-21c96505a036', '2024-06-15 16:10:04.215', NULL, NULL, '角色管理', NULL, '/system/role', 'system/role', 2, 'Sys:Role', '3a132d0c-0a70-b4c5-1ffd-1088c23ae02a', 2, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a132d1f-e2dd-7447-ac4b-2250201a9bad', '3a132908-ca06-34de-164e-21c96505a036', '2024-06-15 16:10:54.046', NULL, NULL, '菜单管理', NULL, '/system/menu', 'system/menu', 2, 'Sys:Menu', '3a132d0c-0a70-b4c5-1ffd-1088c23ae02a', 3, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a198c3b-bf80-dce3-f433-f9f221339227', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-04-28 09:41:59.313', NULL, NULL, '数据字典', NULL, '/system/dict', 'system/dictType', 2, 'Sys:Dict', '3a132d0c-0a70-b4c5-1ffd-1088c23ae02a', 4, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a198d86-8791-5c15-2dac-dada4eeda0fd', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-04-28 15:43:17.394', NULL, NULL, '字典项', NULL, '/system/dictItem/:dictType', 'system/dictData', 2, 'Sys:DictData', '3a132d0c-0a70-b4c5-1ffd-1088c23ae02a', 5, 0, NULL);
INSERT INTO [sys_menu] VALUES ('3a138b12-93b5-e723-1539-adaeb17a2ae1', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-07-03 22:00:40.118', '2025-07-02 21:53:12.229777', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '登录日志', NULL, '/system/log/login', 'system/log/loginLog', 2, '', '3a132d0c-0a70-b4c5-1ffd-1088c23ae02a', 6, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a138b13-fccd-7b4a-0bb5-435a9d9c4172', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-07-03 22:02:12.559', '2025-07-02 21:53:32.960656', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '业务日志', NULL, '/system/log/business', 'system/log/businessLog', 2, '', '3a132d0c-0a70-b4c5-1ffd-1088c23ae02a', 7, 1, NULL);

-- ----------------------------
-- Records of sys_menu (Part 2 - Organization Menus)
-- ----------------------------
INSERT INTO [sys_menu] VALUES ('3a13bcf2-3701-be8e-4ec8-ad5f77536101', '3a13bc48-e3c9-4c0b-0cc4-b6fc4e606741', '2024-07-13 14:26:20.046', '2025-07-11 00:27:56.260876', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '职位分组', NULL, '/org/positionGroup', 'org/positionGroup', 2, '', '3a13a4fe-6f74-733b-a628-6125c0325481', 1, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a13bdaf-34ea-bf3c-c7eb-1d1cfd91d361', '3a13bc48-e3c9-4c0b-0cc4-b6fc4e606741', '2024-07-13 17:52:45.803', NULL, NULL, '职位管理', NULL, '/org/position', 'org/position', 2, 'Org:Position', '3a13a4fe-6f74-733b-a628-6125c0325481', 2, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a13be18-7fe2-2163-01ba-4a86ca6a7c40', '3a13bc48-e3c9-4c0b-0cc4-b6fc4e606741', '2024-07-13 19:47:46.294', NULL, NULL, '部门管理', NULL, '/org/dept', 'org/dept', 2, 'Org:Department', '3a13a4fe-6f74-733b-a628-6125c0325481', 3, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a13be49-5f19-8ebd-5dda-1cf390060a09', '3a13bc48-e3c9-4c0b-0cc4-b6fc4e606741', '2024-07-13 20:41:09.171', NULL, NULL, '员工列表', NULL, '/org/employee', 'org/employee', 2, 'Org:Employee', '3a13a4fe-6f74-733b-a628-6125c0325481', 4, 1, NULL);

-- ----------------------------
-- Records of sys_menu (Part 3 - Monitor Menus)
-- ----------------------------
INSERT INTO [sys_menu] VALUES ('3a174174-857e-2328-55e6-395fcffb3774', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-01-04 11:06:54.207', '2025-07-10 22:39:51.771442', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '系统监控', 'antd:FundOutlined', '/monitor', NULL, 1, '', NULL, 3, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a174175-1893-a38e-c4a2-837cd49e79f6', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-01-04 11:07:31.86', '2025-07-10 22:34:55.638327', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '在线用户', NULL, '/monitor/onlineUser', 'monitor/onlineUser', 2, '', '3a174174-857e-2328-55e6-395fcffb3774', 1, 1, NULL);
INSERT INTO [sys_menu] VALUES ('6865aa1d-4217-02ac-0070-b65d05235757', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-07-02 21:52:29.472722', '2025-07-02 21:52:44.816966', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '异常日志', NULL, '/monitor/exceptionLog', 'monitor/exceptionLog', 2, '', '3a174174-857e-2328-55e6-395fcffb3774', 2, 1, NULL);
INSERT INTO [sys_menu] VALUES ('6865a9ef-4217-02ac-0070-b65a7ed7d760', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-07-02 21:51:43.566378', '2025-07-02 21:51:55.291719', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '访问日志', NULL, '/monitor/apiAccessLog', 'monitor/apiAccessLog', 2, '', '3a174174-857e-2328-55e6-395fcffb3774', 3, 1, NULL);

-- ----------------------------
-- Records of sys_menu (Part 4 - User Permissions)
-- ----------------------------
INSERT INTO [sys_menu] VALUES ('3a135caa-6050-b8fa-ba75-6aaf548a7683', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:44:19.284', NULL, NULL, '新增', NULL, NULL, NULL, 3, 'Sys.User.Add', '3a132d16-df35-09cb-9f50-0a83e8290575', 1, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a135caa-b115-4de4-3be5-4b3cc477d8f4', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:44:39.958', NULL, NULL, '查询', NULL, NULL, NULL, 3, 'Sys.User.List', '3a132d16-df35-09cb-9f50-0a83e8290575', 2, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a135cab-3200-f6de-41f9-948404a81884', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:45:12.962', NULL, NULL, '删除', NULL, NULL, NULL, 3, 'Sys.User.Delete', '3a132d16-df35-09cb-9f50-0a83e8290575', 3, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a135cab-7acb-41cf-30c4-720eea400b2c', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:45:31.598', NULL, NULL, '分配角色', NULL, NULL, NULL, 3, 'Sys.User.AssignRole', '3a132d16-df35-09cb-9f50-0a83e8290575', 4, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a135cab-fcbb-1f19-8416-25c218db4279', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:46:04.86', NULL, NULL, '启用/禁用', NULL, NULL, NULL, 3, 'Sys.User.SwitchEnabledStatus', '3a132d16-df35-09cb-9f50-0a83e8290575', 5, 1, NULL);
INSERT INTO [sys_menu] VALUES ('686a9add-aad5-2c54-003c-a0434dfa66e6', '3a172a37-55d5-ee9b-dc92-e07386eadc7c', '2025-07-06 15:48:45.260116', NULL, NULL, '重置密码', NULL, NULL, NULL, 3, 'Sys.User.ResetPwd', '3a132d16-df35-09cb-9f50-0a83e8290575', 9, 0, NULL);

-- ----------------------------
-- Records of sys_menu (Part 5 - Role Permissions)
-- ----------------------------
INSERT INTO [sys_menu] VALUES ('87cd2f63-3230-11ef-afb3-0242ac110003', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:44:19.284', NULL, NULL, '新增', NULL, NULL, NULL, 3, 'Sys.Role.Add', '3a132d1f-2026-432a-885f-bf6b10bec15c', 1, 1, NULL);
INSERT INTO [sys_menu] VALUES ('9a844856-3230-11ef-afb3-0242ac110003', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:44:39.958', NULL, NULL, '查询', NULL, NULL, NULL, 3, 'Sys.Role.List', '3a132d1f-2026-432a-885f-bf6b10bec15c', 2, 1, NULL);
INSERT INTO [sys_menu] VALUES ('3a135cb0-3753-ae33-82fd-603c3622f661', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:50:42.004', NULL, NULL, '编辑', NULL, NULL, NULL, 3, 'Sys.Role.Update', '3a132d1f-2026-432a-885f-bf6b10bec15c', 3, 1, NULL);
INSERT INTO [sys_menu] VALUES ('9a84d205-3230-11ef-afb3-0242ac110003', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:45:12.962', NULL, NULL, '删除', NULL, NULL, NULL, 3, 'Sys.Role.Delete', '3a132d1f-2026-432a-885f-bf6b10bec15c', 4, 1, NULL);
INSERT INTO [sys_menu] VALUES ('9a8546e3-3230-11ef-afb3-0242ac110003', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:45:31.598', NULL, NULL, '分配菜单', NULL, NULL, NULL, 3, 'Sys.Role.AssignMenu', '3a132d1f-2026-432a-885f-bf6b10bec15c', 5, 1, NULL);

-- ----------------------------
-- Records of sys_menu (Part 6 - Menu Permissions)
-- ----------------------------
INSERT INTO [sys_menu] VALUES ('5c7548d7-3231-11ef-afb3-0242ac110003', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:44:19.284', NULL, NULL, '新增', NULL, NULL, NULL, 3, 'Sys.Menu.Add', '3a132d1f-e2dd-7447-ac4b-2250201a9bad', 1, 1, NULL);
INSERT INTO [sys_menu] VALUES ('5c75c0d0-3231-11ef-afb3-0242ac110003', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:44:39.958', NULL, NULL, '查询', NULL, NULL, NULL, 3, 'Sys.Menu.List', '3a132d1f-e2dd-7447-ac4b-2250201a9bad', 2, 1, NULL);
INSERT INTO [sys_menu] VALUES ('5c74b782-3231-11ef-afb3-0242ac110003', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:50:42.004', NULL, NULL, '编辑', NULL, NULL, NULL, 3, 'Sys.Menu.Update', '3a132d1f-e2dd-7447-ac4b-2250201a9bad', 3, 1, NULL);
INSERT INTO [sys_menu] VALUES ('5c767046-3231-11ef-afb3-0242ac110003', '3a1356b8-6f63-a393-1f8d-4ab9dc4914f4', '2024-06-24 21:45:12.962', NULL, NULL, '删除', NULL, NULL, NULL, 3, 'Sys.Menu.Delete', '3a132d1f-e2dd-7447-ac4b-2250201a9bad', 4, 1, NULL);
