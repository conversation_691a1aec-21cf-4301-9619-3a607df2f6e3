/*
 完整数据库结构和数据脚本 - SQL Server版本
 
 Source Server Type    : PostgreSQL
 Target Server Type    : SQL Server
 File Encoding         : UTF-8
 
 Date: 2025/07/29
 Converted from PostgreSQL to SQL Server
*/

-- ============================
-- 日志和访问记录表
-- ============================

-- ----------------------------
-- Table structure for api_access_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[api_access_log]') AND type in (N'U'))
DROP TABLE [dbo].[api_access_log]
GO

CREATE TABLE [dbo].[api_access_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [path] nvarchar(255) NULL,
  [method] nvarchar(255) NULL,
  [ip] nvarchar(32) NULL,
  [request_time] datetime2(6) NOT NULL,
  [response_time] datetime2(6) NULL,
  [duration] bigint NULL,
  [user_id] uniqueidentifier NULL,
  [user_name] nvarchar(255) NULL,
  [request_body] nvarchar(255) NULL,
  [response_body] ntext NULL,
  [browser] nvarchar(512) NULL,
  [query_string] nvarchar(255) NULL,
  [trace_id] nvarchar(255) NULL,
  [operate_type] nvarchar(255) NULL, -- 注意：PostgreSQL的int4[]数组类型转换为字符串存储
  [operate_name] nvarchar(255) NULL,
  [tenant_id] nvarchar(255) NULL
)
GO

-- ----------------------------
-- Table structure for exception_log
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[exception_log]') AND type in (N'U'))
DROP TABLE [dbo].[exception_log]
GO

CREATE TABLE [dbo].[exception_log] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [exception_type] nvarchar(255) NULL,
  [message] ntext NULL,
  [stack_trace] ntext NULL,
  [inner_exception] ntext NULL,
  [request_path] nvarchar(255) NULL,
  [request_method] nvarchar(255) NULL,
  [user_id] uniqueidentifier NULL,
  [user_name] nvarchar(255) NULL,
  [ip] nvarchar(32) NULL,
  [browser] nvarchar(512) NULL,
  [trace_id] nvarchar(255) NULL,
  [is_handled] bit NOT NULL,
  [handled_time] datetime2(6) NULL,
  [handled_by] nvarchar(255) NULL,
  [tenant_id] nvarchar(255) NULL
)
GO

-- ----------------------------
-- Table structure for log_record
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[log_record]') AND type in (N'U'))
DROP TABLE [dbo].[log_record]
GO

CREATE TABLE [dbo].[log_record] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [type] nvarchar(255) NOT NULL,
  [sub_type] nvarchar(255) NOT NULL,
  [biz_no] nvarchar(255) NOT NULL,
  [content] nvarchar(255) NOT NULL,
  [browser] nvarchar(512) NULL,
  [ip] nvarchar(32) NULL,
  [trace_id] nvarchar(255) NULL,
  [tenant_id] nvarchar(255) NULL,
  [user_id] uniqueidentifier NULL,
  [user_name] nvarchar(255) NULL
)
GO

-- ============================
-- 组织架构表
-- ============================

-- ----------------------------
-- Table structure for org_employee
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[org_employee]') AND type in (N'U'))
DROP TABLE [dbo].[org_employee]
GO

CREATE TABLE [dbo].[org_employee] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [code] nvarchar(64) NOT NULL,
  [name] nvarchar(64) NOT NULL,
  [sex] int NOT NULL,
  [phone] nvarchar(16) NOT NULL,
  [id_no] nvarchar(32) NULL,
  [front_id_no_url] nvarchar(512) NULL,
  [back_id_no_url] nvarchar(512) NULL,
  [birthday] datetime2(6) NULL,
  [address] nvarchar(512) NULL,
  [email] nvarchar(64) NULL,
  [in_time] datetime2(6) NOT NULL,
  [out_time] datetime2(6) NULL,
  [status] int NOT NULL,
  [user_id] uniqueidentifier NULL,
  [dept_id] uniqueidentifier NULL,
  [position_id] uniqueidentifier NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'员工表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'姓名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'性别', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'sex'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'手机号码', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'phone'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'身份证', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'id_no'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'身份证正面', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'front_id_no_url'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'身份证背面', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'back_id_no_url'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'生日', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'birthday'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'现住址', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'address'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'邮箱', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'email'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入职时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'in_time'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'离职时间', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'out_time'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态 1正常2离职', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联用户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'user_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'dept_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'position_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_employee', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for org_position
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[org_position]') AND type in (N'U'))
DROP TABLE [dbo].[org_position]
GO

CREATE TABLE [dbo].[org_position] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [code] nvarchar(32) NOT NULL,
  [name] nvarchar(64) NOT NULL,
  [level] int NOT NULL,
  [status] int NOT NULL,
  [description] nvarchar(512) NULL,
  [group_id] uniqueidentifier NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位编号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职级', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'level'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态：1正常2停用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位分组', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'group_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for org_position_group
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[org_position_group]') AND type in (N'U'))
DROP TABLE [dbo].[org_position_group]
GO

CREATE TABLE [dbo].[org_position_group] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [group_name] nvarchar(64) NOT NULL,
  [remark] nvarchar(512) NULL,
  [parent_id] uniqueidentifier NULL,
  [parent_ids] nvarchar(1024) NULL,
  [sort] int NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'职位分组', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'分组名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'group_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'父ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'parent_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'层级父ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'parent_ids'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序值', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'sort'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'org_position_group', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 系统配置表
-- ============================

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_config]') AND type in (N'U'))
DROP TABLE [dbo].[sys_config]
GO

CREATE TABLE [dbo].[sys_config] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [name] nvarchar(256) NOT NULL,
  [key] nvarchar(128) NOT NULL,
  [value] nvarchar(255) NOT NULL,
  [group_key] nvarchar(64) NULL,
  [remark] nvarchar(512) NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'系统配置', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'配置名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'配置键名', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'key'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'配置键值', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'value'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'组别', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'group_key'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'备注', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'remark'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_config', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sys_dept]') AND type in (N'U'))
DROP TABLE [dbo].[sys_dept]
GO

CREATE TABLE [dbo].[sys_dept] (
  [id] uniqueidentifier NOT NULL,
  [creator_id] uniqueidentifier NOT NULL,
  [creation_time] datetime2(6) NOT NULL,
  [last_modification_time] datetime2(6) NULL,
  [last_modifier_id] uniqueidentifier NULL,
  [is_deleted] bit NOT NULL,
  [deleter_id] uniqueidentifier NULL,
  [deletion_time] datetime2(6) NULL,
  [code] nvarchar(32) NOT NULL,
  [name] nvarchar(64) NOT NULL,
  [sort] int NOT NULL,
  [description] nvarchar(512) NULL,
  [status] int NOT NULL,
  [curator_id] uniqueidentifier NULL,
  [email] nvarchar(64) NULL,
  [phone] nvarchar(64) NULL,
  [parent_id] uniqueidentifier NULL,
  [parent_ids] nvarchar(1024) NULL,
  [layer] int NOT NULL,
  [tenant_id] nvarchar(18) NULL
)
GO

-- 添加表和列注释
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门表', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门编号', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部门名称', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'排序', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'sort'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'描述', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状态：1正常2停用', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'负责人', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'curator_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'邮箱', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'email'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'电话', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'phone'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'父ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'parent_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'层级父ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'parent_ids'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'层级', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'layer'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'租户ID', @level0type=N'SCHEMA', @level0name=N'dbo', @level1type=N'TABLE', @level1name=N'sys_dept', @level2type=N'COLUMN', @level2name=N'tenant_id'
GO

-- ============================
-- 主键约束
-- ============================
ALTER TABLE [dbo].[api_access_log] ADD CONSTRAINT [PK_api_access_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[exception_log] ADD CONSTRAINT [PK_exception_log] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[log_record] ADD CONSTRAINT [PK_log_record] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_employee] ADD CONSTRAINT [PK_org_employee] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_position] ADD CONSTRAINT [PK_org_position] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[org_position_group] ADD CONSTRAINT [PK_org_position_group] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_config] ADD CONSTRAINT [PK_sys_config] PRIMARY KEY ([id])
GO
ALTER TABLE [dbo].[sys_dept] ADD CONSTRAINT [PK_sys_dept] PRIMARY KEY ([id])
GO
