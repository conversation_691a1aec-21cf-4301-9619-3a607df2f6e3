﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AStackFrameIterator_002Ecs_002Fl_003AC_0021_003FUsers_003Fjarva_003FAppData_003FLocal_003FSymbols_003Fsrc_003Fdotnet_003Fruntime_003F3c298d9f00936d651cc47d221762474e25277672_003Fsrc_003Fcoreclr_003Fnativeaot_003FRuntime_002EBase_003Fsrc_003FSystem_003FRuntime_003FStackFrameIterator_002Ecs/@EntryIndexedValue">ForceIncluded</s:String></wpf:ResourceDictionary>