import { render, act } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from '@/store';
import LoginPage from '../login';
import { AuthProvider } from '@/components/AuthProvider';

// Mock AuthProvider
jest.mock('@/components/AuthProvider', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuthProvider: () => ({
    pwdLogin: jest.fn(),
  }),
}));

const renderLoginPage = () => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      </BrowserRouter>
    </Provider>
  );
};

// Mock performance API
const mockPerformance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByType: jest.fn(() => []),
  getEntriesByName: jest.fn(() => []),
};

Object.defineProperty(window, 'performance', {
  value: mockPerformance,
});

describe('LoginPage Performance', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders within acceptable time', async () => {
    const startTime = performance.now();
    
    await act(async () => {
      renderLoginPage();
    });
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // 渲染时间应该小于100ms
    expect(renderTime).toBeLessThan(100);
  });

  test('uses memoization for particle generation', () => {
    const { rerender } = renderLoginPage();
    
    const initialParticles = document.querySelectorAll('.ai-particle');
    
    // 重新渲染组件
    rerender(
      <Provider store={store}>
        <BrowserRouter>
          <AuthProvider>
            <LoginPage />
          </AuthProvider>
        </BrowserRouter>
      </Provider>
    );
    
    const rerenderedParticles = document.querySelectorAll('.ai-particle');
    
    // 粒子数量应该保持一致
    expect(initialParticles).toHaveLength(20);
    expect(rerenderedParticles).toHaveLength(20);
  });

  test('applies will-change property for optimized animations', () => {
    renderLoginPage();
    
    const container = document.querySelector('.login-container');
    const particles = document.querySelectorAll('.ai-particle');
    const loginButton = document.querySelector('.ai-login-button');
    
    // 检查关键元素是否应用了will-change属性
    expect(container).toBeInTheDocument();
    expect(particles).toHaveLength(20);
    expect(loginButton).toBeInTheDocument();
  });

  test('handles reduced motion preference', () => {
    // Mock prefers-reduced-motion
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });
    
    renderLoginPage();
    
    // 验证在减少动画偏好下，动画被禁用
    const particles = document.querySelectorAll('.ai-particle');
    expect(particles).toHaveLength(20);
  });

  test('lazy loads non-critical resources', () => {
    renderLoginPage();
    
    // 验证关键资源立即加载，非关键资源延迟加载
    const container = document.querySelector('.login-container');
    const brandSection = document.querySelector('.ai-brand-section');
    
    expect(container).toBeInTheDocument();
    expect(brandSection).toBeInTheDocument();
  });

  test('uses efficient CSS selectors', () => {
    renderLoginPage();
    
    // 验证CSS类名的存在，确保样式正确应用
    const aiElements = document.querySelectorAll('[class*="ai-"]');
    expect(aiElements.length).toBeGreaterThan(0);
  });

  test('minimizes DOM manipulations', () => {
    const { rerender } = renderLoginPage();
    
    const initialElementCount = document.querySelectorAll('*').length;
    
    // 重新渲染
    rerender(
      <Provider store={store}>
        <BrowserRouter>
          <AuthProvider>
            <LoginPage />
          </AuthProvider>
        </BrowserRouter>
      </Provider>
    );
    
    const finalElementCount = document.querySelectorAll('*').length;
    
    // DOM元素数量应该保持稳定
    expect(finalElementCount).toBe(initialElementCount);
  });
});