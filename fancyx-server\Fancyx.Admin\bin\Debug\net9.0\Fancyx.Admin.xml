<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Fancyx.Admin</name>
    </assembly>
    <members>
        <member name="M:Fancyx.Admin.Controllers.Account.AccountController.LoginAsync(Fancyx.Admin.IService.Account.Dtos.LoginDto)">
            <summary>
            登录
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Account.AccountController.GetAccessTokenAsync(System.String)">
            <summary>
            刷新token
            </summary>
            <param name="refreshToken"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Account.AccountController.UpdateUserInfoAsync(Fancyx.Admin.IService.Account.Dtos.PersonalInfoDto)">
            <summary>
            修改个人基本信息
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Account.AccountController.UpdateUserPwdAsync(Fancyx.Admin.IService.Account.Dtos.UserPwdDto)">
            <summary>
            修改个人密码
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Account.AccountController.SignOutAsync">
            <summary>
            注销
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Account.AccountController.GetUserAuthInfoAsync">
            <summary>
            用户权限信息
            </summary>
            <returns></returns>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileAuthController">
            <summary>
            移动端认证API
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileAuthController.LoginAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileLoginDto)">
            <summary>
            移动端登录
            </summary>
            <param name="loginDto">登录参数</param>
            <returns>登录结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileAuthController.RefreshTokenAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileRefreshTokenDto)">
            <summary>
            刷新Token
            </summary>
            <param name="refreshDto">刷新参数</param>
            <returns>新的Token</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileAuthController.LogoutAsync(Fancyx.Admin.Controllers.Mobile.MobileLogoutDto)">
            <summary>
            移动端登出
            </summary>
            <param name="logoutDto">登出参数</param>
            <returns>登出结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileAuthController.GetUserInfoAsync">
            <summary>
            获取移动端用户信息
            </summary>
            <returns>用户信息</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileAuthController.GetTokenStatusAsync">
            <summary>
            检查Token有效性
            </summary>
            <returns>Token状态</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileAuthController.GetDeviceHistoryAsync(System.Int32)">
            <summary>
            获取设备登录历史
            </summary>
            <param name="pageSize">每页数量</param>
            <returns>登录历史</returns>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileTokenResultDto">
            <summary>
            移动端Token结果内部DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileRefreshTokenResultDto">
            <summary>
            移动端刷新Token结果DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileLogoutDto">
            <summary>
            移动端登出DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileTokenStatusDto">
            <summary>
            移动端Token状态DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileDeviceHistoryDto">
            <summary>
            移动端设备历史DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileFileController">
            <summary>
            移动端文件上传API
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileFileController.UploadFileAsync(Microsoft.AspNetCore.Http.IFormFile,System.String,System.Boolean)">
            <summary>
            单文件上传（移动端优化）
            </summary>
            <param name="file">文件</param>
            <param name="fileType">文件类型（photo/voice/signature）</param>
            <param name="compress">是否压缩（仅对图片有效）</param>
            <returns>上传结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileFileController.BatchUploadAsync(System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile},System.String,System.Boolean)">
            <summary>
            批量文件上传
            </summary>
            <param name="files">文件列表</param>
            <param name="fileType">文件类型</param>
            <param name="compress">是否压缩</param>
            <returns>批量上传结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileFileController.CheckFileExistsAsync(Fancyx.Admin.Controllers.Mobile.MobileFileExistsRequestDto)">
            <summary>
            检查文件是否存在（避免重复上传）
            </summary>
            <param name="fileHash">文件MD5哈希值</param>
            <param name="fileSize">文件大小</param>
            <returns>检查结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileFileController.GetUploadProgressAsync(System.String)">
            <summary>
            获取上传进度（用于大文件分片上传）
            </summary>
            <param name="uploadId">上传任务ID</param>
            <returns>上传进度</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileFileController.DeleteFileAsync(System.String)">
            <summary>
            删除文件
            </summary>
            <param name="fileUrl">文件URL</param>
            <returns>删除结果</returns>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileFileUploadResultDto">
            <summary>
            移动端文件上传结果DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileBatchUploadResultDto">
            <summary>
            移动端批量上传结果DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileFileExistsRequestDto">
            <summary>
            移动端文件存在检查请求DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileFileExistsResultDto">
            <summary>
            移动端文件存在检查结果DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileUploadProgressDto">
            <summary>
            移动端上传进度DTO
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController">
            <summary>
            移动端隐患明细管理API
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.GetMyHiddenDangersAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerQueryDto)">
            <summary>
            获取我的隐患列表
            </summary>
            <param name="queryDto">查询参数</param>
            <returns>隐患列表</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.CreateHiddenDangerAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto)">
            <summary>
            创建隐患记录（支持AI识别）
            </summary>
            <param name="createDto">创建参数</param>
            <returns>隐患ID</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.UpdateHiddenDangerAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto)">
            <summary>
            更新隐患记录
            </summary>
            <param name="id">隐患ID</param>
            <param name="updateDto">更新参数</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.DeleteHiddenDangerAsync(System.Int64,System.String)">
            <summary>
            删除隐患记录
            </summary>
            <param name="id">隐患ID</param>
            <param name="deleteReason">删除原因</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.GetHiddenDangerDetailAsync(System.Int64)">
            <summary>
            获取隐患详情
            </summary>
            <param name="id">隐患ID</param>
            <returns>隐患详情</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.ConfirmHiddenDangerAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmHiddenDangerDto)">
            <summary>
            确认隐患
            </summary>
            <param name="id">隐患ID</param>
            <param name="confirmDto">确认参数</param>
            <returns>确认结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.AddRectificationRecordAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto)">
            <summary>
            添加整改记录
            </summary>
            <param name="id">隐患ID</param>
            <param name="recordDto">整改记录</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.AiRecognitionAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionRequestDto)">
            <summary>
            AI智能识别隐患
            </summary>
            <param name="recognitionDto">识别参数</param>
            <returns>识别结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.GetAiRecognitionStatusAsync(System.String)">
            <summary>
            获取AI识别任务状态
            </summary>
            <param name="taskId">任务ID</param>
            <returns>任务状态</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.BatchUploadMultimediaAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto)">
            <summary>
            批量上传多媒体文件
            </summary>
            <param name="uploadDto">上传参数</param>
            <returns>上传结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.BatchOperationAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchHiddenDangerOperationDto)">
            <summary>
            批量操作隐患
            </summary>
            <param name="batchDto">批量操作参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.SyncHiddenDangerDataAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto)">
            <summary>
            同步隐患数据到云端
            </summary>
            <param name="syncDto">同步数据</param>
            <returns>同步结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.ExportHiddenDangerReportAsync(System.Collections.Generic.List{System.Int64},System.String)">
            <summary>
            导出隐患报告
            </summary>
            <param name="ids">隐患ID列表</param>
            <param name="format">导出格式（word/pdf/excel）</param>
            <returns>导出结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.GetHiddenDangerStatisticsAsync(System.Nullable{System.Int64})">
            <summary>
            获取隐患统计信息
            </summary>
            <param name="checkId">检查任务ID（可选）</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileHiddenDangerController.GetDangerTemplatesAsync">
            <summary>
            获取隐患模板分类
            </summary>
            <returns>模板分类列表</returns>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController">
            <summary>
            移动端项目检查API
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.GetMyCheckTasksAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto)">
            <summary>
            获取我的检查任务列表
            </summary>
            <param name="queryDto">查询参数</param>
            <returns>检查任务列表</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.CreateCheckTaskAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto)">
            <summary>
            创建检查任务
            </summary>
            <param name="createDto">创建参数</param>
            <returns>检查任务ID</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.UpdateCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto)">
            <summary>
            更新检查任务
            </summary>
            <param name="id">检查任务ID</param>
            <param name="updateDto">更新参数</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.DeleteCheckTaskAsync(System.Int64,System.String)">
            <summary>
            删除检查任务
            </summary>
            <param name="id">检查任务ID</param>
            <param name="deleteReason">删除原因</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.StartCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileStartCheckDto)">
            <summary>
            开始检查任务
            </summary>
            <param name="id">检查任务ID</param>
            <param name="startDto">开始检查参数</param>
            <returns>检查任务详情</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.PauseCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobilePauseCheckDto)">
            <summary>
            暂停检查任务
            </summary>
            <param name="id">检查任务ID</param>
            <param name="pauseDto">暂停参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.ResumeCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileResumeCheckDto)">
            <summary>
            恢复检查任务
            </summary>
            <param name="id">检查任务ID</param>
            <param name="resumeDto">恢复参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.CompleteCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto)">
            <summary>
            完成检查任务
            </summary>
            <param name="id">检查任务ID</param>
            <param name="completeDto">完成信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.GetCheckDetailAsync(System.Int64)">
            <summary>
            获取检查任务详情
            </summary>
            <param name="id">检查任务ID</param>
            <returns>检查任务详情</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.SaveCheckProgressAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto)">
            <summary>
            保存检查进度
            </summary>
            <param name="id">检查任务ID</param>
            <param name="progressDto">进度信息</param>
            <returns>保存结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.BatchOperationAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchCheckOperationDto)">
            <summary>
            批量操作检查任务
            </summary>
            <param name="batchDto">批量操作参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.SyncCheckDataAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto)">
            <summary>
            同步检查数据到云端
            </summary>
            <param name="syncDto">同步数据</param>
            <returns>同步结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectCheckController.ExportCheckReportAsync(System.Int64,System.String)">
            <summary>
            导出检查任务报告
            </summary>
            <param name="id">检查任务ID</param>
            <param name="format">导出格式（word/pdf/excel）</param>
            <returns>导出结果</returns>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Mobile.MobileProjectController">
            <summary>
            移动端项目管理API
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectController.GetSimpleProjectListAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileProjectQueryDto)">
            <summary>
            获取项目简要列表（移动端专用）
            </summary>
            <param name="queryDto">查询参数</param>
            <returns>项目简要信息列表</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectController.GetProjectDetailAsync(System.Int64)">
            <summary>
            获取项目详情（移动端优化）
            </summary>
            <param name="id">项目ID</param>
            <returns>项目详情</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectController.CreateProjectAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto)">
            <summary>
            移动端创建项目
            </summary>
            <param name="createDto">创建参数</param>
            <returns>项目ID</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectController.UpdateProjectAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto)">
            <summary>
            移动端更新项目
            </summary>
            <param name="id">项目ID</param>
            <param name="updateDto">更新参数</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectController.DeleteProjectAsync(System.Int64,System.String)">
            <summary>
            移动端删除项目
            </summary>
            <param name="id">项目ID</param>
            <param name="deleteReason">删除原因</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectController.QuickCreateCheckAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto)">
            <summary>
            快速创建检查任务（移动端专用）
            </summary>
            <param name="id">项目ID</param>
            <param name="createDto">创建参数</param>
            <returns>检查任务ID</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectController.GetProjectStatisticsAsync(System.Int64)">
            <summary>
            获取项目统计信息
            </summary>
            <param name="id">项目ID</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectController.BatchOperationAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchProjectOperationDto)">
            <summary>
            批量操作项目
            </summary>
            <param name="batchDto">批量操作参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Mobile.MobileProjectController.SyncProjectsAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncRequestDto)">
            <summary>
            同步项目到移动端
            </summary>
            <param name="syncDto">同步参数</param>
            <returns>同步结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Monitor.MonitorLogController.GetApiAccessLogListAsync(Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogQueryDto)">
            <summary>
            API访问日志列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Monitor.MonitorLogController.GetExceptionLogListAsync(Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogQueryDto)">
            <summary>
            异常日志列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Monitor.MonitorLogController.HandleExceptionAsync(System.Guid)">
            <summary>
            标记异常已处理
            </summary>
            <param name="exceptionId">异常日志ID</param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Monitor.OnlineUserController.GetOnlineUserListAsync(Fancyx.Admin.IService.Monitor.Dtos.OnlineUserSearchDto)">
            <summary>
            在线用户列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Monitor.OnlineUserController.LogoutAsync(System.String)">
            <summary>
            注销用户会话
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.DeptController.AddDeptAsync(Fancyx.Admin.IService.Organization.Dtos.DeptDto)">
            <summary>
            新增部门
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.DeptController.GetDeptListAsync(Fancyx.Admin.IService.Organization.Dtos.DeptQueryDto)">
            <summary>
            部门树形列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.DeptController.UpdateDeptAsync(Fancyx.Admin.IService.Organization.Dtos.DeptDto)">
            <summary>
            修改部门
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.DeptController.DeleteDeptAsync(System.Guid)">
            <summary>
            删除部门
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.EmployeeController.AddEmployeeAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeDto)">
            <summary>
            新增员工
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.EmployeeController.GetEmployeePagedListAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeQueryDto)">
            <summary>
            员工列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.EmployeeController.UpdateEmployeeAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeDto)">
            <summary>
            修改员工
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.EmployeeController.DeleteEmployeeAsync(System.Guid)">
            <summary>
            删除员工
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.EmployeeController.EmployeeBindUserAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeBindUserDto)">
            <summary>
            绑定用户
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.EmployeeController.GetEmployeeInfoAsync(System.Guid)">
            <summary>
            获取员工信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.EmployeeController.GetEmployeeListAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeQueryDto)">
            <summary>
            员工列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.PositionController.AddPositionAsync(Fancyx.Admin.IService.Organization.Dtos.PositionDto)">
            <summary>
            新增职位
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.PositionController.GetPositionListAsync(Fancyx.Admin.IService.Organization.Dtos.PositionQueryDto)">
            <summary>
            职位分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.PositionController.UpdatePositionAsync(Fancyx.Admin.IService.Organization.Dtos.PositionDto)">
            <summary>
            修改职位
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.PositionController.DeletePositionAsync(System.Guid)">
            <summary>
            删除职位
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.PositionController.GetPositionTreeOptionAsync">
            <summary>
            职位分组+职位树
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.PositionGroupController.AddPositionGroupAsync(Fancyx.Admin.IService.Organization.Dtos.PositionGroupDto)">
            <summary>
            新增职位分组
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.PositionGroupController.GetPositionGroupListAsync(Fancyx.Admin.IService.Organization.Dtos.PositionGroupQueryDto)">
            <summary>
            职位分组分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.PositionGroupController.UpdatePositionGroupAsync(Fancyx.Admin.IService.Organization.Dtos.PositionGroupDto)">
            <summary>
            修改职位分组
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Organization.PositionGroupController.DeletePositionGroupAsync(System.Guid)">
            <summary>
            删除职位分组
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Project.HiddenDangerController">
            <summary>
            隐患明细管理控制器
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.HiddenDangerController.GetHiddenDangerList(Fancyx.Admin.IService.Project.Dtos.HiddenDangerQueryDto)">
            <summary>
            获取隐患明细列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>隐患明细列表</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.HiddenDangerController.GetHiddenDangerById(System.Guid)">
            <summary>
            根据ID获取隐患明细详情
            </summary>
            <param name="id">隐患明细ID</param>
            <returns>隐患明细详情</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.HiddenDangerController.CreateHiddenDanger(Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto)">
            <summary>
            创建隐患明细
            </summary>
            <param name="createDto">隐患明细信息</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.HiddenDangerController.UpdateHiddenDanger(System.Guid,Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto)">
            <summary>
            更新隐患明细
            </summary>
            <param name="id">隐患明细ID</param>
            <param name="updateDto">更新信息</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.HiddenDangerController.DeleteHiddenDanger(System.Guid)">
            <summary>
            删除隐患明细
            </summary>
            <param name="id">隐患明细ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.HiddenDangerController.ConfirmHiddenDanger(System.Guid)">
            <summary>
            确认隐患明细（将待确认状态转为正式记录）
            </summary>
            <param name="id">隐患明细ID</param>
            <returns>确认结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.HiddenDangerController.GetHiddenDangersByCheckId(System.Int64)">
            <summary>
            根据检查ID获取隐患明细列表
            </summary>
            <param name="projectCheckId">检查ID</param>
            <returns>隐患明细列表</returns>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Project.ProjectCheckController">
            <summary>
            项目检查管理控制器
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectCheckController.GetProjectCheckList(Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto)">
            <summary>
            获取项目检查列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>检查列表</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectCheckController.GetProjectCheckById(System.Int64)">
            <summary>
            根据ID获取项目检查详情
            </summary>
            <param name="id">检查ID</param>
            <returns>检查详情</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectCheckController.CreateProjectCheck(Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto)">
            <summary>
            创建项目检查
            </summary>
            <param name="createDto">检查信息</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectCheckController.UpdateProjectCheck(System.Int64,Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto)">
            <summary>
            更新项目检查
            </summary>
            <param name="id">检查ID</param>
            <param name="updateDto">更新信息</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectCheckController.DeleteProjectCheck(System.Int64)">
            <summary>
            删除项目检查
            </summary>
            <param name="id">检查ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="T:Fancyx.Admin.Controllers.Project.ProjectController">
            <summary>
            项目管理控制器
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectController.GetProjectList(Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto)">
            <summary>
            获取项目列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>项目列表</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectController.GetProjectById(System.Int64)">
            <summary>
            根据ID获取项目详情
            </summary>
            <param name="id">项目ID</param>
            <returns>项目详情</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectController.CreateProject(Fancyx.Admin.IService.Project.Dtos.CreateProjectDto)">
            <summary>
            创建项目
            </summary>
            <param name="createDto">项目信息</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectController.UpdateProject(System.Int64,Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto)">
            <summary>
            更新项目
            </summary>
            <param name="id">项目ID</param>
            <param name="updateDto">更新信息</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectController.DeleteProject(System.Int64)">
            <summary>
            删除项目
            </summary>
            <param name="id">项目ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectController.GenerateProjectNo">
            <summary>
            生成项目编号
            </summary>
            <returns>项目编号</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.Project.ProjectController.GenerateCheckTask(System.Int64,System.String,System.String)">
            <summary>
            根据项目生成检查任务
            </summary>
            <param name="projectId">项目ID</param>
            <param name="checkType">检查类型</param>
            <param name="secondaryCategory">二级分类</param>
            <returns>检查任务ID</returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictDataController.AddDictDataAsync(Fancyx.Admin.IService.System.Dtos.DictDataDto)">
            <summary>
            新增字典
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictDataController.GetDictDataListAsync(Fancyx.Admin.IService.System.Dtos.DictDataQueryDto)">
            <summary>
            字典分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictDataController.UpdateDictDataAsync(Fancyx.Admin.IService.System.Dtos.DictDataDto)">
            <summary>
            修改字典
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictDataController.DeleteDictDataAsync(System.Guid[])">
            <summary>
            删除字典
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictTypeController.AddDictTypeAsync(Fancyx.Admin.IService.System.Dtos.DictTypeDto)">
            <summary>
            新增字典类型
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictTypeController.GetDictTypeListAsync(Fancyx.Admin.IService.System.Dtos.DictTypeSearchDto)">
            <summary>
            分页查询字典类型列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictTypeController.UpdateDictTypeAsync(Fancyx.Admin.IService.System.Dtos.DictTypeDto)">
            <summary>
            修改字典类型
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictTypeController.DeleteDictTypeAsync(System.String)">
            <summary>
            删除字典类型
            </summary>
            <param name="dictType"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictTypeController.GetDictDataOptionsAsync(System.String)">
            <summary>
            字典选项
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.DictTypeController.DeleteDictTypesAsync(System.Guid[])">
            <summary>
            批量删除字典类型
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.LogManagement.BusinessLogController.GetBusinessLogListAsync(Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogQueryDto)">
            <summary>
            业务日志分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.LogManagement.BusinessLogController.GetBusinessTypeOptionsAsync(System.String)">
            <summary>
            获取所有业务类型选项
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.LogManagement.LoginLogController.GetLoginLogListAsync(Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogQueryDto)">
            <summary>
            登录日志分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.MenuController.AddMenuAsync(Fancyx.Admin.IService.System.Dtos.MenuDto)">
            <summary>
            新增菜单
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.MenuController.GetMenuListAsync(Fancyx.Admin.IService.System.Dtos.MenuQueryDto)">
            <summary>
            菜单树形列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.MenuController.UpdateMenuAsync(Fancyx.Admin.IService.System.Dtos.MenuDto)">
            <summary>
            修改菜单
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.MenuController.DeleteMenusAsync(System.Guid[])">
            <summary>
            删除菜单
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.MenuController.GetMenuOptionsAsync(System.Boolean,System.String)">
            <summary>
            获取菜单组成的选项树
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.RoleController.AddRoleAsync(Fancyx.Admin.IService.System.Dtos.RoleDto)">
            <summary>
            新增角色
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.RoleController.GetRoleListAsync(Fancyx.Admin.IService.System.Dtos.RoleQueryDto)">
            <summary>
            角色分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.RoleController.UpdateRoleAsync(Fancyx.Admin.IService.System.Dtos.RoleDto)">
            <summary>
            修改角色
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.RoleController.DeleteRoleAsync(System.Guid)">
            <summary>
            删除角色
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.RoleController.AssignMenuAsync(Fancyx.Admin.IService.System.Dtos.AssignMenuDto)">
            <summary>
            分配菜单
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.RoleController.GetRoleOptionsAsync">
            <summary>
            获取角色
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.RoleController.GetRoleMenuIdsAsync(System.Guid)">
            <summary>
            获取指定角色菜单
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.RoleController.AssignDataAsync(Fancyx.Admin.IService.System.Dtos.AssignDataDto)">
            <summary>
            分配数据权限
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.RoleController.GetRolePowerDataAsync(System.Guid)">
            <summary>
            获取角色数据权限
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.TenantController.GetTenantListAsync(Fancyx.Admin.IService.System.Dtos.TenantSearchDto)">
            <summary>
            分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.TenantController.UpdateTenantAsync(Fancyx.Admin.IService.System.Dtos.TenantDto)">
            <summary>
            修改
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.TenantController.DeleteTenantAsync(System.Guid)">
            <summary>
            删除
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.UserController.AddUserAsync(Fancyx.Admin.IService.System.Dtos.UserDto)">
            <summary>
            新增用户
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.UserController.GetUserListAsync(Fancyx.Admin.IService.System.Dtos.UserQueryDto)">
            <summary>
            用户分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.UserController.DeleteUserAsync(System.Guid)">
            <summary>
            删除用户
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.UserController.AssignRoleAsync(Fancyx.Admin.IService.System.Dtos.AssignRoleDto)">
            <summary>
            分配角色
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.UserController.SwitchUserEnabledStatusAsync(System.Guid)">
            <summary>
            切换用户启用状态
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.UserController.GetUserRoleIdsAsync(System.Guid)">
            <summary>
            获取指定用户角色
            </summary>
            <param name="uid"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.UserController.ResetUserPasswordAsync(Fancyx.Admin.IService.System.Dtos.ResetUserPwdDto)">
            <summary>
            重置用户密码
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Controllers.System.UserController.GetUserSimpleInfosAsync(System.String)">
            <summary>
            用户简单信息查询
            </summary>
            <param name="keyword"></param>
            <returns></returns>
        </member>
        <member name="T:Fancyx.Admin.Entities.Organization.DeptDO">
            <summary>
            部门表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.Code">
            <summary>
            部门编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.Name">
            <summary>
            部门名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.Status">
            <summary>
            状态：1正常2停用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.CuratorId">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.Phone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.ParentId">
            <summary>
            父ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.ParentIds">
            <summary>
            层级父ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.Layer">
            <summary>
            层级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.DeptDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.Organization.EmployeeDO">
            <summary>
            员工表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.Code">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.Phone">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.IdNo">
            <summary>
            身份证
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.FrontIdNoUrl">
            <summary>
            身份证正面
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.BackIdNoUrl">
            <summary>
            身份证背面
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.Birthday">
            <summary>
            生日
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.Address">
            <summary>
            现住址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.InTime">
            <summary>
            入职时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.OutTime">
            <summary>
            离职时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.Status">
            <summary>
            状态 1正常2离职
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.UserId">
            <summary>
            关联用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.DeptId">
            <summary>
            部门ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.PositionId">
            <summary>
            职位ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.EmployeeDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.Organization.PositionDO">
            <summary>
            职位表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionDO.Code">
            <summary>
            职位编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionDO.Name">
            <summary>
            职位名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionDO.Level">
            <summary>
            职级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionDO.Status">
            <summary>
            状态：1正常2停用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionDO.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionDO.GroupId">
            <summary>
            职位分组
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.Organization.PositionGroupDO">
            <summary>
            职位分组
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionGroupDO.GroupName">
            <summary>
            分组名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionGroupDO.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionGroupDO.ParentId">
            <summary>
            父ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionGroupDO.ParentIds">
            <summary>
            层级父ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionGroupDO.Sort">
            <summary>
            排序值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Organization.PositionGroupDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.AIPlatformConfigDO.PlatformName">
            <summary>
            平台名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.AIPlatformConfigDO.ApiUrl">
            <summary>
            API地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.AIPlatformConfigDO.ApiKey">
            <summary>
            API Key
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.AIPlatformConfigDO.ModelName">
            <summary>
            模型名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.AIPlatformConfigDO.Timeout">
            <summary>
            请求超时时间（秒）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.AIPlatformConfigDO.Status">
            <summary>
            状态（0启用，1停用）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.AIPlatformConfigDO.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.AIPlatformConfigDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.ProjectCheckId">
            <summary>
            检查ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.StandardId">
            <summary>
            标准规范ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.StandardName">
            <summary>
            标准规范名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.ProblemLevel">
            <summary>
            问题等级（一般、较大、重大）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.SafetyHazard">
            <summary>
            安全隐患
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.RegulationClause">
            <summary>
            法规条款
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.Description">
            <summary>
            具体描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.RectificationRequirement">
            <summary>
            整改要求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.PossibleConsequences">
            <summary>
            可能产生后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.RectificationSuggestion">
            <summary>
            整改建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.Photos">
            <summary>
            现场照片（JSON格式存储多个照片路径）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.Status">
            <summary>
            状态（0待确认，1正式记录）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.HiddenDangerDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.Id">
            <summary>
            主键ID - 重写为long类型以匹配业务需求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.CheckType">
            <summary>
            检查类型（生产安全、质量安全）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.CheckDate">
            <summary>
            检查日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.ClientUnit">
            <summary>
            委托单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.CheckMembers">
            <summary>
            检查组员
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.ProjectProgress">
            <summary>
            工程进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.CheckContent">
            <summary>
            检查内容
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectCheckDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.Id">
            <summary>
            主键ID - 重写为long类型以匹配业务需求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.ProjectNo">
            <summary>
            项目编号 (自动生成，规则P+4位年份+2位月份+4位序号)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.ProjectAddress">
            <summary>
            项目地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.Region">
            <summary>
            所属区域
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.ProjectType">
            <summary>
            项目类型（生产安全、质量安全）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.RelatedContract">
            <summary>
            关联合同
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.ManagerPhone">
            <summary>
            负责人电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.ManagerPosition">
            <summary>
            负责人职务
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.StartDate">
            <summary>
            开工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.EndDate">
            <summary>
            竣工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.EngineeringType">
            <summary>
            工程类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.EngineeringCategory">
            <summary>
            工程类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.ConstructionUnit">
            <summary>
            建设单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.SupervisionUnit">
            <summary>
            监理单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.DesignUnit">
            <summary>
            设计单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.ConstructionCompany">
            <summary>
            施工单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.ConstructionPermitNo">
            <summary>
            施工许可证号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.HazardousChemicalWorkers">
            <summary>
            危险化学品从业人数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.ProjectDO.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.StandardDO.Name">
            <summary>
            标准名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.StandardDO.Type">
            <summary>
            标准类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.StandardDO.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.StandardDO.RegulationClause">
            <summary>
            法规条款
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.StandardDO.Requirements">
            <summary>
            具体要求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.StandardDO.Status">
            <summary>
            状态（0启用，1停用）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.Project.StandardDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.BusinessLogDO">
            <summary>
            业务日志
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.UserName">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.Action">
            <summary>
            操作方法，全名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.HttpMethod">
            <summary>
            http请求方式
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.Url">
            <summary>
            请求地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.Os">
            <summary>
            系统
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.Browser">
            <summary>
            浏览器
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.NodeName">
            <summary>
            操作节点名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.Ip">
            <summary>
            IP
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.Address">
            <summary>
            登录地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.OperationMsg">
            <summary>
            操作信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.MillSeconds">
            <summary>
            耗时，单位毫秒
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.RequestId">
            <summary>
            请求跟踪ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.BusinessLogDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.ConfigDO">
            <summary>
            系统配置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.ConfigDO.Name">
            <summary>
            配置名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.ConfigDO.Key">
            <summary>
            配置键名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.ConfigDO.Value">
            <summary>
            配置键值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.ConfigDO.GroupKey">
            <summary>
            组别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.ConfigDO.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.ConfigDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.DictDataDO">
            <summary>
            字典数据表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictDataDO.Value">
            <summary>
            字典值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictDataDO.Label">
            <summary>
            显示文本
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictDataDO.DictType">
            <summary>
            字典类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictDataDO.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictDataDO.Sort">
            <summary>
            排序值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictDataDO.IsEnabled">
            <summary>
            是否开启
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictDataDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.DictTypeDO">
            <summary>
            字典类型表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictTypeDO.Name">
            <summary>
            字典名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictTypeDO.DictType">
            <summary>
            字典类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictTypeDO.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictTypeDO.IsEnabled">
            <summary>
            是否开启
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.DictTypeDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.LoginLogDO">
            <summary>
            登录日志
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.LoginLogDO.UserName">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.LoginLogDO.Ip">
            <summary>
            IP
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.LoginLogDO.Address">
            <summary>
            登录地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.LoginLogDO.Browser">
            <summary>
            浏览器
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.LoginLogDO.OperationMsg">
            <summary>
            操作信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.LoginLogDO.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.LoginLogDO.SessionId">
            <summary>
            会话ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.LoginLogDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.MenuDO">
            <summary>
            菜单表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.Title">
            <summary>
            显示标题/名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.Path">
            <summary>
            路由/地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.Component">
            <summary>
            组件地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.MenuType">
            <summary>
            功能类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.Permission">
            <summary>
            授权码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.ParentId">
            <summary>
            父级ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.Display">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.RoleMenus">
            <summary>
            角色菜单
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.MenuDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.NotificationDO.Title">
            <summary>
            通知标题
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.NotificationDO.Description">
            <summary>
            通知描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.NotificationDO.EmployeeId">
            <summary>
            通知员工
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.NotificationDO.IsReaded">
            <summary>
            是否已读(1已读0未读)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.NotificationDO.ReadedTime">
            <summary>
            已读时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.NotificationDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDeptDO.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDeptDO.DeptId">
            <summary>
            部门ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDeptDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.RoleDO">
            <summary>
            角色表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDO.RoleName">
            <summary>
            角色名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDO.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDO.PowerDataType">
            <summary>
            数据权限类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDO.UserRoles">
            <summary>
            用户角色
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDO.RoleMenus">
            <summary>
            角色菜单
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDO.RoleDepts">
            <summary>
            角色查看部门（数据权限类型=1时，指定部门时才存入）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleDO.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.RoleMenuDO">
            <summary>
            角色菜单表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleMenuDO.MenuId">
            <summary>
            菜单ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleMenuDO.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.RoleMenuDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.TenantDO.Name">
            <summary>
            租户名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.TenantDO.TenantId">
            <summary>
            租户标识
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.TenantDO.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.UserDO">
            <summary>
            用户表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserDO.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserDO.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserDO.PasswordSalt">
            <summary>
            密码盐
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserDO.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserDO.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserDO.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserDO.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserDO.UserRoles">
            <summary>
            用户角色
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Entities.System.UserRoleDO">
            <summary>
            用户角色关联表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserRoleDO.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserRoleDO.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.Entities.System.UserRoleDO.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.LoginDto.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.LoginDto.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.LoginResultDto.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.LoginResultDto.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.LoginResultDto.SessionId">
            <summary>
            当前会话ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.PersonalInfoDto.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.PersonalInfoDto.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.PersonalInfoDto.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.TokenResultDto.AccessToken">
            <summary>
            访问token
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.TokenResultDto.RefreshToken">
            <summary>
            刷新token
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.TokenResultDto.ExpiredTime">
            <summary>
            过期时间，格式YYYY-MM-DD HH:mm:ss
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserAuthInfoDto.User">
            <summary>
            用户信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserAuthInfoDto.Permissions">
            <summary>
            权限信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserAuthInfoDto.Menus">
            <summary>
            菜单信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserInfoDto.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserInfoDto.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserInfoDto.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserInfoDto.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserInfoDto.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserPwdDto.OldPwd">
            <summary>
            旧密码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Account.Dtos.UserPwdDto.NewPwd">
            <summary>
            新密码
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Account.IAccountService.LoginAsync(Fancyx.Admin.IService.Account.Dtos.LoginDto)">
            <summary>
            登录
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Account.IAccountService.GetAccessTokenAsync(System.String)">
            <summary>
            刷新token
            </summary>
            <param name="refreshToken"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Account.IAccountService.UpdateUserInfoAsync(Fancyx.Admin.IService.Account.Dtos.PersonalInfoDto)">
            <summary>
            修改个人基本信息
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Account.IAccountService.UpdateUserPwdAsync(Fancyx.Admin.IService.Account.Dtos.UserPwdDto)">
            <summary>
            修改个人密码
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Account.IAccountService.SignOutAsync">
            <summary>
            注销
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Account.IAccountService.GetUserAuthInfoAsync">
            <summary>
            获取用户权限信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Account.IAccountService.GetCurrentUserInfoAsync">
            <summary>
            获取当前用户信息（移动端专用）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Account.IAccountService.GetUserPermissionsAsync(System.Guid)">
            <summary>
            获取用户权限列表（移动端专用）
            </summary>
            <param name="userId">用户ID</param>
            <returns></returns>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionDto">
            <summary>
            移动端AI识别请求DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionDto.Images">
            <summary>
            图片列表（Base64编码或URL）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionDto.RecognitionType">
            <summary>
            识别类型（safety/quality/environment）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionDto.CheckId">
            <summary>
            检查ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionDto.Location">
            <summary>
            GPS位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognizedDangerDto">
            <summary>
            移动端识别到的隐患DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognizedDangerDto.DangerType">
            <summary>
            隐患类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognizedDangerDto.Description">
            <summary>
            隐患描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognizedDangerDto.ConfidenceScore">
            <summary>
            置信度分数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognizedDangerDto.BoundingBox">
            <summary>
            边界框信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognizedDangerDto.SuggestedAction">
            <summary>
            建议的整改措施
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginDto">
            <summary>
            移动端登录DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginDto.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginDto.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginDto.Location">
            <summary>
            位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginDto.CaptchaCode">
            <summary>
            验证码（如果需要）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginDto.CaptchaKey">
            <summary>
            验证码Key
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginResultDto">
            <summary>
            移动端登录结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginResultDto.AccessToken">
            <summary>
            访问令牌
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginResultDto.RefreshToken">
            <summary>
            刷新令牌
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginResultDto.TokenType">
            <summary>
            令牌类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginResultDto.ExpiresIn">
            <summary>
            过期时间（秒）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginResultDto.UserInfo">
            <summary>
            用户信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginResultDto.AppConfig">
            <summary>
            应用配置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLoginResultDto.ServerTime">
            <summary>
            服务器时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto">
            <summary>
            移动端用户信息DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.Id">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.Roles">
            <summary>
            角色列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.Permissions">
            <summary>
            权限列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.Department">
            <summary>
            部门
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.Position">
            <summary>
            职位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.Phone">
            <summary>
            联系电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUserInfoDto.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto">
            <summary>
            移动端应用配置DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto.MaxFileSize">
            <summary>
            最大文件大小（字节）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto.MaxBatchFiles">
            <summary>
            最大批量文件数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto.SupportedImageFormats">
            <summary>
            支持的图片格式
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto.SupportedAudioFormats">
            <summary>
            支持的音频格式
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto.AIFeatureEnabled">
            <summary>
            AI功能是否启用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto.OfflineModeEnabled">
            <summary>
            离线模式是否启用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto.AutoSyncInterval">
            <summary>
            自动同步间隔（秒）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto.LocationRequired">
            <summary>
            是否需要位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppConfigDto.VersionInfo">
            <summary>
            应用版本信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileAppVersionDto">
            <summary>
            移动端应用版本信息DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppVersionDto.CurrentVersion">
            <summary>
            当前版本
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppVersionDto.LatestVersion">
            <summary>
            最新版本
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppVersionDto.UpdateRequired">
            <summary>
            是否需要更新
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppVersionDto.ForceUpdate">
            <summary>
            是否强制更新
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppVersionDto.UpdateDescription">
            <summary>
            更新说明
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAppVersionDto.DownloadUrl">
            <summary>
            下载链接
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileRefreshTokenDto">
            <summary>
            移动端刷新Token DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRefreshTokenDto.RefreshToken">
            <summary>
            刷新令牌
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRefreshTokenDto.DeviceId">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileLocationDto">
            <summary>
            移动端位置信息DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLocationDto.Latitude">
            <summary>
            纬度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLocationDto.Longitude">
            <summary>
            经度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLocationDto.Altitude">
            <summary>
            海拔高度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLocationDto.Accuracy">
            <summary>
            精度（米）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLocationDto.Address">
            <summary>
            地址描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLocationDto.Timestamp">
            <summary>
            获取位置的时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileDeviceInfoDto">
            <summary>
            移动端设备信息DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDeviceInfoDto.DeviceType">
            <summary>
            设备类型（iOS/Android）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDeviceInfoDto.DeviceModel">
            <summary>
            设备型号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDeviceInfoDto.OSVersion">
            <summary>
            操作系统版本
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDeviceInfoDto.AppVersion">
            <summary>
            应用版本
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDeviceInfoDto.DeviceId">
            <summary>
            设备唯一标识
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDeviceInfoDto.NetworkType">
            <summary>
            网络类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDeviceInfoDto.BatteryLevel">
            <summary>
            电池电量
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto">
            <summary>
            移动端快速检查DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto.CheckMembers">
            <summary>
            检查组员
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto.ProjectProgress">
            <summary>
            工程进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto.CheckContent">
            <summary>
            检查内容
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto.Location">
            <summary>
            位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSimpleDto">
            <summary>
            移动端项目简单信息DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSimpleDto.Id">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSimpleDto.ProjectNo">
            <summary>
            项目编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSimpleDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSimpleDto.ProjectAddress">
            <summary>
            项目地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSimpleDto.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSimpleDto.ManagerPhone">
            <summary>
            负责人电话
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobilePagedQueryDto">
            <summary>
            移动端分页查询基类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePagedQueryDto.Current">
            <summary>
            当前页码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePagedQueryDto.PageSize">
            <summary>
            页大小
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePagedQueryDto.Keyword">
            <summary>
            搜索关键词
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePagedQueryDto.SortField">
            <summary>
            排序字段
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePagedQueryDto.SortOrder">
            <summary>
            排序方向（asc/desc）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileOperationResultDto">
            <summary>
            移动端操作结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileOperationResultDto.Success">
            <summary>
            操作是否成功
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileOperationResultDto.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileOperationResultDto.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileOperationResultDto.Timestamp">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto">
            <summary>
            移动端创建隐患明细DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.ProjectCheckId">
            <summary>
            关联检查任务ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.DangerCategory">
            <summary>
            隐患类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.ProblemLevel">
            <summary>
            问题级别（重大/严重/一般/轻微）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.ProblemDescription">
            <summary>
            问题描述（AI识别 + 人工补充）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.AiRecognitionResult">
            <summary>
            AI识别原始结果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.AiConfidence">
            <summary>
            AI置信度（0-1）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.LocationDescription">
            <summary>
            现场位置描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.Location">
            <summary>
            GPS坐标信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.Photos">
            <summary>
            现场照片URL列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.AudioRecords">
            <summary>
            语音记录URL列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.RectificationSuggestion">
            <summary>
            整改建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.RectificationDeadline">
            <summary>
            整改期限
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.ResponsibleDepartment">
            <summary>
            责任部门
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.ResponsiblePerson">
            <summary>
            责任人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.ResponsibleContact">
            <summary>
            责任人联系方式
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.DiscoveryTime">
            <summary>
            发现时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.DiscoveredBy">
            <summary>
            发现人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.ImpactScope">
            <summary>
            影响范围
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.PotentialConsequences">
            <summary>
            可能后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.TemporaryMeasures">
            <summary>
            临时措施
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.RelatedRegulations">
            <summary>
            相关法规标准
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.Tags">
            <summary>
            额外标签
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateHiddenDangerDto.CreationRemarks">
            <summary>
            创建备注
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto">
            <summary>
            移动端导出结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto.FileUrl">
            <summary>
            文件URL
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto.FileSize">
            <summary>
            文件大小（字节）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto.ExportTime">
            <summary>
            导出时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto.Status">
            <summary>
            导出状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto.ErrorMessage">
            <summary>
            错误消息（如果导出失败）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto.Success">
            <summary>
            导出是否成功
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto.RecordCount">
            <summary>
            导出记录数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportResultDto.Message">
            <summary>
            导出消息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto">
            <summary>
            移动端更新隐患明细DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.DangerCategory">
            <summary>
            隐患类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.ProblemLevel">
            <summary>
            问题级别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.ProblemDescription">
            <summary>
            问题描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.LocationDescription">
            <summary>
            现场位置描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.NewPhotos">
            <summary>
            新增照片信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.DeletedPhotoUrls">
            <summary>
            要删除的照片URL
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.NewAudioRecords">
            <summary>
            新增语音记录
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.DeletedAudioUrls">
            <summary>
            要删除的语音记录URL
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.RectificationSuggestion">
            <summary>
            整改建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.RectificationDeadline">
            <summary>
            整改期限
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.ResponsibleDepartment">
            <summary>
            责任部门
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.ResponsiblePerson">
            <summary>
            责任人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.ResponsibleContact">
            <summary>
            责任人联系方式
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.ImpactScope">
            <summary>
            影响范围
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.PotentialConsequences">
            <summary>
            可能后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.TemporaryMeasures">
            <summary>
            临时措施
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.RelatedRegulations">
            <summary>
            相关法规标准
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.Tags">
            <summary>
            更新标签
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.UpdateReason">
            <summary>
            更新理由
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateHiddenDangerDto.UpdateRemarks">
            <summary>
            更新备注
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmHiddenDangerDto">
            <summary>
            移动端隐患确认DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmHiddenDangerDto.ConfirmationType">
            <summary>
            确认类型（确认存在/误报/已处理）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmHiddenDangerDto.ConfirmationComment">
            <summary>
            确认意见
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmHiddenDangerDto.ConfirmationPhotos">
            <summary>
            确认时的现场照片
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmHiddenDangerDto.Location">
            <summary>
            确认时的GPS位置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmHiddenDangerDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmHiddenDangerDto.ConfirmationTime">
            <summary>
            确认时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto">
            <summary>
            移动端整改记录DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.RectificationStatus">
            <summary>
            整改状态（进行中/已完成/逾期）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.RectificationProgress">
            <summary>
            整改进度（百分比）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.RectificationDescription">
            <summary>
            整改说明
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.RectificationMeasures">
            <summary>
            整改措施
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.RectificationPhotos">
            <summary>
            整改后现场照片
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.ActualCompletionTime">
            <summary>
            实际完成时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.RectificationCost">
            <summary>
            整改费用（可选）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.AcceptancePerson">
            <summary>
            验收人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.AcceptanceTime">
            <summary>
            验收时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.AcceptanceComment">
            <summary>
            验收意见
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionRequestDto">
            <summary>
            移动端AI识别请求DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionRequestDto.RecognitionType">
            <summary>
            识别类型（photo/audio/mixed）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionRequestDto.PhotoFiles">
            <summary>
            照片文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionRequestDto.AudioFiles">
            <summary>
            语音文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionRequestDto.TextDescription">
            <summary>
            文本描述（辅助识别）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionRequestDto.Context">
            <summary>
            上下文信息（项目类型、工程类别等）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionRequestDto.Location">
            <summary>
            GPS位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionRequestDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto">
            <summary>
            移动端AI识别结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.TaskId">
            <summary>
            识别任务ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.Status">
            <summary>
            识别状态（processing/completed/failed）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.Progress">
            <summary>
            识别进度（0-100）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.Results">
            <summary>
            识别结果列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.OverallConfidence">
            <summary>
            整体置信度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.SuggestedCategory">
            <summary>
            建议的隐患类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.SuggestedLevel">
            <summary>
            建议的问题级别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.Success">
            <summary>
            识别是否成功
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.ConfidenceScore">
            <summary>
            置信度分数（兼容性属性）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.RecognizedDangers">
            <summary>
            识别到的隐患列表（兼容性属性）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.ProcessingTime">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.GeneratedDescription">
            <summary>
            生成的问题描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.SuggestedRectification">
            <summary>
            建议的整改措施
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.ProcessTime">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionResultDto.ErrorMessage">
            <summary>
            错误信息（如果识别失败）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionContextDto">
            <summary>
            移动端识别上下文DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionContextDto.ProjectType">
            <summary>
            项目类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionContextDto.EngineeringCategory">
            <summary>
            工程类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionContextDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionContextDto.WorkEnvironment">
            <summary>
            工作环境
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionContextDto.HistoricalData">
            <summary>
            历史识别数据
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionItemDto">
            <summary>
            移动端识别项目DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionItemDto.Type">
            <summary>
            识别类型（safety_violation/quality_issue/equipment_failure等）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionItemDto.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionItemDto.Confidence">
            <summary>
            置信度（0-1）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionItemDto.Location">
            <summary>
            位置信息（在图片中的坐标等）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecognitionItemDto.Tags">
            <summary>
            相关标签
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto">
            <summary>
            移动端隐患创建DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.CheckId">
            <summary>
            检查ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.SafetyHazard">
            <summary>
            安全隐患描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.Description">
            <summary>
            具体描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.RectificationRequirement">
            <summary>
            整改要求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.PossibleConsequences">
            <summary>
            可能后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.RectificationSuggestion">
            <summary>
            整改建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.Photos">
            <summary>
            照片列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.Location">
            <summary>
            位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto">
            <summary>
            移动端隐患更新DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto.SafetyHazard">
            <summary>
            安全隐患描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto.Description">
            <summary>
            具体描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto.RectificationRequirement">
            <summary>
            整改要求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto.PossibleConsequences">
            <summary>
            可能后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto.RectificationSuggestion">
            <summary>
            整改建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto.Photos">
            <summary>
            照片列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto.UpdateReason">
            <summary>
            更新原因
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto">
            <summary>
            移动端隐患状态DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto.DangerId">
            <summary>
            隐患ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto.Status">
            <summary>
            当前状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto.StatusDescription">
            <summary>
            状态描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto.CanEdit">
            <summary>
            是否可以编辑
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto.CanDelete">
            <summary>
            是否可以删除
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto.RectificationProgress">
            <summary>
            整改进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto.LastUpdateTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerStatusDto.IsProcessing">
            <summary>
            是否正在处理中
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerConfirmDto">
            <summary>
            移动端隐患确认DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerConfirmDto.ConfirmComment">
            <summary>
            确认意见
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerConfirmDto.ConfirmResult">
            <summary>
            确认结果（通过/驳回）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerConfirmDto.ConfirmBy">
            <summary>
            确认人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerConfirmDto.ConfirmTime">
            <summary>
            确认时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadDto">
            <summary>
            移动端多媒体上传DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadDto.DangerId">
            <summary>
            关联的隐患ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadDto.Files">
            <summary>
            文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadDto.UploadType">
            <summary>
            上传类型（photo/voice/video）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadDto.Description">
            <summary>
            上传说明
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileFileInfoDto">
            <summary>
            移动端文件信息DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileFileInfoDto.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileFileInfoDto.FileSize">
            <summary>
            文件大小
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileFileInfoDto.FileType">
            <summary>
            文件类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileFileInfoDto.FileUrl">
            <summary>
            文件URL
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileFileInfoDto.ThumbnailUrl">
            <summary>
            缩略图URL
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchUploadResultDto">
            <summary>
            移动端批量上传结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchUploadResultDto.TotalCount">
            <summary>
            总文件数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchUploadResultDto.SuccessCount">
            <summary>
            成功上传数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchUploadResultDto.FailedCount">
            <summary>
            失败数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchUploadResultDto.SuccessFiles">
            <summary>
            成功上传的文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchUploadResultDto.FailedFiles">
            <summary>
            失败的文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchUploadResultDto.UploadTime">
            <summary>
            上传时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto">
            <summary>
            移动端批量操作DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto.OperationType">
            <summary>
            操作类型（delete/confirm/export等）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto.TargetIds">
            <summary>
            目标ID列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto.Parameters">
            <summary>
            操作参数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto.Reason">
            <summary>
            操作原因
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto.OperatorName">
            <summary>
            操作人
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileExportRequestDto">
            <summary>
            移动端导出请求DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportRequestDto.ExportType">
            <summary>
            导出类型（excel/pdf/word）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportRequestDto.ExportFormat">
            <summary>
            导出格式
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportRequestDto.QueryCondition">
            <summary>
            查询条件
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportRequestDto.ExportFields">
            <summary>
            导出字段
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportRequestDto.IncludeImages">
            <summary>
            是否包含图片
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileExportRequestDto.TemplateId">
            <summary>
            模板ID
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto">
            <summary>
            移动端统计查询DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto.StatisticsType">
            <summary>
            统计类型（daily/weekly/monthly）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto.DangerCategory">
            <summary>
            隐患类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto.GroupByFields">
            <summary>
            分组字段
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoInfoDto">
            <summary>
            移动端照片信息DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoInfoDto.Url">
            <summary>
            照片URL
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoInfoDto.ThumbnailUrl">
            <summary>
            缩略图URL
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoInfoDto.Description">
            <summary>
            照片描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoInfoDto.CaptureTime">
            <summary>
            拍摄时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoInfoDto.CaptureLocation">
            <summary>
            拍摄位置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoInfoDto.FileSize">
            <summary>
            文件大小（字节）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoInfoDto.Dimensions">
            <summary>
            图片尺寸
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoInfoDto.AiAnalysis">
            <summary>
            AI识别结果
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileAudioInfoDto">
            <summary>
            移动端语音信息DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAudioInfoDto.Url">
            <summary>
            语音文件URL
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAudioInfoDto.Description">
            <summary>
            语音描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAudioInfoDto.RecordTime">
            <summary>
            录制时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAudioInfoDto.RecordLocation">
            <summary>
            录制位置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAudioInfoDto.FileSize">
            <summary>
            文件大小（字节）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAudioInfoDto.Duration">
            <summary>
            时长（秒）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAudioInfoDto.TranscriptionText">
            <summary>
            语音转文本结果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAudioInfoDto.TranscriptionConfidence">
            <summary>
            转换置信度
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchHiddenDangerOperationDto">
            <summary>
            移动端批量隐患操作DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchHiddenDangerOperationDto.DangerIds">
            <summary>
            隐患ID列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchHiddenDangerOperationDto.OperationType">
            <summary>
            操作类型（delete/export/confirm/rectify/close）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchHiddenDangerOperationDto.OperationParams">
            <summary>
            操作参数（JSON格式）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchHiddenDangerOperationDto.Reason">
            <summary>
            操作原因
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchHiddenDangerOperationDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto">
            <summary>
            移动端批量多媒体上传DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto.HiddenDangerId">
            <summary>
            关联隐患ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto.PhotoFiles">
            <summary>
            照片文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto.AudioFiles">
            <summary>
            语音文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto.EnableAiRecognition">
            <summary>
            是否启用AI识别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto.CompressFiles">
            <summary>
            是否压缩文件
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto.Descriptions">
            <summary>
            媒体文件描述列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto.Location">
            <summary>
            上传位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchMultimediaUploadDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto">
            <summary>
            移动端多媒体上传结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto.SuccessPhotos">
            <summary>
            成功上传的照片列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto.SuccessAudios">
            <summary>
            成功上传的语音列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto.FailedFiles">
            <summary>
            失败的文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto.SuccessCount">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto.FailedCount">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto.BatchId">
            <summary>
            上传批次ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto.AiTaskId">
            <summary>
            AI识别任务ID（如果启用了AI识别）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadResultDto.UploadTime">
            <summary>
            上传时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto">
            <summary>
            移动端隐患查询DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto.CheckId">
            <summary>
            检查任务ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto.Status">
            <summary>
            状态（待确认/正式记录）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto.StartDate">
            <summary>
            创建日期范围开始
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto.EndDate">
            <summary>
            创建日期范围结束
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto">
            <summary>
            移动端隐患列表DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.Id">
            <summary>
            隐患ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.CheckInfo">
            <summary>
            检查信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.SafetyHazard">
            <summary>
            安全隐患简述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.ShortDescription">
            <summary>
            具体描述（截取前50字）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.StatusText">
            <summary>
            状态描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.ThumbnailUrl">
            <summary>
            第一张照片缩略图
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.PhotoCount">
            <summary>
            照片数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.CreatorName">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerListDto.Location">
            <summary>
            位置信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionRequestDto">
            <summary>
            移动端AI识别请求DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionRequestDto.CheckId">
            <summary>
            检查任务ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionRequestDto.Photos">
            <summary>
            现场照片（支持多张）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionRequestDto.VoiceDescription">
            <summary>
            语音描述文件
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionRequestDto.TextDescription">
            <summary>
            文字描述（语音转文字结果或直接输入）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionRequestDto.Location">
            <summary>
            GPS位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionRequestDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionRequestDto.PhotoTimestamp">
            <summary>
            拍照时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionRequestDto.EnvironmentInfo">
            <summary>
            环境信息（可选）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto">
            <summary>
            移动端AI识别结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto.TaskId">
            <summary>
            识别任务ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto.Status">
            <summary>
            识别状态（processing/completed/failed）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto.Progress">
            <summary>
            识别进度（0-100）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto.RecognizedDangers">
            <summary>
            识别到的隐患列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto.TranscribedText">
            <summary>
            语音转文字结果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto.AISuggestion">
            <summary>
            AI分析建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto.ConfidenceScore">
            <summary>
            置信度评分（0-1）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto.ProcessingTime">
            <summary>
            处理耗时（毫秒）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognitionResultDto.ErrorMessage">
            <summary>
            错误信息（如果识别失败）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto">
            <summary>
            移动端AI识别的隐患DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.TempId">
            <summary>
            临时ID（用于前端标识）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.SuggestedLevel">
            <summary>
            推荐的问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.SafetyHazard">
            <summary>
            安全隐患描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.Description">
            <summary>
            具体描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.SuggestedRectification">
            <summary>
            建议的整改要求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.PossibleConsequences">
            <summary>
            可能的后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.StandardReference">
            <summary>
            相关标准规范
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.Confidence">
            <summary>
            识别置信度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.RelatedPhotoIndexes">
            <summary>
            关联的照片索引
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileAIRecognizedDangerDto.BoundingBoxes">
            <summary>
            照片中的标注区域
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmAIDangerDto">
            <summary>
            移动端确认AI识别隐患DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmAIDangerDto.TaskId">
            <summary>
            AI识别任务ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmAIDangerDto.ConfirmedDangers">
            <summary>
            确认的隐患列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmAIDangerDto.CheckId">
            <summary>
            检查任务ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmAIDangerDto.UserModifiedDescription">
            <summary>
            用户修改的文字描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmAIDangerDto.Location">
            <summary>
            位置信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto">
            <summary>
            移动端确认的隐患DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.TempId">
            <summary>
            AI识别的临时ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.DangerCategory">
            <summary>
            用户确认/修改的隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.ProblemLevel">
            <summary>
            用户确认/修改的问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.SafetyHazard">
            <summary>
            用户确认/修改的安全隐患
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.Description">
            <summary>
            用户确认/修改的具体描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.RectificationRequirement">
            <summary>
            用户确认/修改的整改要求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.PossibleConsequences">
            <summary>
            可能产生后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.RectificationSuggestion">
            <summary>
            整改建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.StandardId">
            <summary>
            标准规范ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.StandardName">
            <summary>
            标准规范名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.RegulationClause">
            <summary>
            法规条款
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileConfirmedDangerDto.PhotoUrls">
            <summary>
            关联的照片URL列表
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileEnvironmentInfoDto">
            <summary>
            移动端环境信息DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileEnvironmentInfoDto.LightCondition">
            <summary>
            光线条件（良好/一般/较暗）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileEnvironmentInfoDto.Weather">
            <summary>
            天气情况
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileEnvironmentInfoDto.Temperature">
            <summary>
            温度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileEnvironmentInfoDto.Humidity">
            <summary>
            湿度
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBoundingBoxDto">
            <summary>
            移动端标注框DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBoundingBoxDto.X">
            <summary>
            左上角X坐标（相对于图片宽度的比例，0-1）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBoundingBoxDto.Y">
            <summary>
            左上角Y坐标（相对于图片高度的比例，0-1）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBoundingBoxDto.Width">
            <summary>
            宽度（相对于图片宽度的比例，0-1）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBoundingBoxDto.Height">
            <summary>
            高度（相对于图片高度的比例，0-1）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBoundingBoxDto.Label">
            <summary>
            标注说明
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBoundingBoxDto.Confidence">
            <summary>
            置信度
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileStandardReferenceDto">
            <summary>
            移动端标准规范引用DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStandardReferenceDto.Id">
            <summary>
            标准ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStandardReferenceDto.Name">
            <summary>
            标准名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStandardReferenceDto.Code">
            <summary>
            标准编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStandardReferenceDto.Clause">
            <summary>
            相关条款
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStandardReferenceDto.MatchConfidence">
            <summary>
            匹配置信度
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerQueryDto">
            <summary>
            移动端隐患查询DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerQueryDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerQueryDto.Status">
            <summary>
            隐患状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerQueryDto.DangerLevel">
            <summary>
            隐患等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerQueryDto.StartDate">
            <summary>
            开始日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerQueryDto.EndDate">
            <summary>
            结束日期
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerListDto">
            <summary>
            移动端隐患列表DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerListDto.Id">
            <summary>
            隐患ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerListDto.DangerNo">
            <summary>
            隐患编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerListDto.Description">
            <summary>
            隐患描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerListDto.DangerLevel">
            <summary>
            隐患等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerListDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerListDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerListDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerDetailDto">
            <summary>
            移动端隐患详情DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerDetailDto.Id">
            <summary>
            隐患ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerDetailDto.DangerNo">
            <summary>
            隐患编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerDetailDto.Description">
            <summary>
            隐患描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerDetailDto.DangerLevel">
            <summary>
            隐患等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerDetailDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerDetailDto.Location">
            <summary>
            位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerDetailDto.Files">
            <summary>
            相关文件
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerDetailDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto">
            <summary>
            移动端隐患统计DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.TotalCount">
            <summary>
            总数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.PendingCount">
            <summary>
            待确认数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.ConfirmedCount">
            <summary>
            已确认数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.ResolvedCount">
            <summary>
            已整改数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.RectifiedCount">
            <summary>
            已整改数量（兼容性属性）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.OverdueCount">
            <summary>
            逾期数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.StatisticsTime">
            <summary>
            统计时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.CategoryStats">
            <summary>
            分类统计
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.LevelStats">
            <summary>
            等级统计
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerStatisticsDto.TrendData">
            <summary>
            趋势数据
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCategoryStatDto">
            <summary>
            移动端分类统计DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCategoryStatDto.CategoryName">
            <summary>
            分类名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCategoryStatDto.Count">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCategoryStatDto.Percentage">
            <summary>
            百分比
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileLevelStatDto">
            <summary>
            移动端等级统计DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLevelStatDto.LevelName">
            <summary>
            等级名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLevelStatDto.Count">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileLevelStatDto.Percentage">
            <summary>
            百分比
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileTrendDataDto">
            <summary>
            移动端趋势数据DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileTrendDataDto.Date">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileTrendDataDto.Count">
            <summary>
            数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileTrendDataDto.Type">
            <summary>
            类型
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto">
            <summary>
            移动端创建检查任务DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.ProjectId">
            <summary>
            关联项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.CheckType">
            <summary>
            检查类型（生产安全/质量安全）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.CheckDate">
            <summary>
            检查日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.ClientUnit">
            <summary>
            委托单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.CheckMembers">
            <summary>
            检查组员（多人用逗号分隔）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.ProjectProgress">
            <summary>
            工程进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.CheckContent">
            <summary>
            检查内容
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.EstimatedHours">
            <summary>
            预计检查时长（小时）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.Priority">
            <summary>
            检查优先级（高/中/低）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.Location">
            <summary>
            GPS位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.CheckPoints">
            <summary>
            检查要点列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto">
            <summary>
            移动端更新检查任务DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.CheckDate">
            <summary>
            检查日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.ClientUnit">
            <summary>
            委托单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.CheckMembers">
            <summary>
            检查组员
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.ProjectProgress">
            <summary>
            工程进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.CheckContent">
            <summary>
            检查内容
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.EstimatedHours">
            <summary>
            预计检查时长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.Priority">
            <summary>
            检查优先级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.CheckPoints">
            <summary>
            检查要点列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.UpdateReason">
            <summary>
            更新理由
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileStartCheckDto">
            <summary>
            移动端开始检查DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStartCheckDto.Location">
            <summary>
            开始位置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStartCheckDto.StartPhotos">
            <summary>
            现场照片
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStartCheckDto.StartRemarks">
            <summary>
            开始备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileStartCheckDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobilePauseCheckDto">
            <summary>
            移动端暂停检查DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePauseCheckDto.PauseReason">
            <summary>
            暂停原因
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePauseCheckDto.Location">
            <summary>
            暂停位置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePauseCheckDto.CompletedContent">
            <summary>
            已完成的检查内容
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePauseCheckDto.Progress">
            <summary>
            暂停时进度（百分比）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePauseCheckDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileResumeCheckDto">
            <summary>
            移动端恢复检查DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileResumeCheckDto.Location">
            <summary>
            恢复位置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileResumeCheckDto.ResumeRemarks">
            <summary>
            恢复备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileResumeCheckDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto">
            <summary>
            移动端检查进度DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto.Progress">
            <summary>
            当前进度（百分比）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto.CompletedItems">
            <summary>
            已完成的检查项目
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto.CurrentItem">
            <summary>
            当前正在检查的项目
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto.DangerCount">
            <summary>
            已发现隐患数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto.Location">
            <summary>
            当前位置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto.ProgressRemarks">
            <summary>
            进度备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto.SaveTime">
            <summary>
            保存时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchCheckOperationDto">
            <summary>
            移动端批量检查操作DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchCheckOperationDto.CheckIds">
            <summary>
            检查任务ID列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchCheckOperationDto.OperationType">
            <summary>
            操作类型（delete/export/complete/assign）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchCheckOperationDto.OperationParams">
            <summary>
            操作参数（JSON格式）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchCheckOperationDto.Reason">
            <summary>
            操作原因
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchCheckOperationDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoUploadResultDto">
            <summary>
            移动端照片上传结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoUploadResultDto.SuccessPhotos">
            <summary>
            成功上传的照片列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoUploadResultDto.FailedPhotos">
            <summary>
            失败的照片列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoUploadResultDto.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoUploadResultDto.SuccessCount">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoUploadResultDto.FailedCount">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoUploadResultDto.BatchId">
            <summary>
            上传批次ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobilePhotoUploadResultDto.UploadTime">
            <summary>
            上传时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchPhotoUploadDto">
            <summary>
            移动端批量照片上传DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchPhotoUploadDto.RelatedRecordId">
            <summary>
            关联记录ID（隐患或检查）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchPhotoUploadDto.RecordType">
            <summary>
            记录类型（danger/check）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchPhotoUploadDto.Photos">
            <summary>
            照片文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchPhotoUploadDto.CompressImages">
            <summary>
            是否压缩图片
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchPhotoUploadDto.PhotoDescriptions">
            <summary>
            照片描述列表（与照片对应）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchPhotoUploadDto.Location">
            <summary>
            GPS位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchPhotoUploadDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileUploadErrorDto">
            <summary>
            移动端上传错误DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUploadErrorDto.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUploadErrorDto.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUploadErrorDto.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUploadErrorDto.FileSize">
            <summary>
            文件大小
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto">
            <summary>
            移动端检查查询DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto.Status">
            <summary>
            检查状态（待开始/进行中/已完成）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto.CheckLeaderId">
            <summary>
            检查组长ID（我负责的检查）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto.StartDate">
            <summary>
            检查日期范围开始
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto.EndDate">
            <summary>
            检查日期范围结束
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto.OnlyMyTasks">
            <summary>
            是否只显示我的任务
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto">
            <summary>
            移动端检查任务DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.Id">
            <summary>
            检查任务ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.Project">
            <summary>
            项目信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.CheckDate">
            <summary>
            检查日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.ClientUnit">
            <summary>
            委托单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.CheckMembers">
            <summary>
            检查组员
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.ProjectProgress">
            <summary>
            工程进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.Status">
            <summary>
            检查状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.StatusText">
            <summary>
            状态显示文字
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.Priority">
            <summary>
            优先级（高/中/低）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.DangerCount">
            <summary>
            已发现隐患数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.EstimatedHours">
            <summary>
            预计耗时（小时）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.ActualStartTime">
            <summary>
            实际开始时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.ActualEndTime">
            <summary>
            实际结束时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckTaskDto.LastModificationTime">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckDetailDto">
            <summary>
            移动端检查详情DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckDetailDto.BasicInfo">
            <summary>
            基本信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckDetailDto.CheckContent">
            <summary>
            检查内容
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckDetailDto.CheckPoints">
            <summary>
            检查要点
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckDetailDto.RelevantTemplates">
            <summary>
            相关的隐患模板
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckDetailDto.DiscoveredDangers">
            <summary>
            已发现的隐患列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckDetailDto.Progress">
            <summary>
            检查进度（百分比）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckDetailDto.CheckLocation">
            <summary>
            检查位置
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto">
            <summary>
            移动端检查完成DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto.Summary">
            <summary>
            检查总结
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto.OverallRating">
            <summary>
            整体评价（优秀/良好/一般/较差）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto.Recommendations">
            <summary>
            检查建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto.NextCheckDate">
            <summary>
            下次检查建议时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto.DurationMinutes">
            <summary>
            检查用时（分钟）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto.OverallPhotos">
            <summary>
            检查照片（现场整体照片）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto.CompletionLocation">
            <summary>
            完成位置
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto.SignatureImage">
            <summary>
            签名图片（检查组长签名）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerTemplateDto">
            <summary>
            移动端隐患模板DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerTemplateDto.Id">
            <summary>
            模板ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerTemplateDto.Category">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerTemplateDto.Name">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerTemplateDto.Description">
            <summary>
            模板描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerTemplateDto.RecommendedLevel">
            <summary>
            推荐等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerTemplateDto.RectificationTemplate">
            <summary>
            整改要求模板
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerTemplateDto.RelatedStandards">
            <summary>
            相关标准规范
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDangerTemplateDto.UsageCount">
            <summary>
            使用频次
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncErrorDto">
            <summary>
            移动端同步错误DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncErrorDto.ErrorType">
            <summary>
            错误类型（check/danger/file）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncErrorDto.ClientRecordId">
            <summary>
            客户端记录ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncErrorDto.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncErrorDto.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncErrorDto.CanRetry">
            <summary>
            是否可重试
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateDataDto">
            <summary>
            移动端更新数据DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateDataDto.NewCheckTasks">
            <summary>
            新的检查任务
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateDataDto.UpdatedTemplates">
            <summary>
            更新的隐患模板
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateDataDto.ConfigUpdates">
            <summary>
            系统配置更新
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateDataDto.LastSyncTime">
            <summary>
            最后同步时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto">
            <summary>
            移动端创建项目DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.ProjectAddress">
            <summary>
            项目地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.Region">
            <summary>
            所属区域（上海16区）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.ProjectType">
            <summary>
            项目类型（生产安全/质量安全）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.EngineeringType">
            <summary>
            工程类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.EngineeringCategory">
            <summary>
            工程类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.ManagerPhone">
            <summary>
            负责人电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.ManagerPosition">
            <summary>
            负责人职位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.ConstructionUnit">
            <summary>
            建设单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.ConstructionCompany">
            <summary>
            施工单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.SupervisionUnit">
            <summary>
            监理单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.DesignUnit">
            <summary>
            设计单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.ConstructionPermitNo">
            <summary>
            施工许可证号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.RelatedContract">
            <summary>
            关联合同
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.StartDate">
            <summary>
            开工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.EndDate">
            <summary>
            竣工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.HazardousChemicalWorkers">
            <summary>
            危化品从业人数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.Location">
            <summary>
            GPS位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.SitePhotos">
            <summary>
            项目现场照片
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto.DeviceInfo">
            <summary>
            创建时的设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto">
            <summary>
            移动端更新项目DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.ProjectAddress">
            <summary>
            项目地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.Region">
            <summary>
            所属区域
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.ProjectType">
            <summary>
            项目类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.EngineeringType">
            <summary>
            工程类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.EngineeringCategory">
            <summary>
            工程类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.ManagerPhone">
            <summary>
            负责人电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.ManagerPosition">
            <summary>
            负责人职位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.ConstructionUnit">
            <summary>
            建设单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.ConstructionCompany">
            <summary>
            施工单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.SupervisionUnit">
            <summary>
            监理单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.DesignUnit">
            <summary>
            设计单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.ConstructionPermitNo">
            <summary>
            施工许可证号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.RelatedContract">
            <summary>
            关联合同
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.StartDate">
            <summary>
            开工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.EndDate">
            <summary>
            竣工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.HazardousChemicalWorkers">
            <summary>
            危化品从业人数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.Location">
            <summary>
            更新时的GPS位置信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.NewSitePhotos">
            <summary>
            新增的项目现场照片
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.DeletedSitePhotos">
            <summary>
            要删除的现场照片URL
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.UpdateReason">
            <summary>
            更新理由
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto.DeviceInfo">
            <summary>
            更新时的设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchProjectOperationDto">
            <summary>
            移动端批量项目操作DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchProjectOperationDto.ProjectIds">
            <summary>
            项目ID列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchProjectOperationDto.OperationType">
            <summary>
            操作类型（delete/export/sync/updateStatus）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchProjectOperationDto.OperationParams">
            <summary>
            操作参数（JSON格式）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchProjectOperationDto.Reason">
            <summary>
            操作原因
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchProjectOperationDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationResultDto">
            <summary>
            移动端批量操作结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationResultDto.TotalCount">
            <summary>
            总数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationResultDto.SuccessCount">
            <summary>
            成功数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationResultDto.FailedCount">
            <summary>
            失败数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationResultDto.SuccessIds">
            <summary>
            成功的项目ID列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationResultDto.Errors">
            <summary>
            失败的项目信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationResultDto.Message">
            <summary>
            操作结果消息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationResultDto.OperationTime">
            <summary>
            操作时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationResultDto.ExportFileUrl">
            <summary>
            导出文件URL（如果是导出操作）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchErrorDto">
            <summary>
            移动端批量操作错误DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchErrorDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchErrorDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchErrorDto.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileBatchErrorDto.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncRequestDto">
            <summary>
            移动端项目同步请求DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncRequestDto.LastSyncTime">
            <summary>
            上次同步时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncRequestDto.IncrementalSync">
            <summary>
            是否增量同步
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncRequestDto.ProjectIds">
            <summary>
            同步的项目ID列表（为空则同步全部）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncRequestDto.ClientVersion">
            <summary>
            客户端版本
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncRequestDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncResultDto">
            <summary>
            移动端项目同步结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncResultDto.Status">
            <summary>
            同步状态（success/partial/failed）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncResultDto.ServerTimestamp">
            <summary>
            服务器时间戳
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncResultDto.UpdatedProjects">
            <summary>
            新增或更新的项目列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncResultDto.DeletedProjectIds">
            <summary>
            已删除的项目ID列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncResultDto.SyncStats">
            <summary>
            同步数量统计
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncResultDto.NextSyncTime">
            <summary>
            下次同步时间建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncResultDto.Errors">
            <summary>
            同步错误信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncStatsDto">
            <summary>
            移动端同步统计DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncStatsDto.AddedCount">
            <summary>
            新增数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncStatsDto.UpdatedCount">
            <summary>
            更新数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncStatsDto.DeletedCount">
            <summary>
            删除数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncStatsDto.SyncDuration">
            <summary>
            同步耗时（毫秒）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectQueryDto">
            <summary>
            移动端项目查询DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectQueryDto.ProjectName">
            <summary>
            项目名称（关键字搜索）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectQueryDto.Region">
            <summary>
            所属区域
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectQueryDto.Status">
            <summary>
            项目状态（进行中/已完成/暂停）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectQueryDto.ManagerId">
            <summary>
            负责人ID（当前用户相关项目）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto">
            <summary>
            移动端项目列表DTO（精简版）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.Id">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.ProjectNo">
            <summary>
            项目编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.Region">
            <summary>
            所属区域
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.ProjectType">
            <summary>
            项目类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.ManagerPhone">
            <summary>
            负责人电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.Status">
            <summary>
            项目状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.Progress">
            <summary>
            项目进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.DangerStats">
            <summary>
            隐患统计
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectListDto.LastCheckTime">
            <summary>
            最后检查时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto">
            <summary>
            移动端项目详情DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.BasicInfo">
            <summary>
            项目基本信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.ProjectAddress">
            <summary>
            项目地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.EngineeringType">
            <summary>
            工程类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.EngineeringCategory">
            <summary>
            工程类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.ConstructionUnit">
            <summary>
            建设单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.ConstructionCompany">
            <summary>
            施工单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.SupervisionUnit">
            <summary>
            监理单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.StartDate">
            <summary>
            开工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.EndDate">
            <summary>
            竣工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.RecentChecks">
            <summary>
            最近检查记录
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDetailDto.Statistics">
            <summary>
            项目统计信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDangerStats">
            <summary>
            移动端项目隐患统计
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDangerStats.TotalCount">
            <summary>
            总隐患数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDangerStats.PendingCount">
            <summary>
            待整改数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDangerStats.MajorCount">
            <summary>
            重大隐患数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDangerStats.ModerateCount">
            <summary>
            较大隐患数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectDangerStats.MinorCount">
            <summary>
            一般隐患数量
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto">
            <summary>
            移动端项目统计DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto.TotalProjects">
            <summary>
            总项目数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto.ActiveProjects">
            <summary>
            活跃项目数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto.CompletedProjects">
            <summary>
            已完成项目数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto.PendingProjects">
            <summary>
            待处理项目数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto.StatisticsTime">
            <summary>
            统计时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto.CheckStats">
            <summary>
            检查次数统计
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto.DangerStats">
            <summary>
            隐患统计
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto.CompletionRate">
            <summary>
            整改完成率
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileProjectStatisticsDto.CheckTrend">
            <summary>
            本月检查趋势
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCountStats">
            <summary>
            移动端检查统计
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCountStats.TotalChecks">
            <summary>
            总检查次数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCountStats.MonthlyChecks">
            <summary>
            本月检查次数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCountStats.WeeklyChecks">
            <summary>
            本周检查次数
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileRecentCheckDto">
            <summary>
            移动端最近检查DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecentCheckDto.Id">
            <summary>
            检查ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecentCheckDto.CheckDate">
            <summary>
            检查日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecentCheckDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecentCheckDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecentCheckDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecentCheckDto.DangerCount">
            <summary>
            发现隐患数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileRecentCheckDto.Status">
            <summary>
            检查状态
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileDailyCheckTrendDto">
            <summary>
            移动端日检查趋势DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDailyCheckTrendDto.Date">
            <summary>
            日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDailyCheckTrendDto.CheckCount">
            <summary>
            检查次数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileDailyCheckTrendDto.DangerCount">
            <summary>
            发现隐患数量
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto">
            <summary>
            移动端同步数据DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto.ClientTimestamp">
            <summary>
            客户端时间戳
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto.CheckTasks">
            <summary>
            同步的检查任务
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto.DangerRecords">
            <summary>
            同步的隐患记录
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto.FileUploads">
            <summary>
            需要上传的文件列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto.DeviceInfo">
            <summary>
            设备信息
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncCheckDto">
            <summary>
            移动端同步检查DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncCheckDto.ClientId">
            <summary>
            客户端临时ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncCheckDto.ServerId">
            <summary>
            服务端检查ID（如果已存在）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncCheckDto.CheckData">
            <summary>
            检查数据
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncCheckDto.LastModifiedTime">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncCheckDto.SyncStatus">
            <summary>
            同步状态（pending/synced/conflict）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDangerDto">
            <summary>
            移动端同步隐患DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDangerDto.ClientId">
            <summary>
            客户端临时ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDangerDto.ServerId">
            <summary>
            服务端隐患ID（如果已存在）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDangerDto.DangerData">
            <summary>
            隐患数据
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDangerDto.LastModifiedTime">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDangerDto.SyncStatus">
            <summary>
            同步状态
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto">
            <summary>
            移动端同步文件DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto.ClientFileId">
            <summary>
            客户端文件ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto.FilePath">
            <summary>
            文件路径（本地路径）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto.FileSize">
            <summary>
            文件大小
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto.FileType">
            <summary>
            文件类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto.FileMD5">
            <summary>
            文件MD5
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto.RelatedRecordId">
            <summary>
            关联的记录ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto.RelatedRecordType">
            <summary>
            关联的记录类型（check/danger）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncFileDto.UploadStatus">
            <summary>
            上传状态
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncResultDto">
            <summary>
            移动端同步结果DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncResultDto.Success">
            <summary>
            同步是否成功
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncResultDto.SyncedCount">
            <summary>
            同步的记录数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncResultDto.FailedCount">
            <summary>
            失败的记录数量
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncResultDto.ServerTimestamp">
            <summary>
            服务器时间戳
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncResultDto.Message">
            <summary>
            同步消息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncResultDto.Errors">
            <summary>
            错误详情
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncResultDto.Conflicts">
            <summary>
            冲突的记录
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncConflictDto">
            <summary>
            移动端同步冲突DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncConflictDto.ClientRecordId">
            <summary>
            客户端记录ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncConflictDto.ServerRecordId">
            <summary>
            服务端记录ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncConflictDto.ConflictType">
            <summary>
            冲突类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncConflictDto.ConflictDescription">
            <summary>
            冲突描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncConflictDto.ClientData">
            <summary>
            客户端数据
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Mobile.Dtos.MobileSyncConflictDto.ServerData">
            <summary>
            服务端数据
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Mobile.IAiRecognitionService">
            <summary>
            AI识别服务接口
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Mobile.IAiRecognitionService.ProcessMobileRecognitionAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionDto)">
            <summary>
            处理移动端AI识别请求
            </summary>
            <param name="recognitionDto">识别请求</param>
            <returns>识别结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Mobile.IAiRecognitionService.GetRecognitionTaskStatusAsync(System.String)">
            <summary>
            获取AI识别任务状态
            </summary>
            <param name="taskId">任务ID</param>
            <returns>任务状态</returns>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.Path">
            <summary>
            请求路径
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.Method">
            <summary>
            HTTP方法 (GET, POST, PUT等)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.Ip">
            <summary>
            IP
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.RequestTime">
            <summary>
            请求时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.ResponseTime">
            <summary>
            响应时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.Duration">
            <summary>
            耗时(毫秒)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.UserId">
            <summary>
            用户ID (可为空，未登录用户)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.RequestBody">
            <summary>
            请求体
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.ResponseBody">
            <summary>
            响应体
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.Browser">
            <summary>
            浏览器
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.QueryString">
            <summary>
            请求参数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.TraceId">
            <summary>
            跟踪ID (用于关联一次请求的所有日志)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.OperateType">
            <summary>
            操作类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogListDto.OperateName">
            <summary>
            操作名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.ExceptionType">
            <summary>
            异常类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.Message">
            <summary>
            异常消息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.StackTrace">
            <summary>
            异常堆栈
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.InnerException">
            <summary>
            内部异常信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.RequestPath">
            <summary>
            请求路径 (如果是Web请求)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.RequestMethod">
            <summary>
            请求方法 (GET, POST等)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.Ip">
            <summary>
            IP
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.Browser">
            <summary>
            浏览器
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.TraceId">
            <summary>
            跟踪ID (用于关联一次请求的所有日志)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.IsHandled">
            <summary>
            是否已处理
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.HandledTime">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.HandledBy">
            <summary>
            处理人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogListDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.OnlineUserResultDto.UserName">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.OnlineUserResultDto.Ip">
            <summary>
            IP
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.OnlineUserResultDto.Address">
            <summary>
            登录地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.OnlineUserResultDto.Browser">
            <summary>
            浏览器
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Monitor.Dtos.OnlineUserResultDto.SessionId">
            <summary>
            会话ID
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Monitor.IMonitorLogService.GetApiAccessLogListAsync(Fancyx.Admin.IService.Monitor.Dtos.ApiAccessLogQueryDto)">
            <summary>
            API访问日志列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Monitor.IMonitorLogService.GetExceptionLogListAsync(Fancyx.Admin.IService.Monitor.Dtos.ExceptionLogQueryDto)">
            <summary>
            异常日志列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Monitor.IMonitorLogService.HandleExceptionAsync(System.Guid)">
            <summary>
            标记异常已处理
            </summary>
            <param name="exceptionId">异常日志ID</param>
            <returns></returns>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.BindUserDto.EmployeeId">
            <summary>
            员工ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.BindUserDto.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.Id">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.Name">
            <summary>
            部门名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.Code">
            <summary>
            部门编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.Status">
            <summary>
            状态：1正常2停用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.CuratorId">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.Phone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptDto.ParentId">
            <summary>
            父ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.Code">
            <summary>
            部门编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.Name">
            <summary>
            部门名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.Status">
            <summary>
            状态：1正常2停用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.CuratorId">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.CuratorName">
            <summary>
            负责人姓名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.Phone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.ParentId">
            <summary>
            父ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.ParentIds">
            <summary>
            层级父ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.Layer">
            <summary>
            层级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptListDto.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptQueryDto.Id">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptQueryDto.Code">
            <summary>
            部门编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptQueryDto.Name">
            <summary>
            部门名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.DeptQueryDto.Status">
            <summary>
            状态：1正常2停用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.Code">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.Phone">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.IdNo">
            <summary>
            身份证
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.FrontIdNoUrl">
            <summary>
            身份证正面
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.BackIdNoUrl">
            <summary>
            身份证背面
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.Birthday">
            <summary>
            生日
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.Address">
            <summary>
            现住址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.InTime">
            <summary>
            入职时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.OutTime">
            <summary>
            离职时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.Status">
            <summary>
            是否离职
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.UserId">
            <summary>
            关联用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.DeptId">
            <summary>
            部门ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.PositionId">
            <summary>
            职位ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.IsAddUser">
            <summary>
            是否同时添加用户
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeDto.UserPassword">
            <summary>
            用户密码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.Code">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.Phone">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.IdNo">
            <summary>
            身份证
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.FrontIdNoUrl">
            <summary>
            身份证正面
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.BackIdNoUrl">
            <summary>
            身份证背面
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.Birthday">
            <summary>
            生日
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.Address">
            <summary>
            现住址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.InTime">
            <summary>
            入职时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.OutTime">
            <summary>
            离职时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.Status">
            <summary>
            是否离职
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.UserId">
            <summary>
            关联用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.DeptId">
            <summary>
            部门ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.PositionId">
            <summary>
            职位ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.DeptName">
            <summary>
            部门名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeListDto.PositionName">
            <summary>
            职位名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeQueryDto.Keyword">
            <summary>
            姓名/手机号/工号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.EmployeeQueryDto.DeptId">
            <summary>
            部门ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionDto.Name">
            <summary>
            职位名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionDto.Code">
            <summary>
            职位编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionDto.Level">
            <summary>
            职级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionDto.Status">
            <summary>
            状态：1正常2停用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionDto.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionDto.GroupId">
            <summary>
            职位分组
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupDto.GroupName">
            <summary>
            分组名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupDto.Sort">
            <summary>
            排序值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupListDto.Id">
            <summary>
            数据ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupListDto.GroupName">
            <summary>
            分组名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupListDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupListDto.ParentId">
            <summary>
            父ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupListDto.ParentIds">
            <summary>
            层级父ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupListDto.Sort">
            <summary>
            排序值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionGroupQueryDto.GroupName">
            <summary>
            分组名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionListDto.Code">
            <summary>
            职位编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionListDto.Name">
            <summary>
            职位名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionListDto.Level">
            <summary>
            职级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionListDto.Status">
            <summary>
            状态：1正常2停用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionListDto.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionListDto.GroupId">
            <summary>
            职位分组
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionListDto.LayerName">
            <summary>
            层级分组名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionQueryDto.Keyword">
            <summary>
            职位名称/编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionQueryDto.Level">
            <summary>
            职级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionQueryDto.Status">
            <summary>
            状态：1正常2停用
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Organization.Dtos.PositionQueryDto.GroupId">
            <summary>
            职位分组
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IDeptService.AddDeptAsync(Fancyx.Admin.IService.Organization.Dtos.DeptDto)">
            <summary>
            新增部门
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IDeptService.GetDeptListAsync(Fancyx.Admin.IService.Organization.Dtos.DeptQueryDto)">
            <summary>
            部门树形列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IDeptService.UpdateDeptAsync(Fancyx.Admin.IService.Organization.Dtos.DeptDto)">
            <summary>
            修改部门
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IDeptService.DeleteDeptAsync(System.Guid)">
            <summary>
            删除部门
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IEmployeeService.AddEmployeeAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeDto)">
            <summary>
            新增员工
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IEmployeeService.GetEmployeePagedListAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeQueryDto)">
            <summary>
            员工列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IEmployeeService.UpdateEmployeeAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeDto)">
            <summary>
            修改员工
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IEmployeeService.DeleteEmployeeAsync(System.Guid)">
            <summary>
            删除员工
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IEmployeeService.EmployeeBindUserAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeBindUserDto)">
            <summary>
            绑定用户
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IEmployeeService.GetEmployeeInfoAsync(System.Guid)">
            <summary>
            获取员工信息
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IEmployeeService.GetEmployeeListAsync(Fancyx.Admin.IService.Organization.Dtos.EmployeeQueryDto)">
            <summary>
            获取员工列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IPositionGroupService.AddPositionGroupAsync(Fancyx.Admin.IService.Organization.Dtos.PositionGroupDto)">
            <summary>
            新增职位分组
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IPositionGroupService.GetPositionGroupListAsync(Fancyx.Admin.IService.Organization.Dtos.PositionGroupQueryDto)">
            <summary>
            职位分组分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IPositionGroupService.UpdatePositionGroupAsync(Fancyx.Admin.IService.Organization.Dtos.PositionGroupDto)">
            <summary>
            修改职位分组
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IPositionGroupService.DeletePositionGroupAsync(System.Guid)">
            <summary>
            删除职位分组
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IPositionService.AddPositionAsync(Fancyx.Admin.IService.Organization.Dtos.PositionDto)">
            <summary>
            新增职位
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IPositionService.GetPositionListAsync(Fancyx.Admin.IService.Organization.Dtos.PositionQueryDto)">
            <summary>
            职位分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IPositionService.UpdatePositionAsync(Fancyx.Admin.IService.Organization.Dtos.PositionDto)">
            <summary>
            修改职位
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IPositionService.DeletePositionAsync(System.Guid)">
            <summary>
            删除职位
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Organization.IPositionService.GetPositionTreeOptionAsync">
            <summary>
            职位分组+职位树
            </summary>
            <returns></returns>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.AIRecognitionRequestDto">
            <summary>
            AI识别请求 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.AIRecognitionRequestDto.ProjectCheckId">
            <summary>
            检查ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.AIRecognitionRequestDto.Photo">
            <summary>
            照片文件
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.AIRecognitionRequestDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto">
            <summary>
            创建隐患明细 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.ProjectCheckId">
            <summary>
            检查ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.StandardId">
            <summary>
            标准规范ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.SafetyHazard">
            <summary>
            安全隐患
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.RegulationClause">
            <summary>
            法规条款
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.Description">
            <summary>
            具体描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.RectificationRequirement">
            <summary>
            整改要求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.PossibleConsequences">
            <summary>
            可能产生后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.RectificationSuggestion">
            <summary>
            整改建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.Photos">
            <summary>
            照片列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto.Status">
            <summary>
            状态（0待确认，1正式记录）
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto">
            <summary>
            创建项目检查 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto.CheckDate">
            <summary>
            检查日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto.ClientUnit">
            <summary>
            委托单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto.CheckMembers">
            <summary>
            检查组员
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto.ProjectProgress">
            <summary>
            工程进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto.CheckContent">
            <summary>
            检查内容
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto">
            <summary>
            创建项目 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.ProjectAddress">
            <summary>
            项目地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.Region">
            <summary>
            所属区域
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.ProjectType">
            <summary>
            项目类型（生产安全、质量安全）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.RelatedContract">
            <summary>
            关联合同
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.ManagerPhone">
            <summary>
            负责人电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.ManagerPosition">
            <summary>
            负责人职务
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.StartDate">
            <summary>
            开工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.EndDate">
            <summary>
            竣工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.EngineeringType">
            <summary>
            工程类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.EngineeringCategory">
            <summary>
            工程类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.ConstructionUnit">
            <summary>
            建设单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.SupervisionUnit">
            <summary>
            监理单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.DesignUnit">
            <summary>
            设计单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.ConstructionCompany">
            <summary>
            施工单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.ConstructionPermitNo">
            <summary>
            施工许可证号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.HazardousChemicalWorkers">
            <summary>
            危险化学品从业人数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.CreateProjectDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto">
            <summary>
            隐患明细详情 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.ProjectCheckId">
            <summary>
            检查ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.CheckInfo">
            <summary>
            检查信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.StandardId">
            <summary>
            标准规范ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.StandardName">
            <summary>
            标准规范名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.SafetyHazard">
            <summary>
            安全隐患
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.RegulationClause">
            <summary>
            法规条款
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.Description">
            <summary>
            具体描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.RectificationRequirement">
            <summary>
            整改要求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.PossibleConsequences">
            <summary>
            可能产生后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.RectificationSuggestion">
            <summary>
            整改建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.Photos">
            <summary>
            照片列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.Status">
            <summary>
            状态（0待确认，1正式记录）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerDto.CreatorName">
            <summary>
            创建人
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto">
            <summary>
            隐患明细列表 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.CheckInfo">
            <summary>
            检查信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.SafetyHazard">
            <summary>
            安全隐患
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.Description">
            <summary>
            具体描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerListDto.CreatorName">
            <summary>
            创建人
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.HiddenDangerQueryDto">
            <summary>
            隐患明细查询条件 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerQueryDto.ProjectCheckId">
            <summary>
            检查ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerQueryDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerQueryDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerQueryDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.HiddenDangerQueryDto.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto">
            <summary>
            项目检查详情 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.CheckDate">
            <summary>
            检查日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.ClientUnit">
            <summary>
            委托单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.CheckMembers">
            <summary>
            检查组员
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.ProjectProgress">
            <summary>
            工程进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.CheckContent">
            <summary>
            检查内容
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckDto.CreatorName">
            <summary>
            创建人
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto">
            <summary>
            项目检查列表 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto.CheckDate">
            <summary>
            检查日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto.ClientUnit">
            <summary>
            委托单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckListDto.CreatorName">
            <summary>
            创建人
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto">
            <summary>
            项目检查查询条件 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto.ClientUnit">
            <summary>
            委托单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto.CheckDateStart">
            <summary>
            检查日期起始时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto.CheckDateEnd">
            <summary>
            检查日期结束时间
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.ProjectDto">
            <summary>
            项目信息 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.ProjectNo">
            <summary>
            项目编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.ProjectAddress">
            <summary>
            项目地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.Region">
            <summary>
            所属区域
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.ProjectType">
            <summary>
            项目类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.RelatedContract">
            <summary>
            关联合同
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.ManagerPhone">
            <summary>
            负责人电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.ManagerPosition">
            <summary>
            负责人职务
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.StartDate">
            <summary>
            开工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.EndDate">
            <summary>
            竣工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.EngineeringType">
            <summary>
            工程类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.EngineeringCategory">
            <summary>
            工程类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.ConstructionUnit">
            <summary>
            建设单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.SupervisionUnit">
            <summary>
            监理单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.DesignUnit">
            <summary>
            设计单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.ConstructionCompany">
            <summary>
            施工单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.ConstructionPermitNo">
            <summary>
            施工许可证号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.HazardousChemicalWorkers">
            <summary>
            危险化学品从业人数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectDto.CreatorName">
            <summary>
            创建人
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.ProjectListDto">
            <summary>
            项目列表 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.ProjectNo">
            <summary>
            项目编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.ProjectAddress">
            <summary>
            项目地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.Region">
            <summary>
            所属区域
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.ProjectType">
            <summary>
            项目类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.ManagerPhone">
            <summary>
            负责人电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.StartDate">
            <summary>
            开工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.EndDate">
            <summary>
            竣工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.EngineeringType">
            <summary>
            工程类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.ConstructionUnit">
            <summary>
            建设单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.ConstructionCompany">
            <summary>
            施工单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectListDto.CreatorName">
            <summary>
            创建人
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.ProjectOperationResult">
            <summary>
            项目操作结果枚举
            </summary>
        </member>
        <member name="F:Fancyx.Admin.IService.Project.Dtos.ProjectOperationResult.Success">
            <summary>
            操作成功
            </summary>
        </member>
        <member name="F:Fancyx.Admin.IService.Project.Dtos.ProjectOperationResult.ProjectNotFound">
            <summary>
            项目不存在
            </summary>
        </member>
        <member name="F:Fancyx.Admin.IService.Project.Dtos.ProjectOperationResult.Failed">
            <summary>
            操作失败
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto">
            <summary>
            项目查询条件 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto.ProjectNo">
            <summary>
            项目编号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto.Region">
            <summary>
            所属区域
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto.ProjectType">
            <summary>
            项目类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto.EngineeringType">
            <summary>
            工程类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto.EngineeringCategory">
            <summary>
            工程类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto.ConstructionUnit">
            <summary>
            建设单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto.ConstructionCompany">
            <summary>
            施工单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto">
            <summary>
            更新隐患明细 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.DangerCategory">
            <summary>
            隐患大类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.StandardId">
            <summary>
            标准规范ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.ProblemLevel">
            <summary>
            问题等级
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.SafetyHazard">
            <summary>
            安全隐患
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.RegulationClause">
            <summary>
            法规条款
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.Description">
            <summary>
            具体描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.RectificationRequirement">
            <summary>
            整改要求
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.PossibleConsequences">
            <summary>
            可能产生后果
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.RectificationSuggestion">
            <summary>
            整改建议
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.NeedReview">
            <summary>
            是否需要复查
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.Photos">
            <summary>
            照片列表
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto">
            <summary>
            更新项目检查 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto.CheckType">
            <summary>
            检查类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto.SecondaryCategory">
            <summary>
            二级分类
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto.CheckDate">
            <summary>
            检查日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto.ClientUnit">
            <summary>
            委托单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto.CheckLeader">
            <summary>
            检查组长
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto.CheckMembers">
            <summary>
            检查组员
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto.ProjectProgress">
            <summary>
            工程进度
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto.CheckContent">
            <summary>
            检查内容
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto">
            <summary>
            更新项目 DTO
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.ProjectName">
            <summary>
            项目名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.ProjectAddress">
            <summary>
            项目地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.Region">
            <summary>
            所属区域
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.ProjectType">
            <summary>
            项目类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.RelatedContract">
            <summary>
            关联合同
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.SiteManager">
            <summary>
            现场负责人
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.ManagerPhone">
            <summary>
            负责人电话
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.ManagerPosition">
            <summary>
            负责人职务
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.StartDate">
            <summary>
            开工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.EndDate">
            <summary>
            竣工日期
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.EngineeringType">
            <summary>
            工程类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.EngineeringCategory">
            <summary>
            工程类别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.ConstructionUnit">
            <summary>
            建设单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.SupervisionUnit">
            <summary>
            监理单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.DesignUnit">
            <summary>
            设计单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.ConstructionCompany">
            <summary>
            施工单位
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.ConstructionPermitNo">
            <summary>
            施工许可证号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.HazardousChemicalWorkers">
            <summary>
            危险化学品从业人数
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.IHiddenDangerService">
            <summary>
            隐患明细服务接口
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.GetHiddenDangerListAsync(Fancyx.Admin.IService.Project.Dtos.HiddenDangerQueryDto)">
            <summary>
            获取隐患明细分页列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.GetHiddenDangerByIdAsync(System.Guid)">
            <summary>
            根据ID获取隐患明细详情
            </summary>
            <param name="id">隐患明细ID</param>
            <returns>隐患明细信息</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.CreateHiddenDangerAsync(Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto)">
            <summary>
            创建隐患明细
            </summary>
            <param name="createDto">创建信息</param>
            <returns>隐患明细ID</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.UpdateHiddenDangerAsync(System.Guid,Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto)">
            <summary>
            更新隐患明细
            </summary>
            <param name="id">隐患明细ID</param>
            <param name="updateDto">更新信息</param>
            <returns>是否更新成功</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.DeleteHiddenDangerAsync(System.Guid)">
            <summary>
            删除隐患明细
            </summary>
            <param name="id">隐患明细ID</param>
            <returns>是否删除成功</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.ConfirmHiddenDangerAsync(System.Guid)">
            <summary>
            确认隐患明细（将待确认状态转为正式记录）
            </summary>
            <param name="id">隐患明细ID</param>
            <returns>是否确认成功</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.GetHiddenDangersByCheckIdAsync(System.Int64)">
            <summary>
            根据检查ID获取隐患明细列表
            </summary>
            <param name="projectCheckId">检查ID</param>
            <returns>隐患明细列表</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.GetMobileHiddenDangersAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto)">
            <summary>
            获取移动端隐患列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>隐患列表</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.CreateMobileHiddenDangerAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto)">
            <summary>
            创建移动端隐患
            </summary>
            <param name="createDto">创建信息</param>
            <returns>隐患ID</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.UpdateMobileHiddenDangerAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto)">
            <summary>
            更新移动端隐患
            </summary>
            <param name="id">隐患ID</param>
            <param name="updateDto">更新信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.GetDangerStatusAsync(System.Int64)">
            <summary>
            获取隐患状态
            </summary>
            <param name="id">隐患ID</param>
            <returns>状态信息</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.DeleteMobileHiddenDangerAsync(System.Int64)">
            <summary>
            删除移动端隐患
            </summary>
            <param name="id">隐患ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.GetMobileHiddenDangerDetailAsync(System.Int64)">
            <summary>
            获取移动端隐患详情
            </summary>
            <param name="id">隐患ID</param>
            <returns>隐患详情</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.ConfirmMobileHiddenDangerAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerConfirmDto)">
            <summary>
            确认移动端隐患
            </summary>
            <param name="id">隐患ID</param>
            <param name="confirmDto">确认信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.AddMobileRectificationRecordAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto)">
            <summary>
            添加移动端整改记录
            </summary>
            <param name="recordDto">整改记录</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.BatchUploadMultimediaAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadDto)">
            <summary>
            批量上传多媒体文件
            </summary>
            <param name="uploadDto">上传信息</param>
            <returns>上传结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.BatchOperationMobileHiddenDangersAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto)">
            <summary>
            批量操作移动端隐患
            </summary>
            <param name="operationDto">操作信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.ExportMobileHiddenDangerReportAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileExportRequestDto)">
            <summary>
            导出移动端隐患报告
            </summary>
            <param name="exportDto">导出参数</param>
            <returns>报告文件</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.GetMobileHiddenDangerStatisticsAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto)">
            <summary>
            获取移动端隐患统计
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>统计结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.GetMobileDangerTemplatesAsync(System.String)">
            <summary>
            获取移动端隐患模板
            </summary>
            <param name="category">隐患类别</param>
            <returns>模板列表</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IHiddenDangerService.SyncMobileHiddenDangerDataAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto)">
            <summary>
            同步移动端隐患数据
            </summary>
            <param name="syncDto">同步数据</param>
            <returns>同步结果</returns>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.IProjectCheckService">
            <summary>
            项目检查服务接口
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.GetProjectCheckListAsync(Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto)">
            <summary>
            获取项目检查分页列表
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.GetProjectCheckByIdAsync(System.Int64)">
            <summary>
            根据ID获取项目检查详情
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.CreateProjectCheckAsync(Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto)">
            <summary>
            创建项目检查
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.UpdateProjectCheckAsync(System.Int64,Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto)">
            <summary>
            更新项目检查
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.DeleteProjectCheckAsync(System.Int64)">
            <summary>
            删除项目检查
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.GenerateCheckTaskAsync(System.Int64,System.String,System.String)">
            <summary>
            根据项目生成检查任务
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.GetMobileCheckTasksAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto)">
            <summary>
            获取移动端检查任务列表
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.CreateMobileCheckTaskAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto)">
            <summary>
            创建移动端检查任务
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.UpdateMobileCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto)">
            <summary>
            更新移动端检查任务
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.HasDangersAsync(System.Int64)">
            <summary>
            检查是否有隐患
            </summary>
            <param name="checkId">检查ID</param>
            <returns>是否有隐患</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.DeleteMobileCheckTaskAsync(System.Int64)">
            <summary>
            删除移动端检查任务
            </summary>
            <param name="id">检查任务ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.ResumeMobileCheckTaskAsync(System.Int64)">
            <summary>
            恢复移动端检查任务
            </summary>
            <param name="id">检查任务ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.GetMobileCheckDetailAsync(System.Int64)">
            <summary>
            获取移动端检查详情
            </summary>
            <param name="id">检查任务ID</param>
            <returns>检查详情</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.BatchOperationMobileChecksAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto)">
            <summary>
            批量操作移动端检查
            </summary>
            <param name="operationDto">操作信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.CanStartCheckAsync(System.Int64)">
            <summary>
            检查是否可以开始检查
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.StartMobileCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileStartCheckDto)">
            <summary>
            开始移动端检查任务
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.PauseMobileCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobilePauseCheckDto)">
            <summary>
            暂停移动端检查任务
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.CompleteMobileCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileCheckCompleteDto)">
            <summary>
            完成移动端检查任务
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.SaveMobileCheckProgressAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileCheckProgressDto)">
            <summary>
            保存移动端检查进度
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.BatchOperateMobileCheckTasksAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchCheckOperationDto)">
            <summary>
            批量操作移动端检查任务
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.SyncMobileCheckDataAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto)">
            <summary>
            同步移动端检查数据
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectCheckService.ExportMobileCheckReportAsync(System.Int64,System.String)">
            <summary>
            导出移动端检查报告
            </summary>
        </member>
        <member name="T:Fancyx.Admin.IService.Project.IProjectService">
            <summary>
            项目管理服务接口
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.GetProjectListAsync(Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto)">
            <summary>
            获取项目分页列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>分页结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.GetProjectByIdAsync(System.Int64)">
            <summary>
            根据ID获取项目详情
            </summary>
            <param name="id">项目ID</param>
            <returns>项目信息</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.CreateProjectAsync(Fancyx.Admin.IService.Project.Dtos.CreateProjectDto)">
            <summary>
            创建项目
            </summary>
            <param name="createDto">创建信息</param>
            <returns>项目ID</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.UpdateProjectAsync(System.Int64,Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto)">
            <summary>
            更新项目
            </summary>
            <param name="id">项目ID</param>
            <param name="updateDto">更新信息</param>
            <returns>项目操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.DeleteProjectAsync(System.Int64)">
            <summary>
            删除项目
            </summary>
            <param name="id">项目ID</param>
            <returns>项目操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.GenerateProjectNoAsync">
            <summary>
            生成项目编号
            </summary>
            <returns>项目编号</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.GetMobileProjectListAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileProjectQueryDto)">
            <summary>
            获取移动端项目列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>移动端项目列表</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.GetMobileProjectDetailAsync(System.Int64)">
            <summary>
            获取移动端项目详情
            </summary>
            <param name="id">项目ID</param>
            <returns>移动端项目详情</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.CreateMobileProjectAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto)">
            <summary>
            移动端创建项目
            </summary>
            <param name="createDto">创建信息</param>
            <returns>项目ID</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.UpdateMobileProjectAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto)">
            <summary>
            移动端更新项目
            </summary>
            <param name="id">项目ID</param>
            <param name="updateDto">更新信息</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.QuickCreateCheckAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto)">
            <summary>
            快速创建检查任务（移动端专用）
            </summary>
            <param name="projectId">项目ID</param>
            <param name="createDto">创建参数</param>
            <returns>检查任务ID</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.HasActiveChecksAsync(System.Int64)">
            <summary>
            检查项目是否有活跃的检查任务
            </summary>
            <param name="projectId">项目ID</param>
            <returns>是否有活跃检查</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.DeleteMobileProjectAsync(System.Int64,System.String)">
            <summary>
            删除移动端项目
            </summary>
            <param name="id">项目ID</param>
            <param name="deleteReason">删除原因</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.GetMobileProjectStatisticsAsync(System.Nullable{System.Int64})">
            <summary>
            获取移动端项目统计信息
            </summary>
            <param name="projectId">项目ID（可选）</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.BatchOperationMobileProjectsAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchProjectOperationDto)">
            <summary>
            批量操作移动端项目
            </summary>
            <param name="operationDto">批量操作参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.IService.Project.IProjectService.SyncMobileProjectsAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncRequestDto)">
            <summary>
            同步移动端项目数据
            </summary>
            <param name="syncDto">同步参数</param>
            <returns>同步结果</returns>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.AssignDataDto.PowerDataType">
            <summary>
            数据权限类型（0全部1指定部门2部门及以下3本部门4仅本人）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.AssignMenuDto.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.AssignMenuDto.MenuIds">
            <summary>
            菜单ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.AssignRoleDto.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.AssignRoleDto.RoleIds">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.ConfigDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.ConfigListDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.ConfigListDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.ConfigListDto.LastModificationTime">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataDto.Id">
            <summary>
            字典ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataDto.Value">
            <summary>
            字典值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataDto.Label">
            <summary>
            显示文本
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataDto.DictType">
            <summary>
            字典类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataDto.Sort">
            <summary>
            排序值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataDto.IsEnabled">
            <summary>
            是否开启
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataListDto.Id">
            <summary>
            字典ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataListDto.Key">
            <summary>
            字典键
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataListDto.Value">
            <summary>
            字典值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataListDto.Label">
            <summary>
            显示文本
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataListDto.DictType">
            <summary>
            字典类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataListDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataListDto.Sort">
            <summary>
            排序值
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataListDto.IsEnabled">
            <summary>
            是否开启
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataQueryDto.Label">
            <summary>
            字典标签
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.DictDataQueryDto.DictType">
            <summary>
            字典类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.Id">
            <summary>
            菜单ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.Title">
            <summary>
            显示标题/名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.Path">
            <summary>
            路由/地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.MenuType">
            <summary>
            功能类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.Permission">
            <summary>
            授权码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.ParentId">
            <summary>
            父级ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.Display">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuDto.Component">
            <summary>
            组件地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.Id">
            <summary>
            菜单ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.Title">
            <summary>
            显示标题/名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.Path">
            <summary>
            路由/地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.MenuType">
            <summary>
            功能类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.Permission">
            <summary>
            授权码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.ParentId">
            <summary>
            父级ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.Display">
            <summary>
            隐藏
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.Component">
            <summary>
            组件地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuListDto.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuOptionTreeDto.Children">
            <summary>
            子集
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuQueryDto.Title">
            <summary>
            显示标题
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.MenuQueryDto.Path">
            <summary>
            路由
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.NotificationDto.Title">
            <summary>
            通知标题
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.NotificationDto.Description">
            <summary>
            通知描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.NotificationDto.EmployeeId">
            <summary>
            通知员工
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.NotificationResultDto.Title">
            <summary>
            通知标题
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.NotificationResultDto.Description">
            <summary>
            通知描述
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.NotificationResultDto.EmployeeId">
            <summary>
            通知员工
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.NotificationResultDto.IsReaded">
            <summary>
            是否已读(1已读0未读)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.NotificationResultDto.ReadedTime">
            <summary>
            已读时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.NotificationResultDto.EmployeeName">
            <summary>
            员工名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.ResetUserPwdDto.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.ResetUserPwdDto.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.RoleDto.Id">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.RoleDto.RoleName">
            <summary>
            角色名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.RoleDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.RoleListDto.Id">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.RoleListDto.RoleName">
            <summary>
            角色名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.RoleListDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.RoleQueryDto.RoleName">
            <summary>
            角色名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.TenantDto.Name">
            <summary>
            租户名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.TenantDto.TenantId">
            <summary>
            租户标识
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.TenantDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.TenantResultDto.Name">
            <summary>
            租户名称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.TenantResultDto.TenantId">
            <summary>
            租户标识
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.TenantResultDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.TenantSearchDto.Keyword">
            <summary>
            租户名称/标识
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserDto.UserName">
            <summary>
            用户名（大小写字母，数字，下划线，长度3-12位）
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserDto.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserDto.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserDto.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserDto.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserListDto.Id">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserListDto.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserListDto.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserListDto.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserListDto.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.Dtos.UserListDto.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IDictDataService.AddDictDataAsync(Fancyx.Admin.IService.System.Dtos.DictDataDto)">
            <summary>
            新增字典
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IDictDataService.GetDictDataListAsync(Fancyx.Admin.IService.System.Dtos.DictDataQueryDto)">
            <summary>
            字典分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IDictDataService.UpdateDictDataAsync(Fancyx.Admin.IService.System.Dtos.DictDataDto)">
            <summary>
            修改字典
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IDictDataService.DeleteDictDataAsync(System.Guid[])">
            <summary>
            删除字典
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IMenuService.AddMenuAsync(Fancyx.Admin.IService.System.Dtos.MenuDto)">
            <summary>
            新增菜单
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IMenuService.GetMenuListAsync(Fancyx.Admin.IService.System.Dtos.MenuQueryDto)">
            <summary>
            菜单树形列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IMenuService.UpdateMenuAsync(Fancyx.Admin.IService.System.Dtos.MenuDto)">
            <summary>
            修改菜单
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IMenuService.DeleteMenusAsync(System.Guid[])">
            <summary>
            删除菜单
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IMenuService.GetMenuOptionsAsync(System.Boolean,System.String)">
            <summary>
            获取菜单组成的选项树
            </summary>
            <param name="onlyMenu">true:只要目录+菜单</param>
            <param name="keyword">关键字筛选</param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IRoleService.AddRoleAsync(Fancyx.Admin.IService.System.Dtos.RoleDto)">
            <summary>
            新增角色
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IRoleService.GetRoleListAsync(Fancyx.Admin.IService.System.Dtos.RoleQueryDto)">
            <summary>
            角色分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IRoleService.UpdateRoleAsync(Fancyx.Admin.IService.System.Dtos.RoleDto)">
            <summary>
            修改角色
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IRoleService.DeleteRoleAsync(System.Guid)">
            <summary>
            删除角色
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IRoleService.AssignMenuAsync(Fancyx.Admin.IService.System.Dtos.AssignMenuDto)">
            <summary>
            分配菜单
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IRoleService.GetRoleOptionsAsync">
            <summary>
            获取角色
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IRoleService.GetRoleMenuIdsAsync(System.Guid)">
            <summary>
            获取指定角色菜单
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IRoleService.GetRolePowerDataAsync(System.Guid)">
            <summary>
            获取角色数据权限
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IRoleService.AssignDataAsync(Fancyx.Admin.IService.System.Dtos.AssignDataDto)">
            <summary>
            分配数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IUserService.AddUserAsync(Fancyx.Admin.IService.System.Dtos.UserDto)">
            <summary>
            新增用户
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IUserService.GetUserListAsync(Fancyx.Admin.IService.System.Dtos.UserQueryDto)">
            <summary>
            用户分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IUserService.DeleteUserAsync(System.Guid)">
            <summary>
            删除用户
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IUserService.AssignRoleAsync(Fancyx.Admin.IService.System.Dtos.AssignRoleDto)">
            <summary>
            分配角色
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IUserService.SwitchUserEnabledStatusAsync(System.Guid)">
            <summary>
            切换用户启用状态
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IUserService.GetUserRoleIdsAsync(System.Guid)">
            <summary>
            获取指定用户角色
            </summary>
            <param name="uid"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IUserService.ResetUserPasswordAsync(Fancyx.Admin.IService.System.Dtos.ResetUserPwdDto)">
            <summary>
            重置用户密码
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.IUserService.GetUserSimpleInfosAsync(System.String)">
            <summary>
            用户简单信息查询
            </summary>
            <param name="keyword">账号/昵称</param>
            <returns></returns>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogListDto.Type">
            <summary>
            日志类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogListDto.SubType">
            <summary>
            日志子类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogListDto.BizNo">
            <summary>
            业务编号/ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogListDto.Content">
            <summary>
            操作内容
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogListDto.Browser">
            <summary>
            浏览器
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogListDto.Ip">
            <summary>
            IP
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogListDto.TraceId">
            <summary>
            跟踪ID (用于关联一次请求的所有日志)
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogListDto.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogListDto.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogQueryDto.Type">
            <summary>
            业务类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogQueryDto.SubType">
            <summary>
            业务子类型
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogQueryDto.Content">
            <summary>
            操作内容
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogQueryDto.UserName">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogListDto.Id">
            <summary>
            日志ID
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogListDto.UserName">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogListDto.Ip">
            <summary>
            IP
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogListDto.Address">
            <summary>
            登录地址
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogListDto.Browser">
            <summary>
            浏览器
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogListDto.OperationMsg">
            <summary>
            操作信息
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogListDto.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogListDto.CreationTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogQueryDto.UserName">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogQueryDto.Status">
            <summary>
            是否成功 0全部1成功2失败
            </summary>
        </member>
        <member name="P:Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogQueryDto.Address">
            <summary>
            登录地址
            </summary>
        </member>
        <member name="M:Fancyx.Admin.IService.System.LogManagement.IBusinessLogService.GetBusinessLogListAsync(Fancyx.Admin.IService.System.LogManagement.Dtos.BusinessLogQueryDto)">
            <summary>
            业务日志分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.LogManagement.IBusinessLogService.GetBusinessTypeOptionsAsync(System.String)">
            <summary>
            获取所有业务类型选项
            </summary>
            <param name="type">业务类型模糊匹配</param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.IService.System.LogManagement.ILoginLogService.GetLoginLogListAsync(Fancyx.Admin.IService.System.LogManagement.Dtos.LoginLogQueryDto)">
            <summary>
            登录日志分页列表
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Middlewares.DemonstrationModeMiddleware.IsDemonstrationMode(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            是否演示模式
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Middlewares.DemonstrationModeMiddleware.IsIgnoreHttpMethod(System.String)">
            <summary>
            是否忽略请求
            </summary>
            <param name="method">请求方法</param>
            <returns></returns>
        </member>
        <member name="T:Fancyx.Admin.Profiles.ProjectAutoMapperProfile">
            <summary>
            项目映射配置
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Repositories.Project.HiddenDangerRepository">
            <summary>
            隐患明细仓储实现
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Repositories.Project.IHiddenDangerRepository">
            <summary>
            隐患明细仓储接口
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Repositories.Project.IProjectCheckRepository">
            <summary>
            项目检查仓储接口
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Repositories.Project.IProjectRepository">
            <summary>
            项目仓储接口
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Repositories.Project.ProjectCheckRepository">
            <summary>
            项目检查仓储实现
            </summary>
        </member>
        <member name="T:Fancyx.Admin.Repositories.Project.ProjectRepository">
            <summary>
            项目仓储实现
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Service.Account.AccountService.GenerateTokenAsync(System.Guid,System.String,System.String)">
            <summary>
            生成token
            </summary>
            <param name="userId"></param>
            <param name="userName"></param>
            <param name="sessionId"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Fancyx.Admin.Service.Account.AccountService.GetCurrentUserInfoAsync">
            <summary>
            获取当前用户信息（移动端专用）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.Service.Account.AccountService.GetUserPermissionsAsync(System.Guid)">
            <summary>
            获取用户权限列表（移动端专用）
            </summary>
            <param name="userId">用户ID</param>
            <returns></returns>
        </member>
        <member name="T:Fancyx.Admin.Service.Mobile.AiRecognitionService">
            <summary>
            AI识别服务实现
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Service.Mobile.AiRecognitionService.ProcessMobileRecognitionAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileAiRecognitionDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Mobile.AiRecognitionService.GetRecognitionTaskStatusAsync(System.String)">
            <summary>
            获取AI识别任务状态
            </summary>
            <param name="taskId">任务ID</param>
            <returns>任务状态</returns>
        </member>
        <member name="T:Fancyx.Admin.Service.Project.HiddenDangerService">
            <summary>
            隐患明细服务实现
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.GetHiddenDangerListAsync(Fancyx.Admin.IService.Project.Dtos.HiddenDangerQueryDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.GetHiddenDangerByIdAsync(System.Guid)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.CreateHiddenDangerAsync(Fancyx.Admin.IService.Project.Dtos.CreateHiddenDangerDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.UpdateHiddenDangerAsync(System.Guid,Fancyx.Admin.IService.Project.Dtos.UpdateHiddenDangerDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.DeleteHiddenDangerAsync(System.Guid)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.ConfirmHiddenDangerAsync(System.Guid)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.GetHiddenDangersByCheckIdAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.ParsePhotosFromJson(System.String)">
            <summary>
            安全解析照片JSON字符串
            </summary>
            <param name="photosJson">照片JSON字符串</param>
            <returns>照片列表</returns>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.GetMobileHiddenDangersAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileDangerQueryDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.CreateMobileHiddenDangerAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerCreateDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.UpdateMobileHiddenDangerAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerUpdateDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.GetDangerStatusAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.DeleteMobileHiddenDangerAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.GetMobileHiddenDangerDetailAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.SyncMobileHiddenDangerDataAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileSyncDataDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.ConfirmMobileHiddenDangerAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileHiddenDangerConfirmDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.AddMobileRectificationRecordAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileRectificationRecordDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.BatchUploadMultimediaAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileMultimediaUploadDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.BatchOperationMobileHiddenDangersAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.ExportMobileHiddenDangerReportAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileExportRequestDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.GetMobileHiddenDangerStatisticsAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileStatisticsQueryDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.HiddenDangerService.GetMobileDangerTemplatesAsync(System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Fancyx.Admin.Service.Project.ProjectCheckService">
            <summary>
            项目检查服务实现
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.GetProjectCheckListAsync(Fancyx.Admin.IService.Project.Dtos.ProjectCheckQueryDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.GetProjectCheckByIdAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.CreateProjectCheckAsync(Fancyx.Admin.IService.Project.Dtos.CreateProjectCheckDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.UpdateProjectCheckAsync(System.Int64,Fancyx.Admin.IService.Project.Dtos.UpdateProjectCheckDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.DeleteProjectCheckAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.GenerateCheckTaskAsync(System.Int64,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.GetMobileCheckTasksAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCheckQueryDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.CreateMobileCheckTaskAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCreateCheckDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.UpdateMobileCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateCheckDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.CanStartCheckAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.StartMobileCheckTaskAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileStartCheckDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.HasDangersAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.DeleteMobileCheckTaskAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.ResumeMobileCheckTaskAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.GetMobileCheckDetailAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectCheckService.BatchOperationMobileChecksAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchOperationDto)">
            <inheritdoc/>
        </member>
        <member name="T:Fancyx.Admin.Service.Project.ProjectService">
            <summary>
            项目管理服务实现
            </summary>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.GetProjectListAsync(Fancyx.Admin.IService.Project.Dtos.ProjectQueryDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.GetProjectByIdAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.CreateProjectAsync(Fancyx.Admin.IService.Project.Dtos.CreateProjectDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.UpdateProjectAsync(System.Int64,Fancyx.Admin.IService.Project.Dtos.UpdateProjectDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.DeleteProjectAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.GenerateProjectNoAsync">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.GetMobileProjectListAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileProjectQueryDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.GetMobileProjectDetailAsync(System.Int64)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.CreateMobileProjectAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileCreateProjectDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.UpdateMobileProjectAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileUpdateProjectDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.QuickCreateCheckAsync(System.Int64,Fancyx.Admin.IService.Mobile.Dtos.MobileQuickCheckDto)">
            <inheritdoc/>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.HasActiveChecksAsync(System.Int64)">
            <summary>
            检查项目是否有活跃的检查任务
            </summary>
            <param name="projectId">项目ID</param>
            <returns>是否有活跃检查</returns>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.DeleteMobileProjectAsync(System.Int64,System.String)">
            <summary>
            删除移动端项目
            </summary>
            <param name="id">项目ID</param>
            <param name="deleteReason">删除原因</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.GetMobileProjectStatisticsAsync(System.Nullable{System.Int64})">
            <summary>
            获取移动端项目统计信息
            </summary>
            <param name="projectId">项目ID（可选）</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.BatchOperationMobileProjectsAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileBatchProjectOperationDto)">
            <summary>
            批量操作移动端项目
            </summary>
            <param name="operationDto">批量操作参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:Fancyx.Admin.Service.Project.ProjectService.SyncMobileProjectsAsync(Fancyx.Admin.IService.Mobile.Dtos.MobileProjectSyncRequestDto)">
            <summary>
            同步移动端项目数据
            </summary>
            <param name="syncDto">同步参数</param>
            <returns>同步结果</returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.GetUserPermissionAsync(System.Guid)">
            <summary>
            获取用户权限
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.DelUserPermissionCacheByRoleIdAsync(System.Guid)">
            <summary>
            删除用户权限缓存（通过角色ID）
            </summary>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.DelUserPermissionCacheByUserIdAsync(System.Guid)">
            <summary>
            删除用户权限缓存（通过用户ID）
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.DelAdminUserPermissionCacheAsync">
            <summary>
            删除超级管理员用户权限缓存
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.DelUserPermissionCacheByMenuIdAsync(System.Guid)">
            <summary>
            删除用户权限缓存（通过菜单ID，通过RoleMenu关系删除）
            </summary>
            <param name="menuId"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.CheckTokenAsync(System.String,System.String,System.String)">
            <summary>
            检查Token是否存在
            </summary>
            <param name="userId"></param>
            <param name="sessionId"></param>
            <param name="token"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.CheckPermissionAsync(System.String,System.String)">
            <summary>
            检查用户是否有权限
            </summary>
            <param name="userId"></param>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.GenerateToken(System.Collections.Generic.IEnumerable{System.Security.Claims.Claim},System.DateTime)">
            <summary>
            生成Token
            </summary>
            <param name="claims"></param>
            <param name="expireTime"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.GetPrincipalFromAccessToken(System.String)">
            <summary>
            从Token中获取用户身份
            </summary>
            <param name="token"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.UserIsFromMainDbAsync(System.String)">
            <summary>
            用户是否来源主库
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.IdentitySharedService.GetPowerData">
            <summary>
            获取当前用户可查看的相关部门，员工
            </summary>
            <returns></returns>
        </member>
        <member name="M:Fancyx.Admin.SharedService.MqttSharedService.PushAsync``1(System.String,``0)">
            <summary>
            以指定主题推送消息
            </summary>
            <typeparam name="T"></typeparam>
            <param name="topic"></param>
            <param name="payload"></param>
            <returns></returns>
        </member>
    </members>
</doc>
