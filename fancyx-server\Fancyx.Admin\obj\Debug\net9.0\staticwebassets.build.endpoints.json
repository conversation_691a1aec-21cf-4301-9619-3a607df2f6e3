{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "avatar/female.jpo3bp7plx.png", "AssetFile": "avatar/female.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6504"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1z6NCY+ulYkG6MWc2qDfiphlBqzicMk0408cegG9GrY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:47:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jpo3bp7plx"}, {"Name": "integrity", "Value": "sha256-1z6NCY+ulYkG6MWc2qDfiphlBqzicMk0408cegG9GrY="}, {"Name": "label", "Value": "avatar/female.png"}]}, {"Route": "avatar/female.png", "AssetFile": "avatar/female.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6504"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"1z6NCY+ulYkG6MWc2qDfiphlBqzicMk0408cegG9GrY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:47:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1z6NCY+ulYkG6MWc2qDfiphlBqzicMk0408cegG9GrY="}]}, {"Route": "avatar/male.arke4br1v2.png", "AssetFile": "avatar/male.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10521"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ah+Oyo7u1oO3l5c1yXWGQ0btO+Io1gSCgHqlSo2mwcg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:47:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "arke4br1v2"}, {"Name": "integrity", "Value": "sha256-ah+Oyo7u1oO3l5c1yXWGQ0btO+Io1gSCgHqlSo2mwcg="}, {"Name": "label", "Value": "avatar/male.png"}]}, {"Route": "avatar/male.png", "AssetFile": "avatar/male.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10521"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"ah+Oyo7u1oO3l5c1yXWGQ0btO+Io1gSCgHqlSo2mwcg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 08:47:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ah+Oyo7u1oO3l5c1yXWGQ0btO+Io1gSCgHqlSo2mwcg="}]}]}