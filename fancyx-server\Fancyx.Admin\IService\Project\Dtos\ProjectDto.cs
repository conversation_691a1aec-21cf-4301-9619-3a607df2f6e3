using System;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 项目信息 DTO
    /// </summary>
    public class ProjectDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 项目编号
        /// </summary>
        public string ProjectNo { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 项目地址
        /// </summary>
        public string ProjectAddress { get; set; }

        /// <summary>
        /// 所属区域
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// 项目类型
        /// </summary>
        public string ProjectType { get; set; }

        /// <summary>
        /// 关联合同
        /// </summary>
        public string RelatedContract { get; set; }

        /// <summary>
        /// 现场负责人
        /// </summary>
        public string SiteManager { get; set; }

        /// <summary>
        /// 负责人电话
        /// </summary>
        public string ManagerPhone { get; set; }

        /// <summary>
        /// 负责人职务
        /// </summary>
        public string ManagerPosition { get; set; }

        /// <summary>
        /// 开工日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 竣工日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 工程类型
        /// </summary>
        public string EngineeringType { get; set; }

        /// <summary>
        /// 工程类别
        /// </summary>
        public string EngineeringCategory { get; set; }

        /// <summary>
        /// 建设单位
        /// </summary>
        public string ConstructionUnit { get; set; }

        /// <summary>
        /// 监理单位
        /// </summary>
        public string SupervisionUnit { get; set; }

        /// <summary>
        /// 设计单位
        /// </summary>
        public string DesignUnit { get; set; }

        /// <summary>
        /// 施工单位
        /// </summary>
        public string ConstructionCompany { get; set; }

        /// <summary>
        /// 施工许可证号
        /// </summary>
        public string ConstructionPermitNo { get; set; }

        /// <summary>
        /// 危险化学品从业人数
        /// </summary>
        public int? HazardousChemicalWorkers { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatorName { get; set; }
    }
}