using Microsoft.AspNetCore.Mvc;
using Fancyx.Admin.IService.Project;
using Fancyx.Admin.IService.Mobile.Dtos;
using Fancyx.Shared.Models;
using Fancyx.Core.Attributes;

namespace Fancyx.Admin.Controllers.Mobile;

/// <summary>
/// 移动端项目检查API
/// </summary>
[ApiController]
[Route("api/mobile/v1/checks")]
public class MobileProjectCheckController : ControllerBase
{
    private readonly IProjectCheckService _checkService;
    private readonly ILogger<MobileProjectCheckController> _logger;

    public MobileProjectCheckController(IProjectCheckService checkService, ILogger<MobileProjectCheckController> logger)
    {
        _checkService = checkService;
        _logger = logger;
    }

    /// <summary>
    /// 获取我的检查任务列表
    /// </summary>
    /// <param name="queryDto">查询参数</param>
    /// <returns>检查任务列表</returns>
    [HttpGet("my-tasks")]
    public async Task<AppResponse<PagedResult<MobileCheckTaskDto>>> GetMyCheckTasksAsync([FromQuery] MobileCheckQueryDto queryDto)
    {
        try
        {
            var result = await _checkService.GetMobileCheckTasksAsync(queryDto);
            return AppResponse<PagedResult<MobileCheckTaskDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取我的检查任务失败");
            return AppResponse<PagedResult<MobileCheckTaskDto>>.Error("获取检查任务失败");
        }
    }

    /// <summary>
    /// 创建检查任务
    /// </summary>
    /// <param name="createDto">创建参数</param>
    /// <returns>检查任务ID</returns>
    [HttpPost]
    [HasPermission("check:create")]
    public async Task<AppResponse<long>> CreateCheckTaskAsync([FromBody] MobileCreateCheckDto createDto)
    {
        try
        {
            // 验证创建参数
            var validationResult = ValidateCreateCheckData(createDto);
            if (!validationResult.IsValid)
            {
                return AppResponse<long>.Error(validationResult.ErrorMessage);
            }

            // 验证GPS位置信息
            if (createDto.Location != null && !IsValidLocation(createDto.Location))
            {
                return AppResponse<long>.Error("GPS位置信息无效");
            }

            var result = await _checkService.CreateMobileCheckTaskAsync(createDto);
            return AppResponse<long>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建检查任务失败");
            return AppResponse<long>.Error("创建检查任务失败，请重试");
        }
    }

    /// <summary>
    /// 更新检查任务
    /// </summary>
    /// <param name="id">检查任务ID</param>
    /// <param name="updateDto">更新参数</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    [HasPermission("check:update")]
    public async Task<AppResponse<bool>> UpdateCheckTaskAsync(long id, [FromBody] MobileUpdateCheckDto updateDto)
    {
        try
        {
            var validationResult = ValidateUpdateCheckData(updateDto);
            if (!validationResult.IsValid)
            {
                return AppResponse<bool>.Error(validationResult.ErrorMessage);
            }

            var result = await _checkService.UpdateMobileCheckTaskAsync(id, updateDto);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新检查任务失败，任务ID：{TaskId}", id);
            return AppResponse<bool>.Error("更新检查任务失败");
        }
    }

    /// <summary>
    /// 删除检查任务
    /// </summary>
    /// <param name="id">检查任务ID</param>
    /// <param name="deleteReason">删除原因</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [HasPermission("check:delete")]
    public async Task<AppResponse<bool>> DeleteCheckTaskAsync(long id, [FromQuery] string? deleteReason = null)
    {
        try
        {
            // 检查任务是否有关联的隐患记录
            var hasDangers = await _checkService.HasDangersAsync(id);
            if (hasDangers)
            {
                return AppResponse<bool>.Error("检查任务存在隐患记录，无法删除");
            }

            var result = await _checkService.DeleteMobileCheckTaskAsync(id);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除检查任务失败，任务ID：{TaskId}", id);
            return AppResponse<bool>.Error("删除检查任务失败");
        }
    }

    /// <summary>
    /// 开始检查任务
    /// </summary>
    /// <param name="id">检查任务ID</param>
    /// <param name="startDto">开始检查参数</param>
    /// <returns>检查任务详情</returns>
    [HttpPost("{id}/start")]
    [HasPermission("check:execute")]
    public async Task<AppResponse<MobileCheckDetailDto>> StartCheckTaskAsync(long id, [FromBody] MobileStartCheckDto startDto)
    {
        try
        {
            // 验证开始检查的条件
            var canStart = await _checkService.CanStartCheckAsync(id);
            if (!canStart.CanStart)
            {
                return AppResponse<MobileCheckDetailDto>.Error(canStart.Reason);
            }

            var result = await _checkService.StartMobileCheckTaskAsync(id, startDto);
            return AppResponse<MobileCheckDetailDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "开始检查任务失败，任务ID：{TaskId}", id);
            return AppResponse<MobileCheckDetailDto>.Error("开始检查任务失败");
        }
    }

    /// <summary>
    /// 暂停检查任务
    /// </summary>
    /// <param name="id">检查任务ID</param>
    /// <param name="pauseDto">暂停参数</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/pause")]
    [HasPermission("check:execute")]
    public async Task<AppResponse<bool>> PauseCheckTaskAsync(long id, [FromBody] MobilePauseCheckDto pauseDto)
    {
        try
        {
            var result = await _checkService.PauseMobileCheckTaskAsync(id, pauseDto);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "暂停检查任务失败，任务ID：{TaskId}", id);
            return AppResponse<bool>.Error("暂停检查任务失败");
        }
    }

    /// <summary>
    /// 恢复检查任务
    /// </summary>
    /// <param name="id">检查任务ID</param>
    /// <param name="resumeDto">恢复参数</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/resume")]
    [HasPermission("check:execute")]
    public async Task<AppResponse<bool>> ResumeCheckTaskAsync(long id, [FromBody] MobileResumeCheckDto resumeDto)
    {
        try
        {
            var result = await _checkService.ResumeMobileCheckTaskAsync(id);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "恢复检查任务失败，任务ID：{TaskId}", id);
            return AppResponse<bool>.Error("恢复检查任务失败");
        }
    }

    /// <summary>
    /// 完成检查任务
    /// </summary>
    /// <param name="id">检查任务ID</param>
    /// <param name="completeDto">完成信息</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/complete")]
    [HasPermission("check:execute")]
    public async Task<AppResponse<bool>> CompleteCheckTaskAsync(long id, [FromBody] MobileCheckCompleteDto completeDto)
    {
        try
        {
            var result = await _checkService.CompleteMobileCheckTaskAsync(id, completeDto);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "完成检查任务失败，任务ID：{TaskId}", id);
            return AppResponse<bool>.Error("完成检查任务失败");
        }
    }

    /// <summary>
    /// 获取检查任务详情
    /// </summary>
    /// <param name="id">检查任务ID</param>
    /// <returns>检查任务详情</returns>
    [HttpGet("{id}")]
    public async Task<AppResponse<MobileCheckDetailDto>> GetCheckDetailAsync(long id)
    {
        try
        {
            var result = await _checkService.GetMobileCheckDetailAsync(id);
            return AppResponse<MobileCheckDetailDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取检查任务详情失败，任务ID：{TaskId}", id);
            return AppResponse<MobileCheckDetailDto>.Error("获取检查任务详情失败");
        }
    }

    /// <summary>
    /// 保存检查进度
    /// </summary>
    /// <param name="id">检查任务ID</param>
    /// <param name="progressDto">进度信息</param>
    /// <returns>保存结果</returns>
    [HttpPost("{id}/save-progress")]
    [HasPermission("check:execute")]
    public async Task<AppResponse<bool>> SaveCheckProgressAsync(long id, [FromBody] MobileCheckProgressDto progressDto)
    {
        try
        {
            var result = await _checkService.SaveMobileCheckProgressAsync(id, progressDto);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存检查进度失败，任务ID：{TaskId}", id);
            return AppResponse<bool>.Error("保存检查进度失败");
        }
    }

    /// <summary>
    /// 批量操作检查任务
    /// </summary>
    /// <param name="batchDto">批量操作参数</param>
    /// <returns>操作结果</returns>
    [HttpPost("batch-operation")]
    [HasPermission("check:batch")]
    public async Task<AppResponse<MobileBatchOperationResultDto>> BatchOperationAsync([FromBody] MobileBatchCheckOperationDto batchDto)
    {
        try
        {
            if (batchDto.CheckIds == null || batchDto.CheckIds.Count == 0)
            {
                return AppResponse<MobileBatchOperationResultDto>.Error("请选择要操作的检查任务");
            }

            if (batchDto.CheckIds.Count > 50)
            {
                return AppResponse<MobileBatchOperationResultDto>.Error("单次最多操作50个检查任务");
            }

            // 转换DTO类型
            var batchOperation = new MobileBatchOperationDto
            {
                OperationType = batchDto.OperationType,
                TargetIds = batchDto.CheckIds
            };

            var result = await _checkService.BatchOperationMobileChecksAsync(batchOperation);
            return AppResponse<MobileBatchOperationResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量操作检查任务失败");
            return AppResponse<MobileBatchOperationResultDto>.Error("批量操作失败");
        }
    }

    /// <summary>
    /// 同步检查数据到云端
    /// </summary>
    /// <param name="syncDto">同步数据</param>
    /// <returns>同步结果</returns>
    [HttpPost("sync")]
    public async Task<AppResponse<MobileSyncResultDto>> SyncCheckDataAsync([FromBody] MobileSyncDataDto syncDto)
    {
        try
        {
            var result = await _checkService.SyncMobileCheckDataAsync(syncDto);
            return AppResponse<MobileSyncResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步检查数据失败");
            return AppResponse<MobileSyncResultDto>.Error("数据同步失败");
        }
    }

    /// <summary>
    /// 导出检查任务报告
    /// </summary>
    /// <param name="id">检查任务ID</param>
    /// <param name="format">导出格式（word/pdf/excel）</param>
    /// <returns>导出结果</returns>
    [HttpGet("{id}/export")]
    public async Task<AppResponse<MobileExportResultDto>> ExportCheckReportAsync(long id, [FromQuery] string format = "word")
    {
        try
        {
            if (!new[] { "word", "pdf", "excel" }.Contains(format.ToLower()))
            {
                return AppResponse<MobileExportResultDto>.Error("不支持的导出格式");
            }

            var result = await _checkService.ExportMobileCheckReportAsync(id, format);
            return AppResponse<MobileExportResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出检查报告失败，任务ID：{TaskId}，格式：{Format}", id, format);
            return AppResponse<MobileExportResultDto>.Error("导出报告失败");
        }
    }

    #region 私有方法

    private (bool IsValid, string ErrorMessage) ValidateCreateCheckData(MobileCreateCheckDto createDto)
    {
        if (createDto.ProjectId <= 0)
            return (false, "请选择关联项目");

        if (string.IsNullOrWhiteSpace(createDto.CheckType))
            return (false, "检查类型不能为空");

        if (string.IsNullOrWhiteSpace(createDto.SecondaryCategory))
            return (false, "二级分类不能为空");

        if (string.IsNullOrWhiteSpace(createDto.CheckLeader))
            return (false, "检查组长不能为空");

        if (createDto.CheckDate == DateTime.MinValue)
            return (false, "请选择检查日期");

        if (createDto.CheckDate < DateTime.Today)
            return (false, "检查日期不能早于今天");

        return (true, string.Empty);
    }

    private (bool IsValid, string ErrorMessage) ValidateUpdateCheckData(MobileUpdateCheckDto updateDto)
    {
        if (!string.IsNullOrWhiteSpace(updateDto.CheckDate?.ToString()) && updateDto.CheckDate < DateTime.Today)
            return (false, "检查日期不能早于今天");

        return (true, string.Empty);
    }

    private bool IsValidLocation(MobileLocationDto location)
    {
        return location.Latitude >= -90 && location.Latitude <= 90 &&
               location.Longitude >= -180 && location.Longitude <= 180 &&
               location.Accuracy > 0;
    }

    #endregion
}