namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端AI识别请求DTO
/// </summary>
public class MobileAiRecognitionDto
{
    /// <summary>
    /// 图片列表（Base64编码或URL）
    /// </summary>
    public List<string> Images { get; set; } = new();

    /// <summary>
    /// 识别类型（safety/quality/environment）
    /// </summary>
    public string RecognitionType { get; set; } = string.Empty;

    /// <summary>
    /// 项目ID
    /// </summary>
    public long? ProjectId { get; set; }

    /// <summary>
    /// 检查ID
    /// </summary>
    public long? CheckId { get; set; }

    /// <summary>
    /// GPS位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}


/// <summary>
/// 移动端识别到的隐患DTO
/// </summary>
public class MobileRecognizedDangerDto
{
    /// <summary>
    /// 隐患类型
    /// </summary>
    public string DangerType { get; set; } = string.Empty;

    /// <summary>
    /// 隐患描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 置信度分数
    /// </summary>
    public float ConfidenceScore { get; set; }

    /// <summary>
    /// 边界框信息
    /// </summary>
    public MobileBoundingBoxDto? BoundingBox { get; set; }

    /// <summary>
    /// 建议的整改措施
    /// </summary>
    public string? SuggestedAction { get; set; }
}

