// 模拟登录功能，用于演示登录页面效果
export const mockLogin = async (username: string, password: string) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // 模拟登录验证
  if (username === 'admin' && password === '123456') {
    return {
      success: true,
      data: {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        expiredTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        userInfo: {
          id: '1',
          username: 'admin',
          nickName: '系统管理员',
          avatar: '',
          menus: [
            {
              id: '1',
              title: '组织管理',
              icon: 'team',
              display: true,
              path: '/org',
              component: null,
              layerName: 'org',
              menuType: 1,
              children: [
                {
                  id: '1-1',
                  title: '部门管理',
                  icon: 'apartment',
                  display: true,
                  path: '/org/dept',
                  component: 'org/dept',
                  layerName: 'dept',
                  menuType: 2,
                  children: null
                },
                {
                  id: '1-2',
                  title: '员工管理',
                  icon: 'user',
                  display: true,
                  path: '/org/employee',
                  component: 'org/employee',
                  layerName: 'employee',
                  menuType: 2,
                  children: null
                },
                {
                  id: '1-3',
                  title: '职位管理',
                  icon: 'crown',
                  display: true,
                  path: '/org/position',
                  component: 'org/position',
                  layerName: 'position',
                  menuType: 2,
                  children: null
                },
                {
                  id: '1-4',
                  title: '职位组管理',
                  icon: 'team',
                  display: true,
                  path: '/org/positionGroup',
                  component: 'org/positionGroup',
                  layerName: 'positionGroup',
                  menuType: 2,
                  children: null
                }
              ]
            },
            {
              id: '2',
              title: '系统管理',
              icon: 'setting',
              display: true,
              path: '/system',
              component: null,
              layerName: 'system',
              menuType: 1,
              children: [
                {
                  id: '2-1',
                  title: '用户管理',
                  icon: 'user',
                  display: true,
                  path: '/system/user',
                  component: 'system/user',
                  layerName: 'user',
                  menuType: 2,
                  children: null
                },
                {
                  id: '2-2',
                  title: '角色管理',
                  icon: 'usergroup-add',
                  display: true,
                  path: '/system/role',
                  component: 'system/role',
                  layerName: 'role',
                  menuType: 2,
                  children: null
                },
                {
                  id: '2-3',
                  title: '菜单管理',
                  icon: 'menu',
                  display: true,
                  path: '/system/menu',
                  component: 'system/menu',
                  layerName: 'menu',
                  menuType: 2,
                  children: null
                },
                {
                  id: '2-4',
                  title: '系统配置',
                  icon: 'tool',
                  display: true,
                  path: '/system/config',
                  component: 'system/config',
                  layerName: 'config',
                  menuType: 2,
                  children: null
                }
              ]
            },
            {
              id: '3',
              title: '系统监控',
              icon: 'monitor',
              display: true,
              path: '/monitor',
              component: null,
              layerName: 'monitor',
              menuType: 1,
              children: [
                {
                  id: '3-1',
                  title: '在线用户',
                  icon: 'user',
                  display: true,
                  path: '/monitor/onlineUser',
                  component: 'monitor/onlineUser',
                  layerName: 'onlineUser',
                  menuType: 2,
                  children: null
                },
                {
                  id: '3-2',
                  title: 'API访问日志',
                  icon: 'api',
                  display: true,
                  path: '/monitor/apiAccessLog',
                  component: 'monitor/apiAccessLog',
                  layerName: 'apiAccessLog',
                  menuType: 2,
                  children: null
                },
                {
                  id: '3-3',
                  title: '异常日志',
                  icon: 'warning',
                  display: true,
                  path: '/monitor/exceptionLog',
                  component: 'monitor/exceptionLog',
                  layerName: 'exceptionLog',
                  menuType: 2,
                  children: null
                }
              ]
            },
            {
              id: '4',
              title: '项目管理',
              icon: 'project',
              display: true,
              path: '/project',
              component: null,
              layerName: 'project',
              menuType: 1,
              children: [
                {
                  id: '4-1',
                  title: '项目列表',
                  icon: 'bars',
                  display: true,
                  path: '/project/list',
                  component: 'project/list',
                  layerName: 'list',
                  menuType: 2,
                  children: null
                },
                {
                  id: '4-2',
                  title: '项目详情',
                  icon: 'eye',
                  display: false,
                  path: '/project/detail',
                  component: 'project/detail',
                  layerName: 'detail',
                  menuType: 2,
                  children: null
                },
                {
                  id: '4-3',
                  title: '项目编辑',
                  icon: 'edit',
                  display: false,
                  path: '/project/edit',
                  component: 'project/edit',
                  layerName: 'edit',
                  menuType: 2,
                  children: null
                }
              ]
            },
            {
              id: '5',
              title: '隐患管理',
              icon: 'warning',
              display: true,
              path: '/hiddenDanger',
              component: null,
              layerName: 'hiddenDanger',
              menuType: 1,
              children: [
                {
                  id: '5-1',
                  title: '隐患列表',
                  icon: 'bars',
                  display: true,
                  path: '/hiddenDanger/list',
                  component: 'hiddenDanger/list',
                  layerName: 'list',
                  menuType: 2,
                  children: null
                },
                {
                  id: '5-2',
                  title: '隐患详情',
                  icon: 'eye',
                  display: false,
                  path: '/hiddenDanger/detail',
                  component: 'hiddenDanger/detail',
                  layerName: 'detail',
                  menuType: 2,
                  children: null
                },
                {
                  id: '5-3',
                  title: '隐患编辑',
                  icon: 'edit',
                  display: false,
                  path: '/hiddenDanger/edit',
                  component: 'hiddenDanger/edit',
                  layerName: 'edit',
                  menuType: 2,
                  children: null
                }
              ]
            },
            {
              id: '6',
              title: '项目检查',
              icon: 'check-circle',
              display: true,
              path: '/projectCheck',
              component: null,
              layerName: 'projectCheck',
              menuType: 1,
              children: [
                {
                  id: '6-1',
                  title: '检查列表',
                  icon: 'bars',
                  display: true,
                  path: '/projectCheck/list',
                  component: 'projectCheck/list',
                  layerName: 'list',
                  menuType: 2,
                  children: null
                },
                {
                  id: '6-2',
                  title: '检查详情',
                  icon: 'eye',
                  display: false,
                  path: '/projectCheck/detail',
                  component: 'projectCheck/detail',
                  layerName: 'detail',
                  menuType: 2,
                  children: null
                },
                {
                  id: '6-3',
                  title: '检查编辑',
                  icon: 'edit',
                  display: false,
                  path: '/projectCheck/edit',
                  component: 'projectCheck/edit',
                  layerName: 'edit',
                  menuType: 2,
                  children: null
                }
              ]
            }
          ]
        }
      }
    };
  } else {
    throw new Error('用户名或密码错误');
  }
};