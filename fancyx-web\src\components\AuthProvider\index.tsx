import React, { createContext, useContext } from 'react';
import UserStore from '@/store/userStore.ts';
import { getUserAuth, login, type LoginDto } from '@/api/auth.ts';
import { clearTabs } from '@/store/tabStore.ts';
import { useDispatch } from 'react-redux';

export interface AuthProviderType {
  pwdLogin?: (values: LoginDto) => Promise<void>;
  clearToken: () => void;
  refreshUserAuthInfo?: () => Promise<void>;
}

const AuthContext = createContext<AuthProviderType>({
  clearToken: () => {},
});

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const dispatch = useDispatch();

  const pwdLogin = async (values: LoginDto) => {
    // 登录前先清理旧的认证状态
    UserStore.logout();
    
    try {
      // 调用登录API
      const res = await login(values);
      if (res.data) {
        // 设置token信息
        UserStore.setToken({
          sessionId: res.data.sessionId,
          accessToken: res.data.accessToken,
          refreshToken: res.data.refreshToken,
          expiredTime: res.data.expiredTime,
        });
        
        // 获取用户权限信息
        try {
          await setOrRefreshUserAuthInfo();
        } catch (authError) {
          console.error('获取用户权限信息失败:', authError);
          // 即使获取权限信息失败，也不清理token，让用户能正常使用基本功能
          // 可以设置一个默认的用户信息
          UserStore.setUserInfo({
            userId: res.data.userId || '',
            userName: res.data.userName || values.userName,
            nickName: '用户',
            avatar: '',
            sex: 1,
            menus: [],
            permissions: [],
          });
        }
      }
    } catch (error) {
      // 确保登录失败时清理状态
      UserStore.logout();
      console.error('登录失败:', error);
      throw error;
    }
  };
  const clearToken = () => {
    UserStore.logout();
    dispatch(clearTabs());
  };

  const setOrRefreshUserAuthInfo = async () => {
    // 直接尝试获取用户权限信息，不依赖 isAuthenticated 检查
    // 因为刚设置完 token，isAuthenticated 可能还没有正确反映状态
    const { data: authInfo } = await getUserAuth();
    const _authInfo = {
      userId: authInfo.user.userId,
      userName: authInfo.user.userName,
      nickName: authInfo.user.nickName,
      avatar: authInfo.user.avatar,
      sex: authInfo.user.sex,
      menus: authInfo.menus,
      permissions: authInfo.permissions,
    };
    UserStore.setUserInfo(_authInfo);
  };

  return (
    <AuthContext.Provider
      value={{
        pwdLogin,
        clearToken,
        refreshUserAuthInfo: setOrRefreshUserAuthInfo,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useAuthProvider = () => useContext(AuthContext);
