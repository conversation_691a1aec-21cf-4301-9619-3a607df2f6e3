import type { RouteObject } from "react-router-dom";
import React, { lazy } from "react";
import type { FrontendMenu } from "@/api/auth";

// 获取所有页面文件
const PageKeys = Object.keys(import.meta.glob([
  "@/pages/**/index.tsx",
  "@/pages/**/*.tsx"
], { eager: true }));
const PagesList = import.meta.glob([
  "@/pages/**/index.tsx",
  "@/pages/**/*.tsx"
]);

const getChildrenRoutes = (children: FrontendMenu[]) => {
  const newChildren: RouteObject[] = [];
  children?.forEach((route) => {
    // 如果component为null或空，尝试根据path推断组件路径
    let componentPath: string;
    if (route.component) {
      componentPath = `/src/pages/${route.component}.tsx`;
    } else {
      // 根据完整路径推断组件路径
      const fullPath = route.path?.replace(/^\//, ''); // 移除前导斜杠
      componentPath = `/src/pages/${fullPath}.tsx`;
    }
    
    // 子路由的路径计算：移除父路径部分，只保留子路径
    let relativePath: string | undefined;
    if (route.path) {
      const pathParts = route.path.split('/').filter(p => p);
      if (pathParts.length > 1) {
        // 子路由使用最后一个路径部分作为相对路径
        relativePath = pathParts[pathParts.length - 1];
      } else {
        relativePath = route.path.replace(/^\//, '');
      }
    }
    
    const newRoute: RouteObject = {
      path: relativePath,
    };
    
    if (PageKeys.includes(componentPath)) {
      newRoute.element = React.createElement(
        lazy(() => PagesList[componentPath]() as Promise<{ default: React.ComponentType<any> }>)
      );
    } else {
      // 尝试寻找匹配的组件文件
      const pathParts = route.path?.split('/').filter(p => p);
      if (pathParts && pathParts.length >= 2) {
        const fallbackPath = `/src/pages/${pathParts[1]}/${pathParts[2] || 'index'}.tsx`;
        if (PageKeys.includes(fallbackPath)) {
          newRoute.element = React.createElement(
            lazy(() => PagesList[fallbackPath]() as Promise<{ default: React.ComponentType<any> }>)
          );
        }
      }
    }
    
    if (route.children != null && route.children.length > 0) {
      newRoute.children = getChildrenRoutes(route.children);
    }
    newChildren.push(newRoute);
  });
  
  return newChildren;
};

export const generateDynamicRoutes = (apiRoutes: FrontendMenu[]) => {
  const dynamicRoutes: RouteObject[] = [];
  apiRoutes?.forEach((route) => {
    // 如果component为null或空，尝试根据path推断组件路径
    let componentPath: string;
    if (route.component) {
      componentPath = `/src/pages/${route.component}.tsx`;
    } else {
      // 根据路径推断组件，优先使用index文件作为容器组件
      const pathSegment = route.path?.substring(1); // 移除前导斜杠
      // 优先尝试使用 index.tsx 作为容器组件
      componentPath = `/src/pages/${pathSegment}/index.tsx`;
    }
    
    // 确保主路由使用相对路径（在Layout子路由中）
    const relativePath = route.path?.startsWith('/') ? route.path.substring(1) : route.path;
    
    const newRoute: RouteObject = {
      path: relativePath
    };

    if (PageKeys.includes(componentPath)) {
      newRoute.element = React.createElement(
        lazy(() => PagesList[componentPath]() as Promise<{ default: React.ComponentType<any> }>)
      );
    } else {
      // 如果推断的组件不存在，尝试寻找该目录下的第一个组件
      const pathSegment = route.path?.substring(1);
      const matchingFiles = PageKeys.filter(key => key.includes(`/pages/${pathSegment}/`));
      if (matchingFiles.length > 0) {
        const fallbackComponent = matchingFiles[0];
        newRoute.element = React.createElement(
          lazy(() => PagesList[fallbackComponent]() as Promise<{ default: React.ComponentType<any> }>)
        );
      }
    }
    
    // 处理子路由
    if (route.children != null && route.children.length > 0) {
      newRoute.children = getChildrenRoutes(route.children);
    }
    
    // 为项目管理模块添加额外的路由配置
    if (route.path === '/project') {
      // 确保项目管理的所有子路由都能正确访问
      const additionalRoutes: RouteObject[] = [
        {
          path: 'edit',
          element: React.createElement(
            lazy(() => PagesList['/src/pages/project/edit.tsx']() as Promise<{ default: React.ComponentType<any> }>)
          )
        },
        {
          path: 'edit/:id',
          element: React.createElement(
            lazy(() => PagesList['/src/pages/project/edit.tsx']() as Promise<{ default: React.ComponentType<any> }>)
          )
        },
        {
          path: 'detail/:id',
          element: React.createElement(
            lazy(() => PagesList['/src/pages/project/detail.tsx']() as Promise<{ default: React.ComponentType<any> }>)
          )
        }
      ];
      
      if (newRoute.children) {
        newRoute.children.push(...additionalRoutes);
      } else {
        newRoute.children = additionalRoutes;
      }
    }
    
    dynamicRoutes.push(newRoute);
  });
  
  return dynamicRoutes;
};
