import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';

const MonitorIndex: React.FC = () => {
  const location = useLocation();

  // 如果访问 /monitor 根路径，重定向到在线用户
  React.useEffect(() => {
    if (location.pathname === '/monitor') {
      window.location.replace('/monitor/onlineUser');
    }
  }, [location.pathname]);

  return (
    <div className="monitor-container">
      <Outlet />
    </div>
  );
};

export default MonitorIndex;