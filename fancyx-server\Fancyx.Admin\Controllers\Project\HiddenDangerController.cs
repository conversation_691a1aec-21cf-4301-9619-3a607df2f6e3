using Fancyx.Admin.IService.Project;
using Fancyx.Admin.IService.Project.Dtos;
using Fancyx.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Fancyx.Admin.Controllers.Project
{
    /// <summary>
    /// 隐患明细管理控制器
    /// </summary>
    [Route("api/hidden-danger")]
    [Authorize]
    [ApiController]
    public class HiddenDangerController : ControllerBase
    {
        private readonly IHiddenDangerService _hiddenDangerService;

        public HiddenDangerController(IHiddenDangerService hiddenDangerService)
        {
            _hiddenDangerService = hiddenDangerService;
        }

        /// <summary>
        /// 获取隐患明细列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>隐患明细列表</returns>
        [HttpGet]
        [Route("list")]
        public async Task<AppResponse<PagedResult<HiddenDangerListDto>>> GetHiddenDangerList([FromQuery] HiddenDangerQueryDto queryDto)
        {
            var result = await _hiddenDangerService.GetHiddenDangerListAsync(queryDto);
            return Result.Data(result);
        }

        /// <summary>
        /// 根据ID获取隐患明细详情
        /// </summary>
        /// <param name="id">隐患明细ID</param>
        /// <returns>隐患明细详情</returns>
        [HttpGet]
        [Route("{id}")]
        public async Task<AppResponse<HiddenDangerDto>> GetHiddenDangerById(Guid id)
        {
            var hiddenDanger = await _hiddenDangerService.GetHiddenDangerByIdAsync(id);
            return Result.Data(hiddenDanger);
        }

        /// <summary>
        /// 创建隐患明细
        /// </summary>
        /// <param name="createDto">隐患明细信息</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<AppResponse<Guid>> CreateHiddenDanger([FromBody] CreateHiddenDangerDto createDto)
        {
            var hiddenDangerId = await _hiddenDangerService.CreateHiddenDangerAsync(createDto);
            return Result.Data(hiddenDangerId);
        }

        /// <summary>
        /// 更新隐患明细
        /// </summary>
        /// <param name="id">隐患明细ID</param>
        /// <param name="updateDto">更新信息</param>
        /// <returns>更新结果</returns>
        [HttpPut]
        [Route("{id}")]
        public async Task<AppResponse<bool>> UpdateHiddenDanger(Guid id, [FromBody] UpdateHiddenDangerDto updateDto)
        {
            var result = await _hiddenDangerService.UpdateHiddenDangerAsync(id, updateDto);
            return result ? Result.Ok() : Result.Fail();
        }

        /// <summary>
        /// 删除隐患明细
        /// </summary>
        /// <param name="id">隐患明细ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete]
        [Route("{id}")]
        public async Task<AppResponse<bool>> DeleteHiddenDanger(Guid id)
        {
            var result = await _hiddenDangerService.DeleteHiddenDangerAsync(id);
            return result ? Result.Ok() : Result.Fail();
        }

        /// <summary>
        /// 确认隐患明细（将待确认状态转为正式记录）
        /// </summary>
        /// <param name="id">隐患明细ID</param>
        /// <returns>确认结果</returns>
        [HttpPost]
        [Route("{id}/confirm")]
        public async Task<AppResponse<bool>> ConfirmHiddenDanger(Guid id)
        {
            var result = await _hiddenDangerService.ConfirmHiddenDangerAsync(id);
            return result ? Result.Ok() : Result.Fail();
        }

        /// <summary>
        /// 根据检查ID获取隐患明细列表
        /// </summary>
        /// <param name="projectCheckId">检查ID</param>
        /// <returns>隐患明细列表</returns>
        [HttpGet]
        [Route("by-check/{projectCheckId}")]
        public async Task<AppResponse<List<HiddenDangerDto>>> GetHiddenDangersByCheckId(long projectCheckId)
        {
            var result = await _hiddenDangerService.GetHiddenDangersByCheckIdAsync(projectCheckId);
            return Result.Data(result);
        }
    }
}