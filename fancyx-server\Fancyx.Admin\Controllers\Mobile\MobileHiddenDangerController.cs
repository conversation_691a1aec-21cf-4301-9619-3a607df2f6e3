using Microsoft.AspNetCore.Mvc;
using Fancyx.Admin.IService.Project;
using Fancyx.Admin.IService.Mobile;
using Fancyx.Admin.IService.Mobile.Dtos;
using Fancyx.Shared.Models;
using Fancyx.Core.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.Controllers.Mobile;

/// <summary>
/// 移动端隐患明细管理API
/// </summary>
[ApiController]
[Route("api/mobile/v1/hidden-dangers")]
public class MobileHiddenDangerController : ControllerBase
{
    private readonly IHiddenDangerService _hiddenDangerService;
    private readonly IAiRecognitionService _aiRecognitionService;
    private readonly ILogger<MobileHiddenDangerController> _logger;

    public MobileHiddenDangerController(
        IHiddenDangerService hiddenDangerService,
        IAiRecognitionService aiRecognitionService,
        ILogger<MobileHiddenDangerController> logger)
    {
        _hiddenDangerService = hiddenDangerService;
        _aiRecognitionService = aiRecognitionService;
        _logger = logger;
    }

    /// <summary>
    /// 获取我的隐患列表
    /// </summary>
    /// <param name="queryDto">查询参数</param>
    /// <returns>隐患列表</returns>
    [HttpGet("my-dangers")]
    public async Task<AppResponse<PagedResult<MobileHiddenDangerListDto>>> GetMyHiddenDangersAsync([FromQuery] MobileHiddenDangerQueryDto queryDto)
    {
        try
        {
            // 转换DTO类型
            var mobileDangerQuery = new MobileDangerQueryDto
            {
                ProjectId = queryDto.ProjectId,
                Current = queryDto.Current,
                PageSize = queryDto.PageSize
            };

            var result = await _hiddenDangerService.GetMobileHiddenDangersAsync(mobileDangerQuery);
            return AppResponse<PagedResult<MobileHiddenDangerListDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取我的隐患列表失败");
            return AppResponse<PagedResult<MobileHiddenDangerListDto>>.Error("获取隐患列表失败");
        }
    }

    /// <summary>
    /// 创建隐患记录（支持AI识别）
    /// </summary>
    /// <param name="createDto">创建参数</param>
    /// <returns>隐患ID</returns>
    [HttpPost]
    [HasPermission("danger:create")]
    public async Task<AppResponse<long>> CreateHiddenDangerAsync([FromBody] MobileCreateHiddenDangerDto createDto)
    {
        try
        {
            // 验证创建参数
            var validationResult = ValidateCreateDangerData(createDto);
            if (!validationResult.IsValid)
            {
                return AppResponse<long>.Error(validationResult.ErrorMessage);
            }

            // 验证GPS位置信息
            if (!IsValidLocation(createDto.Location))
            {
                return AppResponse<long>.Error("GPS位置信息无效");
            }

            // 验证照片是否存在
            if (createDto.Photos == null || createDto.Photos.Count == 0)
            {
                return AppResponse<long>.Error("请至少上传一张现场照片");
            }

            // 转换DTO类型
            var hiddenDangerCreate = new MobileHiddenDangerCreateDto
            {
                ProjectId = createDto.ProjectCheckId,
                CheckId = createDto.ProjectCheckId,
                DangerCategory = createDto.DangerCategory,
                ProblemLevel = createDto.ProblemLevel,
                SafetyHazard = createDto.ProblemDescription ?? string.Empty,
                Description = createDto.ProblemDescription ?? string.Empty,
                RectificationRequirement = createDto.LocationDescription ?? string.Empty,
                PossibleConsequences = string.Empty,
                RectificationSuggestion = string.Empty,
                Photos = new List<string>(),
                Location = createDto.Location,
                DeviceInfo = createDto.DeviceInfo
            };

            var result = await _hiddenDangerService.CreateMobileHiddenDangerAsync(hiddenDangerCreate);
            return AppResponse<long>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建隐患记录失败");
            return AppResponse<long>.Error("创建隐患记录失败，请重试");
        }
    }

    /// <summary>
    /// 更新隐患记录
    /// </summary>
    /// <param name="id">隐患ID</param>
    /// <param name="updateDto">更新参数</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    [HasPermission("danger:update")]
    public async Task<AppResponse<bool>> UpdateHiddenDangerAsync(long id, [FromBody] MobileUpdateHiddenDangerDto updateDto)
    {
        try
        {
            var validationResult = ValidateUpdateDangerData(updateDto);
            if (!validationResult.IsValid)
            {
                return AppResponse<bool>.Error(validationResult.ErrorMessage);
            }

            // 转换DTO类型
            var hiddenDangerUpdate = new MobileHiddenDangerUpdateDto
            {
                DangerCategory = updateDto.DangerCategory,
                ProblemLevel = updateDto.ProblemLevel,
                SafetyHazard = updateDto.ProblemDescription,
                Description = updateDto.ProblemDescription,
                RectificationRequirement = updateDto.LocationDescription,
                Photos = new List<string>(),
                UpdateReason = "移动端更新"
            };

            var result = await _hiddenDangerService.UpdateMobileHiddenDangerAsync(id, hiddenDangerUpdate);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新隐患记录失败，隐患ID：{DangerId}", id);
            return AppResponse<bool>.Error("更新隐患记录失败");
        }
    }

    /// <summary>
    /// 删除隐患记录
    /// </summary>
    /// <param name="id">隐患ID</param>
    /// <param name="deleteReason">删除原因</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [HasPermission("danger:delete")]
    public async Task<AppResponse<bool>> DeleteHiddenDangerAsync(long id, [FromQuery] string? deleteReason = null)
    {
        try
        {
            // 检查隐患是否已被确认或处理中
            var dangerStatus = await _hiddenDangerService.GetDangerStatusAsync(id);
            if (dangerStatus.IsProcessing)
            {
                return AppResponse<bool>.Error("隐患正在处理中，无法删除");
            }

            var result = await _hiddenDangerService.DeleteMobileHiddenDangerAsync(id);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除隐患记录失败，隐患ID：{DangerId}", id);
            return AppResponse<bool>.Error("删除隐患记录失败");
        }
    }

    /// <summary>
    /// 获取隐患详情
    /// </summary>
    /// <param name="id">隐患ID</param>
    /// <returns>隐患详情</returns>
    [HttpGet("{id}/detail")]
    public async Task<AppResponse<MobileHiddenDangerDetailDto>> GetHiddenDangerDetailAsync(long id)
    {
        try
        {
            var result = await _hiddenDangerService.GetMobileHiddenDangerDetailAsync(id);
            return AppResponse<MobileHiddenDangerDetailDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取隐患详情失败，隐患ID：{DangerId}", id);
            return AppResponse<MobileHiddenDangerDetailDto>.Error("获取隐患详情失败");
        }
    }

    /// <summary>
    /// 确认隐患
    /// </summary>
    /// <param name="id">隐患ID</param>
    /// <param name="confirmDto">确认参数</param>
    /// <returns>确认结果</returns>
    [HttpPost("{id}/confirm")]
    [HasPermission("danger:confirm")]
    public async Task<AppResponse<bool>> ConfirmHiddenDangerAsync(long id, [FromBody] MobileConfirmHiddenDangerDto confirmDto)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(confirmDto.ConfirmationType))
            {
                return AppResponse<bool>.Error("确认类型不能为空");
            }

            if (string.IsNullOrWhiteSpace(confirmDto.ConfirmationComment))
            {
                return AppResponse<bool>.Error("确认意见不能为空");
            }

            // 转换DTO类型
            var hiddenDangerConfirm = new MobileHiddenDangerConfirmDto
            {
                ConfirmComment = "移动端确认",
                ConfirmResult = "confirmed",
                ConfirmBy = "移动端用户",
                ConfirmTime = DateTime.UtcNow
            };

            var result = await _hiddenDangerService.ConfirmMobileHiddenDangerAsync(id, hiddenDangerConfirm);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "确认隐患失败，隐患ID：{DangerId}", id);
            return AppResponse<bool>.Error("确认隐患失败");
        }
    }

    /// <summary>
    /// 添加整改记录
    /// </summary>
    /// <param name="id">隐患ID</param>
    /// <param name="recordDto">整改记录</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/rectification")]
    [HasPermission("danger:rectify")]
    public async Task<AppResponse<bool>> AddRectificationRecordAsync(long id, [FromBody] MobileRectificationRecordDto recordDto)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(recordDto.RectificationStatus))
            {
                return AppResponse<bool>.Error("整改状态不能为空");
            }

            if (string.IsNullOrWhiteSpace(recordDto.RectificationDescription))
            {
                return AppResponse<bool>.Error("整改说明不能为空");
            }

            var result = await _hiddenDangerService.AddMobileRectificationRecordAsync(recordDto);
            return AppResponse<bool>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加整改记录失败，隐患ID：{DangerId}", id);
            return AppResponse<bool>.Error("添加整改记录失败");
        }
    }

    /// <summary>
    /// AI智能识别隐患
    /// </summary>
    /// <param name="recognitionDto">识别参数</param>
    /// <returns>识别结果</returns>
    [HttpPost("ai-recognition")]
    [HasPermission("danger:ai_recognition")]
    public async Task<AppResponse<MobileAiRecognitionResultDto>> AiRecognitionAsync([FromForm] MobileAiRecognitionRequestDto recognitionDto)
    {
        try
        {
            // 验证识别参数
            var validationResult = ValidateAiRecognitionData(recognitionDto);
            if (!validationResult.IsValid)
            {
                return AppResponse<MobileAiRecognitionResultDto>.Error(validationResult.ErrorMessage);
            }

            // 验证文件大小和格式
            var fileValidation = ValidateMultimediaFiles(recognitionDto.PhotoFiles, recognitionDto.AudioFiles);
            if (!fileValidation.IsValid)
            {
                return AppResponse<MobileAiRecognitionResultDto>.Error(fileValidation.ErrorMessage);
            }

            // 转换DTO类型
            var aiRecognitionDto = new MobileAiRecognitionDto
            {
                Images = new List<string>(), // 这里需要处理文件上传并转换为Base64或URL
                RecognitionType = recognitionDto.RecognitionType,
                ProjectId = null, // 可以从上下文中获取
                CheckId = null,
                Location = null, // 可以从recognitionDto.Context中获取
                DeviceInfo = new MobileDeviceInfoDto()
            };

            var result = await _aiRecognitionService.ProcessMobileRecognitionAsync(aiRecognitionDto);
            return AppResponse<MobileAiRecognitionResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI识别处理失败");
            return AppResponse<MobileAiRecognitionResultDto>.Error("AI识别失败，请重试");
        }
    }

    /// <summary>
    /// 获取AI识别任务状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>任务状态</returns>
    [HttpGet("ai-recognition/{taskId}/status")]
    public async Task<AppResponse<MobileAiRecognitionResultDto>> GetAiRecognitionStatusAsync(string taskId)
    {
        try
        {
            var result = await _aiRecognitionService.GetRecognitionTaskStatusAsync(taskId);
            return AppResponse<MobileAiRecognitionResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取AI识别任务状态失败，任务ID：{TaskId}", taskId);
            return AppResponse<MobileAiRecognitionResultDto>.Error("获取识别状态失败");
        }
    }

    /// <summary>
    /// 批量上传多媒体文件
    /// </summary>
    /// <param name="uploadDto">上传参数</param>
    /// <returns>上传结果</returns>
    [HttpPost("batch-multimedia-upload")]
    [HasPermission("file:upload")]
    public async Task<AppResponse<MobileMultimediaUploadResultDto>> BatchUploadMultimediaAsync([FromForm] MobileBatchMultimediaUploadDto uploadDto)
    {
        try
        {
            // 验证文件数量和大小
            var totalFiles = (uploadDto.PhotoFiles?.Count ?? 0) + (uploadDto.AudioFiles?.Count ?? 0);
            if (totalFiles == 0)
            {
                return AppResponse<MobileMultimediaUploadResultDto>.Error("请至少选择一个文件上传");
            }

            if (totalFiles > 20)
            {
                return AppResponse<MobileMultimediaUploadResultDto>.Error("单次最多上传20个文件");
            }

            // 验证文件格式和大小
            var fileValidation = ValidateMultimediaFiles(uploadDto.PhotoFiles, uploadDto.AudioFiles);
            if (!fileValidation.IsValid)
            {
                return AppResponse<MobileMultimediaUploadResultDto>.Error(fileValidation.ErrorMessage);
            }

            // 转换DTO类型
            var multimediaUpload = new MobileMultimediaUploadDto
            {
                DangerId = uploadDto.HiddenDangerId,
                Files = new List<MobileFileInfoDto>()
            };

            var batchResult = await _hiddenDangerService.BatchUploadMultimediaAsync(multimediaUpload);

            // 转换返回类型
            var result = new MobileMultimediaUploadResultDto
            {
                TotalCount = batchResult.TotalCount,
                SuccessCount = batchResult.SuccessCount,
                FailedCount = batchResult.FailedCount,
                BatchId = Guid.NewGuid().ToString()
            };

            return AppResponse<MobileMultimediaUploadResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量上传多媒体文件失败");
            return AppResponse<MobileMultimediaUploadResultDto>.Error("文件上传失败");
        }
    }

    /// <summary>
    /// 批量操作隐患
    /// </summary>
    /// <param name="batchDto">批量操作参数</param>
    /// <returns>操作结果</returns>
    [HttpPost("batch-operation")]
    [HasPermission("danger:batch")]
    public async Task<AppResponse<MobileBatchOperationResultDto>> BatchOperationAsync([FromBody] MobileBatchHiddenDangerOperationDto batchDto)
    {
        try
        {
            if (batchDto.DangerIds == null || batchDto.DangerIds.Count == 0)
            {
                return AppResponse<MobileBatchOperationResultDto>.Error("请选择要操作的隐患");
            }

            if (batchDto.DangerIds.Count > 50)
            {
                return AppResponse<MobileBatchOperationResultDto>.Error("单次最多操作50个隐患");
            }

            var allowedOperations = new[] { "delete", "export", "confirm", "rectify", "close" };
            if (!allowedOperations.Contains(batchDto.OperationType.ToLower()))
            {
                return AppResponse<MobileBatchOperationResultDto>.Error("不支持的操作类型");
            }

            // 转换DTO类型
            var batchOperation = new MobileBatchOperationDto
            {
                OperationType = batchDto.OperationType,
                TargetIds = batchDto.DangerIds
            };

            var result = await _hiddenDangerService.BatchOperationMobileHiddenDangersAsync(batchOperation);
            return AppResponse<MobileBatchOperationResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量操作隐患失败");
            return AppResponse<MobileBatchOperationResultDto>.Error("批量操作失败");
        }
    }

    /// <summary>
    /// 同步隐患数据到云端
    /// </summary>
    /// <param name="syncDto">同步数据</param>
    /// <returns>同步结果</returns>
    [HttpPost("sync")]
    public async Task<AppResponse<MobileSyncResultDto>> SyncHiddenDangerDataAsync([FromBody] MobileSyncDataDto syncDto)
    {
        try
        {
            var result = await _hiddenDangerService.SyncMobileHiddenDangerDataAsync(syncDto);
            return AppResponse<MobileSyncResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步隐患数据失败");
            return AppResponse<MobileSyncResultDto>.Error("数据同步失败");
        }
    }

    /// <summary>
    /// 导出隐患报告
    /// </summary>
    /// <param name="ids">隐患ID列表</param>
    /// <param name="format">导出格式（word/pdf/excel）</param>
    /// <returns>导出结果</returns>
    [HttpGet("export")]
    public async Task<AppResponse<MobileExportResultDto>> ExportHiddenDangerReportAsync([FromQuery] List<long> ids, [FromQuery] string format = "word")
    {
        try
        {
            if (ids == null || ids.Count == 0)
            {
                return AppResponse<MobileExportResultDto>.Error("请选择要导出的隐患");
            }

            if (ids.Count > 100)
            {
                return AppResponse<MobileExportResultDto>.Error("单次最多导出100个隐患");
            }

            if (!new[] { "word", "pdf", "excel" }.Contains(format.ToLower()))
            {
                return AppResponse<MobileExportResultDto>.Error("不支持的导出格式");
            }

            // 转换为DTO
            var exportDto = new MobileExportRequestDto
            {
                ExportType = "danger_report",
                ExportFormat = format,
                QueryCondition = new MobileDangerQueryDto
                {
                    // 这里可以根据需要设置查询条件
                }
            };

            var result = await _hiddenDangerService.ExportMobileHiddenDangerReportAsync(exportDto);
            return AppResponse<MobileExportResultDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出隐患报告失败，格式：{Format}", format);
            return AppResponse<MobileExportResultDto>.Error("导出报告失败");
        }
    }

    /// <summary>
    /// 获取隐患统计信息
    /// </summary>
    /// <param name="checkId">检查任务ID（可选）</param>
    /// <returns>统计信息</returns>
    [HttpGet("statistics")]
    public async Task<AppResponse<MobileHiddenDangerStatisticsDto>> GetHiddenDangerStatisticsAsync([FromQuery] long? checkId = null)
    {
        try
        {
            // 转换为DTO
            var queryDto = new MobileStatisticsQueryDto
            {
                ProjectId = checkId,
                StatisticsType = "overview"
            };

            var result = await _hiddenDangerService.GetMobileHiddenDangerStatisticsAsync(queryDto);
            return AppResponse<MobileHiddenDangerStatisticsDto>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取隐患统计信息失败");
            return AppResponse<MobileHiddenDangerStatisticsDto>.Error("获取统计信息失败");
        }
    }

    /// <summary>
    /// 获取隐患模板分类
    /// </summary>
    /// <returns>模板分类列表</returns>
    [HttpGet("templates")]
    public async Task<AppResponse<List<MobileDangerTemplateDto>>> GetDangerTemplatesAsync()
    {
        try
        {
            var result = await _hiddenDangerService.GetMobileDangerTemplatesAsync();
            return AppResponse<List<MobileDangerTemplateDto>>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取隐患模板失败");
            return AppResponse<List<MobileDangerTemplateDto>>.Error("获取模板失败");
        }
    }

    #region 私有方法

    private (bool IsValid, string ErrorMessage) ValidateCreateDangerData(MobileCreateHiddenDangerDto createDto)
    {
        if (createDto.ProjectCheckId <= 0)
            return (false, "请选择关联检查任务");

        if (string.IsNullOrWhiteSpace(createDto.DangerCategory))
            return (false, "隐患类别不能为空");

        if (string.IsNullOrWhiteSpace(createDto.ProblemLevel))
            return (false, "问题级别不能为空");

        if (string.IsNullOrWhiteSpace(createDto.ProblemDescription))
            return (false, "问题描述不能为空");

        if (string.IsNullOrWhiteSpace(createDto.LocationDescription))
            return (false, "现场位置不能为空");

        if (string.IsNullOrWhiteSpace(createDto.ResponsiblePerson))
            return (false, "责任人不能为空");

        if (createDto.RectificationDeadline <= DateTime.Now)
            return (false, "整改期限不能早于当前时间");

        return (true, string.Empty);
    }

    private (bool IsValid, string ErrorMessage) ValidateUpdateDangerData(MobileUpdateHiddenDangerDto updateDto)
    {
        if (updateDto.RectificationDeadline.HasValue && updateDto.RectificationDeadline <= DateTime.Now)
            return (false, "整改期限不能早于当前时间");

        return (true, string.Empty);
    }

    private (bool IsValid, string ErrorMessage) ValidateAiRecognitionData(MobileAiRecognitionRequestDto recognitionDto)
    {
        if (string.IsNullOrWhiteSpace(recognitionDto.RecognitionType))
            return (false, "识别类型不能为空");

        var validTypes = new[] { "photo", "audio", "mixed" };
        if (!validTypes.Contains(recognitionDto.RecognitionType.ToLower()))
            return (false, "不支持的识别类型");

        var hasPhoto = recognitionDto.PhotoFiles != null && recognitionDto.PhotoFiles.Count > 0;
        var hasAudio = recognitionDto.AudioFiles != null && recognitionDto.AudioFiles.Count > 0;

        if (!hasPhoto && !hasAudio)
            return (false, "请至少上传一个照片或语音文件");

        return (true, string.Empty);
    }

    private (bool IsValid, string ErrorMessage) ValidateMultimediaFiles(List<IFormFile>? photos, List<IFormFile>? audios)
    {
        const long maxFileSize = 10 * 1024 * 1024; // 10MB
        var supportedImageFormats = new[] { ".jpg", ".jpeg", ".png", ".webp" };
        var supportedAudioFormats = new[] { ".mp3", ".wav", ".m4a", ".aac" };

        // 验证照片文件
        if (photos != null)
        {
            foreach (var photo in photos)
            {
                if (photo.Length > maxFileSize)
                    return (false, $"照片文件 {photo.FileName} 大小超过10MB限制");

                var extension = Path.GetExtension(photo.FileName).ToLower();
                if (!supportedImageFormats.Contains(extension))
                    return (false, $"不支持的图片格式：{extension}");
            }
        }

        // 验证语音文件
        if (audios != null)
        {
            foreach (var audio in audios)
            {
                if (audio.Length > maxFileSize)
                    return (false, $"语音文件 {audio.FileName} 大小超过10MB限制");

                var extension = Path.GetExtension(audio.FileName).ToLower();
                if (!supportedAudioFormats.Contains(extension))
                    return (false, $"不支持的音频格式：{extension}");
            }
        }

        return (true, string.Empty);
    }

    private bool IsValidLocation(MobileLocationDto location)
    {
        return location.Latitude >= -90 && location.Latitude <= 90 &&
               location.Longitude >= -180 && location.Longitude <= 180 &&
               location.Accuracy > 0;
    }

    #endregion
}