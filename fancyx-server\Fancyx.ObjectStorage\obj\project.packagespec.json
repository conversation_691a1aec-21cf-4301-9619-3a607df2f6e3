﻿"restore":{"projectUniqueName":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\Fancyx.ObjectStorage.csproj","projectName":"Fancyx.ObjectStorage","projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\Fancyx.ObjectStorage.csproj","outputPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.ObjectStorage\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj":{"projectPath":"D:\\开发代码\\Code\\dotnet\\Ris-Sys\\fancyx-server\\Fancyx.Core\\Fancyx.Core.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Aliyun.OSS.SDK.NetCore":{"target":"Package","version":"[2.14.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}