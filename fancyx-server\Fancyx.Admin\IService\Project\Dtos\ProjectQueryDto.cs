using Fancyx.Shared.Models;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 项目查询条件 DTO
    /// </summary>
    public class ProjectQueryDto : PageSearch
    {
        /// <summary>
        /// 项目编号
        /// </summary>
        public string ProjectNo { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 所属区域
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// 项目类型
        /// </summary>
        public string ProjectType { get; set; }

        /// <summary>
        /// 工程类型
        /// </summary>
        public string EngineeringType { get; set; }

        /// <summary>
        /// 工程类别
        /// </summary>
        public string EngineeringCategory { get; set; }

        /// <summary>
        /// 建设单位
        /// </summary>
        public string ConstructionUnit { get; set; }

        /// <summary>
        /// 施工单位
        /// </summary>
        public string ConstructionCompany { get; set; }

        /// <summary>
        /// 现场负责人
        /// </summary>
        public string SiteManager { get; set; }
    }
}