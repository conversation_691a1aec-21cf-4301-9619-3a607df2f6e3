using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端创建项目DTO
/// </summary>
public class MobileCreateProjectDto
{
    /// <summary>
    /// 项目名称
    /// </summary>
    [Required(ErrorMessage = "项目名称不能为空")]
    [StringLength(200, ErrorMessage = "项目名称长度不能超过200字符")]
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// 项目地址
    /// </summary>
    [Required(ErrorMessage = "项目地址不能为空")]
    [StringLength(500, ErrorMessage = "项目地址长度不能超过500字符")]
    public string ProjectAddress { get; set; } = string.Empty;

    /// <summary>
    /// 所属区域（上海16区）
    /// </summary>
    [Required(ErrorMessage = "所属区域不能为空")]
    public string Region { get; set; } = string.Empty;

    /// <summary>
    /// 项目类型（生产安全/质量安全）
    /// </summary>
    [Required(ErrorMessage = "项目类型不能为空")]
    public string ProjectType { get; set; } = string.Empty;

    /// <summary>
    /// 工程类型
    /// </summary>
    [Required(ErrorMessage = "工程类型不能为空")]
    public string EngineeringType { get; set; } = string.Empty;

    /// <summary>
    /// 工程类别
    /// </summary>
    [Required(ErrorMessage = "工程类别不能为空")]
    public string EngineeringCategory { get; set; } = string.Empty;

    /// <summary>
    /// 现场负责人
    /// </summary>
    [Required(ErrorMessage = "现场负责人不能为空")]
    [StringLength(50, ErrorMessage = "负责人姓名长度不能超过50字符")]
    public string SiteManager { get; set; } = string.Empty;

    /// <summary>
    /// 负责人电话
    /// </summary>
    [Required(ErrorMessage = "负责人电话不能为空")]
    [Phone(ErrorMessage = "请输入正确的电话号码")]
    public string ManagerPhone { get; set; } = string.Empty;

    /// <summary>
    /// 负责人职位
    /// </summary>
    public string? ManagerPosition { get; set; }

    /// <summary>
    /// 建设单位
    /// </summary>
    [Required(ErrorMessage = "建设单位不能为空")]
    [StringLength(200, ErrorMessage = "建设单位名称长度不能超过200字符")]
    public string ConstructionUnit { get; set; } = string.Empty;

    /// <summary>
    /// 施工单位
    /// </summary>
    [Required(ErrorMessage = "施工单位不能为空")]
    [StringLength(200, ErrorMessage = "施工单位名称长度不能超过200字符")]
    public string ConstructionCompany { get; set; } = string.Empty;

    /// <summary>
    /// 监理单位
    /// </summary>
    public string? SupervisionUnit { get; set; }

    /// <summary>
    /// 设计单位
    /// </summary>
    public string? DesignUnit { get; set; }

    /// <summary>
    /// 施工许可证号
    /// </summary>
    public string? ConstructionPermitNo { get; set; }

    /// <summary>
    /// 关联合同
    /// </summary>
    public string? RelatedContract { get; set; }

    /// <summary>
    /// 开工日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 竣工日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 危化品从业人数
    /// </summary>
    public int? HazardousChemicalWorkers { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(1000, ErrorMessage = "备注长度不能超过1000字符")]
    public string? Remarks { get; set; }

    /// <summary>
    /// GPS位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 项目现场照片
    /// </summary>
    public List<string> SitePhotos { get; set; } = new();

    /// <summary>
    /// 创建时的设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端更新项目DTO
/// </summary>
public class MobileUpdateProjectDto
{
    /// <summary>
    /// 项目名称
    /// </summary>
    [StringLength(200, ErrorMessage = "项目名称长度不能超过200字符")]
    public string? ProjectName { get; set; }

    /// <summary>
    /// 项目地址
    /// </summary>
    [StringLength(500, ErrorMessage = "项目地址长度不能超过500字符")]
    public string? ProjectAddress { get; set; }

    /// <summary>
    /// 所属区域
    /// </summary>
    public string? Region { get; set; }

    /// <summary>
    /// 项目类型
    /// </summary>
    public string? ProjectType { get; set; }

    /// <summary>
    /// 工程类型
    /// </summary>
    public string? EngineeringType { get; set; }

    /// <summary>
    /// 工程类别
    /// </summary>
    public string? EngineeringCategory { get; set; }

    /// <summary>
    /// 现场负责人
    /// </summary>
    [StringLength(50, ErrorMessage = "负责人姓名长度不能超过50字符")]
    public string? SiteManager { get; set; }

    /// <summary>
    /// 负责人电话
    /// </summary>
    [Phone(ErrorMessage = "请输入正确的电话号码")]
    public string? ManagerPhone { get; set; }

    /// <summary>
    /// 负责人职位
    /// </summary>
    public string? ManagerPosition { get; set; }

    /// <summary>
    /// 建设单位
    /// </summary>
    [StringLength(200, ErrorMessage = "建设单位名称长度不能超过200字符")]
    public string? ConstructionUnit { get; set; }

    /// <summary>
    /// 施工单位
    /// </summary>
    [StringLength(200, ErrorMessage = "施工单位名称长度不能超过200字符")]
    public string? ConstructionCompany { get; set; }

    /// <summary>
    /// 监理单位
    /// </summary>
    public string? SupervisionUnit { get; set; }

    /// <summary>
    /// 设计单位
    /// </summary>
    public string? DesignUnit { get; set; }

    /// <summary>
    /// 施工许可证号
    /// </summary>
    public string? ConstructionPermitNo { get; set; }

    /// <summary>
    /// 关联合同
    /// </summary>
    public string? RelatedContract { get; set; }

    /// <summary>
    /// 开工日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 竣工日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 危化品从业人数
    /// </summary>
    public int? HazardousChemicalWorkers { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(1000, ErrorMessage = "备注长度不能超过1000字符")]
    public string? Remarks { get; set; }

    /// <summary>
    /// 更新时的GPS位置信息
    /// </summary>
    public MobileLocationDto? Location { get; set; }

    /// <summary>
    /// 新增的项目现场照片
    /// </summary>
    public List<string> NewSitePhotos { get; set; } = new();

    /// <summary>
    /// 要删除的现场照片URL
    /// </summary>
    public List<string> DeletedSitePhotos { get; set; } = new();

    /// <summary>
    /// 更新理由
    /// </summary>
    [StringLength(500, ErrorMessage = "更新理由长度不能超过500字符")]
    public string? UpdateReason { get; set; }

    /// <summary>
    /// 更新时的设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端批量项目操作DTO
/// </summary>
public class MobileBatchProjectOperationDto
{
    /// <summary>
    /// 项目ID列表
    /// </summary>
    [Required(ErrorMessage = "请选择要操作的项目")]
    public List<long> ProjectIds { get; set; } = new();

    /// <summary>
    /// 操作类型（delete/export/sync/updateStatus）
    /// </summary>
    [Required(ErrorMessage = "请选择操作类型")]
    public string OperationType { get; set; } = string.Empty;

    /// <summary>
    /// 操作参数（JSON格式）
    /// </summary>
    public string? OperationParams { get; set; }

    /// <summary>
    /// 操作原因
    /// </summary>
    [StringLength(500, ErrorMessage = "操作原因长度不能超过500字符")]
    public string? Reason { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端批量操作结果DTO
/// </summary>
public class MobileBatchOperationResultDto
{
    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// 成功的项目ID列表
    /// </summary>
    public List<long> SuccessIds { get; set; } = new();

    /// <summary>
    /// 失败的项目信息
    /// </summary>
    public List<MobileBatchErrorDto> Errors { get; set; } = new();

    /// <summary>
    /// 操作结果消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperationTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 导出文件URL（如果是导出操作）
    /// </summary>
    public string? ExportFileUrl { get; set; }
}

/// <summary>
/// 移动端批量操作错误DTO
/// </summary>
public class MobileBatchErrorDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long ProjectId { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string ProjectName { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;
}

/// <summary>
/// 移动端项目同步请求DTO
/// </summary>
public class MobileProjectSyncRequestDto
{
    /// <summary>
    /// 上次同步时间
    /// </summary>
    public DateTime? LastSyncTime { get; set; }

    /// <summary>
    /// 是否增量同步
    /// </summary>
    public bool IncrementalSync { get; set; } = true;

    /// <summary>
    /// 同步的项目ID列表（为空则同步全部）
    /// </summary>
    public List<long>? ProjectIds { get; set; }

    /// <summary>
    /// 客户端版本
    /// </summary>
    public string ClientVersion { get; set; } = string.Empty;

    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();
}

/// <summary>
/// 移动端项目同步结果DTO
/// </summary>
public class MobileProjectSyncResultDto
{
    /// <summary>
    /// 同步状态（success/partial/failed）
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 服务器时间戳
    /// </summary>
    public DateTime ServerTimestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 新增或更新的项目列表
    /// </summary>
    public List<MobileProjectListDto> UpdatedProjects { get; set; } = new();

    /// <summary>
    /// 已删除的项目ID列表
    /// </summary>
    public List<long> DeletedProjectIds { get; set; } = new();

    /// <summary>
    /// 同步数量统计
    /// </summary>
    public MobileSyncStatsDto SyncStats { get; set; } = new();

    /// <summary>
    /// 下次同步时间建议
    /// </summary>
    public DateTime NextSyncTime { get; set; }

    /// <summary>
    /// 同步错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// 移动端同步统计DTO
/// </summary>
public class MobileSyncStatsDto
{
    /// <summary>
    /// 新增数量
    /// </summary>
    public int AddedCount { get; set; }

    /// <summary>
    /// 更新数量
    /// </summary>
    public int UpdatedCount { get; set; }

    /// <summary>
    /// 删除数量
    /// </summary>
    public int DeletedCount { get; set; }

    /// <summary>
    /// 同步耗时（毫秒）
    /// </summary>
    public long SyncDuration { get; set; }
}