import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from '@/store';
import LoginPage from '../login';
import { AuthProvider } from '@/components/AuthProvider';

// Mock AuthProvider
jest.mock('@/components/AuthProvider', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuthProvider: () => ({
    pwdLogin: jest.fn(),
  }),
}));

const renderLoginPage = () => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <AuthProvider>
          <LoginPage />
        </AuthProvider>
      </BrowserRouter>
    </Provider>
  );
};

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe('LoginPage Responsive Design', () => {
  beforeEach(() => {
    // Reset viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 768,
    });
  });

  test('renders correctly on desktop (1024px+)', () => {
    renderLoginPage();
    
    const loginLayout = document.querySelector('.login-layout');
    const brandSection = document.querySelector('.login-bg-side');
    const formSection = document.querySelector('.login-form-side');
    
    expect(loginLayout).toBeInTheDocument();
    expect(brandSection).toBeInTheDocument();
    expect(formSection).toBeInTheDocument();
  });

  test('adapts layout for tablet (768px)', () => {
    // Simulate tablet viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });
    
    renderLoginPage();
    
    const loginCard = document.querySelector('.login-card');
    expect(loginCard).toBeInTheDocument();
  });

  test('adapts layout for mobile (480px)', () => {
    // Simulate mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 480,
    });
    
    renderLoginPage();
    
    const container = document.querySelector('.login-container');
    expect(container).toBeInTheDocument();
  });

  test('hides particles on mobile for performance', () => {
    // Simulate mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 480,
    });
    
    renderLoginPage();
    
    const particles = document.querySelectorAll('.ai-particle');
    expect(particles).toHaveLength(20); // Particles are still rendered but hidden via CSS
  });

  test('adjusts font sizes for different screen sizes', () => {
    renderLoginPage();
    
    const systemTitle = document.querySelector('.ai-system-title');
    const formTitle = document.querySelector('.ai-form-title');
    
    expect(systemTitle).toBeInTheDocument();
    expect(formTitle).toBeInTheDocument();
  });

  test('maintains touch-friendly button sizes on mobile', () => {
    // Simulate mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 480,
    });
    
    renderLoginPage();
    
    const loginButton = document.querySelector('.ai-login-button');
    expect(loginButton).toBeInTheDocument();
  });
});