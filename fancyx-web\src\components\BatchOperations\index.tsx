// @ts-nocheck
import React from 'react';
import { Button, Space, Dropdown, Popconfirm, message } from 'antd';
import { DeleteOutlined, ExportOutlined, MoreOutlined, FileExcelOutlined } from '@ant-design/icons';

interface BatchOperationsProps {
  selectedRowKeys: React.Key[];
  selectedRows: any[];
  onBatchDelete?: (ids: React.Key[]) => Promise<void>;
  onBatchExport?: (format: 'excel' | 'word' | 'pdf') => Promise<void>;
  onClearSelection?: () => void;
  customActions?: Array<{
    key: string;
    label: string;
    icon?: React.ReactNode;
    onClick: (ids: React.Key[], rows: any[]) => Promise<void>;
    danger?: boolean;
  }>;
  exportFormats?: Array<'excel' | 'word' | 'pdf'>;
  entityName?: string; // 实体名称，用于确认提示
}

const BatchOperations: React.FC<BatchOperationsProps> = ({
  selectedRowKeys,
  selectedRows,
  onBatchDelete,
  onBatchExport,
  onClearSelection,
  customActions = [],
  exportFormats = ['excel'],
  entityName = '记录'
}) => {
  const hasSelection = selectedRowKeys.length > 0;

  // 导出菜单项
  const exportMenuItems = exportFormats.map(format => {
    const icons = {
      excel: <FileExcelOutlined />,
      word: <FileWordOutlined />,
      pdf: <FilePdfOutlined />
    };
    
    const labels = {
      excel: 'Excel格式',
      word: 'Word格式', 
      pdf: 'PDF格式'
    };

    return {
      key: format,
      icon: icons[format],
      label: labels[format],
      onClick: () => onBatchExport?.(format)
    };
  });

  // 更多操作菜单项
  const moreMenuItems = customActions.map(action => ({
    key: action.key,
    icon: action.icon,
    label: action.label,
    danger: action.danger,
    onClick: () => action.onClick(selectedRowKeys, selectedRows)
  }));

  return (
    <div style={{ 
      marginBottom: 16, 
      padding: '8px 0',
      borderBottom: hasSelection ? '1px solid #f0f0f0' : 'none',
      background: hasSelection ? '#fafafa' : 'transparent',
      borderRadius: hasSelection ? '4px' : '0'
    }}>
      {hasSelection ? (
        <Space>
          <span style={{ color: '#666' }}>
            已选择 {selectedRowKeys.length} 项
          </span>
          
          {onBatchDelete && (
            <Popconfirm
              title={`确认删除选中的 ${selectedRowKeys.length} 个${entityName}吗？`}
              onConfirm={async () => {
                try {
                  await onBatchDelete(selectedRowKeys);
                  message.success(`成功删除 ${selectedRowKeys.length} 个${entityName}`);
                  onClearSelection?.();
                } catch (error) {
                  message.error('批量删除失败');
                }
              }}
              okText="确认"
              cancelText="取消"
            >
              <Button 
                icon={<DeleteOutlined />} 
                danger 
                size="small"
              >
                批量删除
              </Button>
            </Popconfirm>
          )}

          {onBatchExport && (
            <Dropdown.Button
              size="small"
              icon={<ExportOutlined />}
              menu={{ items: exportMenuItems }}
              onClick={() => onBatchExport(exportFormats[0])}
            >
              批量导出
            </Dropdown.Button>
          )}

          {moreMenuItems.length > 0 && (
            <Dropdown menu={{ items: moreMenuItems }}>
              <Button size="small" icon={<MoreOutlined />}>
                更多操作
              </Button>
            </Dropdown>
          )}

          <Button 
            size="small" 
            onClick={onClearSelection}
          >
            取消选择
          </Button>
        </Space>
      ) : (
        <div style={{ height: '32px' }} /> // 占位元素，保持布局稳定
      )}
    </div>
  );
};

export default BatchOperations;