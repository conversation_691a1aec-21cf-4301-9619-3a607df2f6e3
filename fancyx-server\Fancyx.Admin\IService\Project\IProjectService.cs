using Fancyx.Admin.IService.Project.Dtos;
using Fancyx.Admin.IService.Mobile.Dtos;
using Fancyx.Shared.Models;
using System.Threading.Tasks;

namespace Fancyx.Admin.IService.Project
{
    /// <summary>
    /// 项目管理服务接口
    /// </summary>
    public interface IProjectService
    {
        /// <summary>
        /// 获取项目分页列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<ProjectListDto>> GetProjectListAsync(ProjectQueryDto queryDto);

        /// <summary>
        /// 根据ID获取项目详情
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <returns>项目信息</returns>
        Task<ProjectDto?> GetProjectByIdAsync(long id);

        /// <summary>
        /// 创建项目
        /// </summary>
        /// <param name="createDto">创建信息</param>
        /// <returns>项目ID</returns>
        Task<long> CreateProjectAsync(CreateProjectDto createDto);

        /// <summary>
        /// 更新项目
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <param name="updateDto">更新信息</param>
        /// <returns>项目操作结果</returns>
        Task<ProjectOperationResult> UpdateProjectAsync(long id, UpdateProjectDto updateDto);

        /// <summary>
        /// 删除项目
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <returns>项目操作结果</returns>
        Task<ProjectOperationResult> DeleteProjectAsync(long id);

        /// <summary>
        /// 生成项目编号
        /// </summary>
        /// <returns>项目编号</returns>
        Task<string> GenerateProjectNoAsync();

        // ========== Mobile端专用方法 ==========

        /// <summary>
        /// 获取移动端项目列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>移动端项目列表</returns>
        Task<PagedResult<MobileProjectListDto>> GetMobileProjectListAsync(MobileProjectQueryDto queryDto);

        /// <summary>
        /// 获取移动端项目详情
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <returns>移动端项目详情</returns>
        Task<MobileProjectDetailDto?> GetMobileProjectDetailAsync(long id);

        /// <summary>
        /// 移动端创建项目
        /// </summary>
        /// <param name="createDto">创建信息</param>
        /// <returns>项目ID</returns>
        Task<long> CreateMobileProjectAsync(MobileCreateProjectDto createDto);

        /// <summary>
        /// 移动端更新项目
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <param name="updateDto">更新信息</param>
        /// <returns>操作结果</returns>
        Task<bool> UpdateMobileProjectAsync(long id, MobileUpdateProjectDto updateDto);

        /// <summary>
        /// 快速创建检查任务（移动端专用）
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <param name="createDto">创建参数</param>
        /// <returns>检查任务ID</returns>
        Task<long> QuickCreateCheckAsync(long projectId, MobileQuickCheckDto createDto);

        /// <summary>
        /// 检查项目是否有活跃的检查任务
        /// </summary>
        /// <param name="projectId">项目ID</param>
        /// <returns>是否有活跃检查</returns>
        Task<bool> HasActiveChecksAsync(long projectId);

        /// <summary>
        /// 删除移动端项目
        /// </summary>
        /// <param name="id">项目ID</param>
        /// <param name="deleteReason">删除原因</param>
        /// <returns>操作结果</returns>
        Task<bool> DeleteMobileProjectAsync(long id, string? deleteReason = null);

        /// <summary>
        /// 获取移动端项目统计信息
        /// </summary>
        /// <param name="projectId">项目ID（可选）</param>
        /// <returns>统计信息</returns>
        Task<MobileProjectStatisticsDto> GetMobileProjectStatisticsAsync(long? projectId = null);

        /// <summary>
        /// 批量操作移动端项目
        /// </summary>
        /// <param name="operationDto">批量操作参数</param>
        /// <returns>操作结果</returns>
        Task<MobileBatchOperationResultDto> BatchOperationMobileProjectsAsync(MobileBatchProjectOperationDto operationDto);

        /// <summary>
        /// 同步移动端项目数据
        /// </summary>
        /// <param name="syncDto">同步参数</param>
        /// <returns>同步结果</returns>
        Task<MobileProjectSyncResultDto> SyncMobileProjectsAsync(MobileProjectSyncRequestDto syncDto);
    }
}