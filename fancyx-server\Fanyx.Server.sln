﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34728.123
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{921ECD09-64BF-4A09-B45E-303C199761C4}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fancyx.Core", "Fancyx.Core\Fancyx.Core.csproj", "{59BFD2F8-06D0-4A28-83A1-8A8CA6842B37}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fancyx.Shared", "Fancyx.Shared\Fancyx.Shared.csproj", "{E3E6B4B3-2985-4506-8681-806264287BF0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fancyx.Admin", "Fancyx.Admin\Fancyx.Admin.csproj", "{C0FB332D-78AF-4230-9750-2914BE0406D1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fancyx.Repository", "Fancyx.Repository\Fancyx.Repository.csproj", "{979A8DFF-BAE5-49D7-A6F4-335974873E31}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fancyx.Redis", "Fancyx.Redis\Fancyx.Redis.csproj", "{5364A22C-7C01-4499-903D-1A985B11B30C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fancyx.Cap", "Fancyx.Cap\Fancyx.Cap.csproj", "{F6BFCC9E-2239-46CE-AAF5-F7D08B4F4C1D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fancyx.Logger", "Fancyx.Logger\Fancyx.Logger.csproj", "{AA0A1CD5-4F70-4323-91A3-CF9FAE5DA6E9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fancyx.ObjectStorage", "Fancyx.ObjectStorage\Fancyx.ObjectStorage.csproj", "{5A5DDF67-7FDD-4801-BF74-432B8DDF33F3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{59BFD2F8-06D0-4A28-83A1-8A8CA6842B37}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59BFD2F8-06D0-4A28-83A1-8A8CA6842B37}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59BFD2F8-06D0-4A28-83A1-8A8CA6842B37}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59BFD2F8-06D0-4A28-83A1-8A8CA6842B37}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3E6B4B3-2985-4506-8681-806264287BF0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3E6B4B3-2985-4506-8681-806264287BF0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3E6B4B3-2985-4506-8681-806264287BF0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3E6B4B3-2985-4506-8681-806264287BF0}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0FB332D-78AF-4230-9750-2914BE0406D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0FB332D-78AF-4230-9750-2914BE0406D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0FB332D-78AF-4230-9750-2914BE0406D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0FB332D-78AF-4230-9750-2914BE0406D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{979A8DFF-BAE5-49D7-A6F4-335974873E31}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{979A8DFF-BAE5-49D7-A6F4-335974873E31}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{979A8DFF-BAE5-49D7-A6F4-335974873E31}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{979A8DFF-BAE5-49D7-A6F4-335974873E31}.Release|Any CPU.Build.0 = Release|Any CPU
		{5364A22C-7C01-4499-903D-1A985B11B30C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5364A22C-7C01-4499-903D-1A985B11B30C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5364A22C-7C01-4499-903D-1A985B11B30C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5364A22C-7C01-4499-903D-1A985B11B30C}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6BFCC9E-2239-46CE-AAF5-F7D08B4F4C1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6BFCC9E-2239-46CE-AAF5-F7D08B4F4C1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6BFCC9E-2239-46CE-AAF5-F7D08B4F4C1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6BFCC9E-2239-46CE-AAF5-F7D08B4F4C1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA0A1CD5-4F70-4323-91A3-CF9FAE5DA6E9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA0A1CD5-4F70-4323-91A3-CF9FAE5DA6E9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA0A1CD5-4F70-4323-91A3-CF9FAE5DA6E9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA0A1CD5-4F70-4323-91A3-CF9FAE5DA6E9}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A5DDF67-7FDD-4801-BF74-432B8DDF33F3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A5DDF67-7FDD-4801-BF74-432B8DDF33F3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A5DDF67-7FDD-4801-BF74-432B8DDF33F3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A5DDF67-7FDD-4801-BF74-432B8DDF33F3}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {28315BFD-90E7-4E14-A2EA-F3D23AF4126F}
	EndGlobalSection
EndGlobal
