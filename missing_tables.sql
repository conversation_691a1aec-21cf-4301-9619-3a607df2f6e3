/*
 补充缺失表的创建脚本
 
 Source Server Type    : PostgreSQL
 Target Server Type    : PostgreSQL
 File Encoding         : 65001
 
 Date: 2025/07/24
*/

-- ----------------------------
-- Table structure for sys_project
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_project";
CREATE TABLE "public"."sys_project" (
  "id" int8 NOT NULL,
  "creator_id" uuid NOT NULL,
  "creation_time" timestamp(6) NOT NULL,
  "last_modification_time" timestamp(6),
  "last_modifier_id" uuid,
  "is_deleted" bool NOT NULL DEFAULT false,
  "deleter_id" uuid,
  "deletion_time" timestamp(6),
  "project_no" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "project_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "project_address" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
  "region" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "project_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "related_contract" varchar(100) COLLATE "pg_catalog"."default",
  "site_manager" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "manager_phone" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "manager_position" varchar(50) COLLATE "pg_catalog"."default",
  "start_date" timestamp(6),
  "end_date" timestamp(6),
  "project_status" int4 NOT NULL DEFAULT 1,
  "description" varchar(1000) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."sys_project"."id" IS '项目ID';
COMMENT ON COLUMN "public"."sys_project"."project_no" IS '项目编号';
COMMENT ON COLUMN "public"."sys_project"."project_name" IS '项目名称';
COMMENT ON COLUMN "public"."sys_project"."project_address" IS '项目地址';
COMMENT ON COLUMN "public"."sys_project"."region" IS '所属区域';
COMMENT ON COLUMN "public"."sys_project"."project_type" IS '项目类型（生产安全、质量安全）';
COMMENT ON COLUMN "public"."sys_project"."related_contract" IS '关联合同';
COMMENT ON COLUMN "public"."sys_project"."site_manager" IS '现场负责人';
COMMENT ON COLUMN "public"."sys_project"."manager_phone" IS '负责人电话';
COMMENT ON COLUMN "public"."sys_project"."manager_position" IS '负责人职务';
COMMENT ON COLUMN "public"."sys_project"."start_date" IS '开工日期';
COMMENT ON COLUMN "public"."sys_project"."end_date" IS '竣工日期';
COMMENT ON COLUMN "public"."sys_project"."project_status" IS '项目状态';
COMMENT ON COLUMN "public"."sys_project"."description" IS '项目描述';
COMMENT ON COLUMN "public"."sys_project"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."sys_project" IS '项目表';

-- ----------------------------
-- Table structure for sys_project_check
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_project_check";
CREATE TABLE "public"."sys_project_check" (
  "id" int8 NOT NULL,
  "creator_id" uuid NOT NULL,
  "creation_time" timestamp(6) NOT NULL,
  "last_modification_time" timestamp(6),
  "last_modifier_id" uuid,
  "is_deleted" bool NOT NULL DEFAULT false,
  "deleter_id" uuid,
  "deletion_time" timestamp(6),
  "project_id" int8 NOT NULL,
  "check_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "secondary_category" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "check_date" timestamp(6) NOT NULL,
  "client_unit" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "check_leader" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "check_members" varchar(500) COLLATE "pg_catalog"."default",
  "project_progress" varchar(100) COLLATE "pg_catalog"."default",
  "check_content" varchar(2000) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."sys_project_check"."id" IS '检查ID';
COMMENT ON COLUMN "public"."sys_project_check"."project_id" IS '项目ID';
COMMENT ON COLUMN "public"."sys_project_check"."check_type" IS '检查类型（生产安全、质量安全）';
COMMENT ON COLUMN "public"."sys_project_check"."secondary_category" IS '二级分类';
COMMENT ON COLUMN "public"."sys_project_check"."check_date" IS '检查日期';
COMMENT ON COLUMN "public"."sys_project_check"."client_unit" IS '委托单位';
COMMENT ON COLUMN "public"."sys_project_check"."check_leader" IS '检查组长';
COMMENT ON COLUMN "public"."sys_project_check"."check_members" IS '检查组员';
COMMENT ON COLUMN "public"."sys_project_check"."project_progress" IS '工程进度';
COMMENT ON COLUMN "public"."sys_project_check"."check_content" IS '检查内容';
COMMENT ON COLUMN "public"."sys_project_check"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."sys_project_check" IS '项目检查表';

-- ----------------------------
-- Table structure for sys_hidden_danger
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_hidden_danger";
CREATE TABLE "public"."sys_hidden_danger" (
  "id" uuid NOT NULL,
  "creator_id" uuid NOT NULL,
  "creation_time" timestamp(6) NOT NULL,
  "last_modification_time" timestamp(6),
  "last_modifier_id" uuid,
  "is_deleted" bool NOT NULL DEFAULT false,
  "deleter_id" uuid,
  "deletion_time" timestamp(6),
  "project_check_id" int8 NOT NULL,
  "danger_category" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "standard_id" int8,
  "standard_name" varchar(200) COLLATE "pg_catalog"."default",
  "problem_level" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "safety_hazard" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
  "regulation_clause" varchar(500) COLLATE "pg_catalog"."default",
  "description" varchar(2000) COLLATE "pg_catalog"."default" NOT NULL,
  "rectification_requirement" varchar(2000) COLLATE "pg_catalog"."default" NOT NULL,
  "possible_consequences" varchar(1000) COLLATE "pg_catalog"."default",
  "rectification_suggestion" varchar(500) COLLATE "pg_catalog"."default",
  "need_review" bool NOT NULL DEFAULT false,
  "photos" varchar(2000) COLLATE "pg_catalog"."default",
  "status" int4 NOT NULL DEFAULT 1,
  "rectification_deadline" timestamp(6),
  "rectification_person" varchar(100) COLLATE "pg_catalog"."default",
  "rectification_status" int4 NOT NULL DEFAULT 0,
  "review_status" int4 NOT NULL DEFAULT 0,
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."sys_hidden_danger"."id" IS '隐患ID';
COMMENT ON COLUMN "public"."sys_hidden_danger"."project_check_id" IS '检查ID';
COMMENT ON COLUMN "public"."sys_hidden_danger"."danger_category" IS '隐患大类';
COMMENT ON COLUMN "public"."sys_hidden_danger"."standard_id" IS '标准规范ID';
COMMENT ON COLUMN "public"."sys_hidden_danger"."standard_name" IS '标准规范名称';
COMMENT ON COLUMN "public"."sys_hidden_danger"."problem_level" IS '问题等级（一般、较大、重大）';
COMMENT ON COLUMN "public"."sys_hidden_danger"."safety_hazard" IS '安全隐患';
COMMENT ON COLUMN "public"."sys_hidden_danger"."regulation_clause" IS '法规条款';
COMMENT ON COLUMN "public"."sys_hidden_danger"."description" IS '具体描述';
COMMENT ON COLUMN "public"."sys_hidden_danger"."rectification_requirement" IS '整改要求';
COMMENT ON COLUMN "public"."sys_hidden_danger"."possible_consequences" IS '可能产生后果';
COMMENT ON COLUMN "public"."sys_hidden_danger"."rectification_suggestion" IS '整改建议';
COMMENT ON COLUMN "public"."sys_hidden_danger"."need_review" IS '是否需要复查';
COMMENT ON COLUMN "public"."sys_hidden_danger"."photos" IS '现场照片（JSON格式）';
COMMENT ON COLUMN "public"."sys_hidden_danger"."status" IS '隐患状态';
COMMENT ON COLUMN "public"."sys_hidden_danger"."rectification_deadline" IS '整改期限';
COMMENT ON COLUMN "public"."sys_hidden_danger"."rectification_person" IS '整改责任人';
COMMENT ON COLUMN "public"."sys_hidden_danger"."rectification_status" IS '整改状态';
COMMENT ON COLUMN "public"."sys_hidden_danger"."review_status" IS '复查状态';
COMMENT ON COLUMN "public"."sys_hidden_danger"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."sys_hidden_danger" IS '隐患表';

-- ----------------------------
-- Table structure for sys_ai_platform_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_ai_platform_config";
CREATE TABLE "public"."sys_ai_platform_config" (
  "id" uuid NOT NULL,
  "creator_id" uuid NOT NULL,
  "creation_time" timestamp(6) NOT NULL,
  "last_modification_time" timestamp(6),
  "last_modifier_id" uuid,
  "is_deleted" bool NOT NULL DEFAULT false,
  "deleter_id" uuid,
  "deletion_time" timestamp(6),
  "platform_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "api_url" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
  "api_key" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
  "model_name" varchar(100) COLLATE "pg_catalog"."default",
  "timeout" int4 NOT NULL DEFAULT 30,
  "status" int4 NOT NULL DEFAULT 0,
  "remarks" varchar(1000) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."sys_ai_platform_config"."id" IS 'AI平台配置ID';
COMMENT ON COLUMN "public"."sys_ai_platform_config"."platform_name" IS '平台名称';
COMMENT ON COLUMN "public"."sys_ai_platform_config"."api_url" IS 'API地址';
COMMENT ON COLUMN "public"."sys_ai_platform_config"."api_key" IS 'API Key';
COMMENT ON COLUMN "public"."sys_ai_platform_config"."model_name" IS '模型名称';
COMMENT ON COLUMN "public"."sys_ai_platform_config"."timeout" IS '请求超时时间（秒）';
COMMENT ON COLUMN "public"."sys_ai_platform_config"."status" IS '状态（0启用，1停用）';
COMMENT ON COLUMN "public"."sys_ai_platform_config"."remarks" IS '备注';
COMMENT ON COLUMN "public"."sys_ai_platform_config"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."sys_ai_platform_config" IS 'AI平台配置表';

-- ----------------------------
-- Table structure for sys_standard
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_standard";
CREATE TABLE "public"."sys_standard" (
  "id" uuid NOT NULL,
  "creator_id" uuid NOT NULL,
  "creation_time" timestamp(6) NOT NULL,
  "last_modification_time" timestamp(6),
  "last_modifier_id" uuid,
  "is_deleted" bool NOT NULL DEFAULT false,
  "deleter_id" uuid,
  "deletion_time" timestamp(6),
  "name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "danger_category" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "regulation_clause" varchar(500) COLLATE "pg_catalog"."default",
  "requirements" varchar(2000) COLLATE "pg_catalog"."default",
  "status" int4 NOT NULL DEFAULT 0,
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."sys_standard"."id" IS '标准ID';
COMMENT ON COLUMN "public"."sys_standard"."name" IS '标准名称';
COMMENT ON COLUMN "public"."sys_standard"."type" IS '标准类型';
COMMENT ON COLUMN "public"."sys_standard"."danger_category" IS '隐患大类';
COMMENT ON COLUMN "public"."sys_standard"."regulation_clause" IS '法规条款';
COMMENT ON COLUMN "public"."sys_standard"."requirements" IS '具体要求';
COMMENT ON COLUMN "public"."sys_standard"."status" IS '状态（0启用，1停用）';
COMMENT ON COLUMN "public"."sys_standard"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."sys_standard" IS '标准规范表';

-- ----------------------------
-- Table structure for sys_business_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_business_log";
CREATE TABLE "public"."sys_business_log" (
  "id" uuid NOT NULL,
  "creator_id" uuid NOT NULL,
  "creation_time" timestamp(6) NOT NULL,
  "user_name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "action" varchar(512) COLLATE "pg_catalog"."default" NOT NULL,
  "http_method" varchar(16) COLLATE "pg_catalog"."default" NOT NULL,
  "url" varchar(512) COLLATE "pg_catalog"."default" NOT NULL,
  "os" varchar(64) COLLATE "pg_catalog"."default",
  "browser" varchar(64) COLLATE "pg_catalog"."default",
  "node_name" varchar(128) COLLATE "pg_catalog"."default" NOT NULL,
  "ip" varchar(32) COLLATE "pg_catalog"."default",
  "address" varchar(256) COLLATE "pg_catalog"."default",
  "is_success" bool NOT NULL DEFAULT true,
  "operation_msg" varchar(128) COLLATE "pg_catalog"."default",
  "mill_seconds" int4 NOT NULL DEFAULT 0,
  "request_id" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."sys_business_log"."id" IS '业务日志ID';
COMMENT ON COLUMN "public"."sys_business_log"."user_name" IS '账号';
COMMENT ON COLUMN "public"."sys_business_log"."action" IS '操作方法，全名';
COMMENT ON COLUMN "public"."sys_business_log"."http_method" IS 'HTTP请求方式';
COMMENT ON COLUMN "public"."sys_business_log"."url" IS '请求地址';
COMMENT ON COLUMN "public"."sys_business_log"."os" IS '系统';
COMMENT ON COLUMN "public"."sys_business_log"."browser" IS '浏览器';
COMMENT ON COLUMN "public"."sys_business_log"."node_name" IS '操作节点名';
COMMENT ON COLUMN "public"."sys_business_log"."ip" IS 'IP';
COMMENT ON COLUMN "public"."sys_business_log"."address" IS '登录地址';
COMMENT ON COLUMN "public"."sys_business_log"."is_success" IS '是否成功';
COMMENT ON COLUMN "public"."sys_business_log"."operation_msg" IS '操作信息';
COMMENT ON COLUMN "public"."sys_business_log"."mill_seconds" IS '耗时，单位毫秒';
COMMENT ON COLUMN "public"."sys_business_log"."request_id" IS '请求跟踪ID';
COMMENT ON COLUMN "public"."sys_business_log"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."sys_business_log" IS '业务日志表';

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_role_dept";
CREATE TABLE "public"."sys_role_dept" (
  "id" uuid NOT NULL,
  "role_id" uuid NOT NULL,
  "dept_id" uuid NOT NULL,
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."sys_role_dept"."id" IS '关联ID';
COMMENT ON COLUMN "public"."sys_role_dept"."role_id" IS '角色ID';
COMMENT ON COLUMN "public"."sys_role_dept"."dept_id" IS '部门ID';
COMMENT ON COLUMN "public"."sys_role_dept"."tenant_id" IS '租户ID';
COMMENT ON TABLE "public"."sys_role_dept" IS '角色部门关联表';

-- ----------------------------
-- Table structure for sys_tenant
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_tenant";
CREATE TABLE "public"."sys_tenant" (
  "id" uuid NOT NULL,
  "creator_id" uuid NOT NULL,
  "creation_time" timestamp(6) NOT NULL,
  "last_modification_time" timestamp(6),
  "last_modifier_id" uuid,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "tenant_id" varchar(18) COLLATE "pg_catalog"."default" NOT NULL,
  "remark" varchar(512) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."sys_tenant"."id" IS '租户主键ID';
COMMENT ON COLUMN "public"."sys_tenant"."name" IS '租户名称';
COMMENT ON COLUMN "public"."sys_tenant"."tenant_id" IS '租户标识';
COMMENT ON COLUMN "public"."sys_tenant"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_tenant" IS '租户表';

-- ----------------------------
-- Primary Key structure for table sys_project
-- ----------------------------
ALTER TABLE "public"."sys_project" ADD CONSTRAINT "public_sys_project_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_project_check
-- ----------------------------
ALTER TABLE "public"."sys_project_check" ADD CONSTRAINT "public_sys_project_check_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_hidden_danger
-- ----------------------------
ALTER TABLE "public"."sys_hidden_danger" ADD CONSTRAINT "public_sys_hidden_danger_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_ai_platform_config
-- ----------------------------
ALTER TABLE "public"."sys_ai_platform_config" ADD CONSTRAINT "public_sys_ai_platform_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_standard
-- ----------------------------
ALTER TABLE "public"."sys_standard" ADD CONSTRAINT "public_sys_standard_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_business_log
-- ----------------------------
ALTER TABLE "public"."sys_business_log" ADD CONSTRAINT "public_sys_business_log_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_role_dept
-- ----------------------------
ALTER TABLE "public"."sys_role_dept" ADD CONSTRAINT "public_sys_role_dept_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_tenant
-- ----------------------------
ALTER TABLE "public"."sys_tenant" ADD CONSTRAINT "public_sys_tenant_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes for table sys_tenant
-- ----------------------------
CREATE UNIQUE INDEX "uk_tenant_id" ON "public"."sys_tenant" USING btree (
  "tenant_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Foreign Key constraints (optional - uncomment if needed)
-- ----------------------------
-- ALTER TABLE "public"."sys_project_check" ADD CONSTRAINT "fk_project_check_project" FOREIGN KEY ("project_id") REFERENCES "public"."sys_project" ("id");
-- ALTER TABLE "public"."sys_hidden_danger" ADD CONSTRAINT "fk_hidden_danger_project_check" FOREIGN KEY ("project_check_id") REFERENCES "public"."sys_project_check" ("id");
-- ALTER TABLE "public"."sys_hidden_danger" ADD CONSTRAINT "fk_hidden_danger_standard" FOREIGN KEY ("standard_id") REFERENCES "public"."sys_standard" ("id");
-- ALTER TABLE "public"."sys_role_dept" ADD CONSTRAINT "fk_role_dept_role" FOREIGN KEY ("role_id") REFERENCES "public"."sys_role" ("id");
-- ALTER TABLE "public"."sys_role_dept" ADD CONSTRAINT "fk_role_dept_dept" FOREIGN KEY ("dept_id") REFERENCES "public"."sys_dept" ("id");
