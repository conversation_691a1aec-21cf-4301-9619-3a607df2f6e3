import { useRoutes } from 'react-router-dom';
import { routes } from '@/router';
import zhCN from 'antd/locale/zh_CN';
import { ConfigProvider, Spin, theme } from 'antd';
import { Suspense, useMemo } from 'react';
import { generateDynamicRoutes } from './router/dynamic';
import UserStore from './store/userStore';
import { useSelector } from 'react-redux';
import { selectSize } from '@/store/themeStore.ts';
import { AuthProvider } from '@/components/AuthProvider';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Application from '@/components/Application';
import { observer } from 'mobx-react-lite';

// 创建 QueryClient 单例，避免每次渲染都创建新实例
const queryClient = new QueryClient();

// 清爽紫色主题配置
const defaultTheme = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: '#7E57C2',
    colorLink: '#7E57C2',
    colorSuccess: '#66BB6A',
    colorWarning: '#FFA726',
    colorError: '#FF7043',
    colorBgBase: '#FFFFFF',
    colorTextBase: '#4A4A4A',
    borderRadius: 4,
    fontSize: 14,
  },
  components: {
    Layout: {
      siderBg: '#FFFFFF',
      headerBg: '#FFFFFF',
    },
    Menu: {
      itemBg: '#FFFFFF',
      itemColor: '#4A4A4A',
      itemSelectedBg: '#EDE7F6',
      itemSelectedColor: '#7E57C2',
      itemHoverBg: '#F5F3FF',
      itemBorderRadius: 8,
    },
    Button: {
      colorPrimary: '#7E57C2',
      colorPrimaryHover: '#9575CD',
      colorPrimaryActive: '#673AB7',
    },
    Table: {
      headerBg: '#F5F3FF',
      headerColor: '#4A4A4A',
      borderColor: '#EDE7F6',
    },
  },
};

const App = observer(() => {
  const size = useSelector(selectSize);
  
  // 使用 useMemo 缓存路由生成结果，避免无限循环
  const renderRoutes = useMemo(() => {
    let finalRoutes = [...routes];

    if (UserStore.isAuthenticated()) {
      const menus = UserStore.userInfo?.menus;
      if (Array.isArray(menus) && menus.length > 0) {
        const dyRoutes = generateDynamicRoutes(menus);
        
        finalRoutes = [
          routes[0], // login route
          {
            ...routes[1],
            children: [
              ...(routes[1].children || []),
              ...dyRoutes
            ]
          }
        ];
      }
    }
    
    return finalRoutes;
  }, [UserStore.isAuthenticated(), UserStore.userInfo?.menus]);

  const fallback = (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}
    >
      <Spin />
    </div>
  );

  return (
    <>
      <AuthProvider>
        <ConfigProvider locale={zhCN} componentSize={size} theme={defaultTheme}>
          <Application>
            <QueryClientProvider client={queryClient}>
              <Suspense fallback={fallback}>{useRoutes(renderRoutes)}</Suspense>
            </QueryClientProvider>
          </Application>
        </ConfigProvider>
      </AuthProvider>
    </>
  );
});

export default App;
