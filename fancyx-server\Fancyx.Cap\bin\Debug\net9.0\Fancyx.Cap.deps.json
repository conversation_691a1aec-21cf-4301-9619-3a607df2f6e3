{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Fancyx.Cap/1.0.0": {"dependencies": {"DotNetCore.CAP": "8.3.4", "DotNetCore.CAP.Dashboard": "8.3.4", "DotNetCore.CAP.PostgreSql": "8.3.4", "DotNetCore.CAP.RedisStreams": "8.3.4", "Fancyx.Core": "1.0.0"}, "runtime": {"Fancyx.Cap.dll": {}}}, "Autofac/8.2.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net8.0/Autofac.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.0.0"}}}, "Autofac.Extensions.DependencyInjection/10.0.0": {"dependencies": {"Autofac": "8.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.0.0"}}}, "Autofac.Extras.DynamicProxy/7.1.0": {"dependencies": {"Autofac": "8.2.0", "Castle.Core": "5.2.1"}, "runtime": {"lib/net6.0/Autofac.Extras.DynamicProxy.dll": {"assemblyVersion": "7.1.0.0", "fileVersion": "7.1.0.0"}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "Castle.Core/5.2.1": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"dependencies": {"Castle.Core": "5.2.1"}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Consul/********": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Consul.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "DotNetCore.CAP/8.3.4": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/DotNetCore.CAP.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP.Dashboard/8.3.4": {"dependencies": {"Consul": "********", "DotNetCore.CAP": "8.3.4"}, "runtime": {"lib/net6.0/DotNetCore.CAP.Dashboard.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP.PostgreSql/8.3.4": {"dependencies": {"DotNetCore.CAP": "8.3.4", "Microsoft.EntityFrameworkCore.Relational": "6.0.36", "Npgsql": "8.0.5"}, "runtime": {"lib/net6.0/DotNetCore.CAP.PostgreSql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DotNetCore.CAP.RedisStreams/8.3.4": {"dependencies": {"DotNetCore.CAP": "8.3.4", "StackExchange.Redis": "2.8.16"}, "runtime": {"lib/net8.0/DotNetCore.CAP.RedisStreams.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "IP2Region.Net/2.0.2": {"runtime": {"lib/net7.0/IP2Region.Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.6": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.624.26909"}}}, "Microsoft.AspNetCore.JsonPatch/8.0.14": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1425.11221"}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.14": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.14", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1425.11221"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.36", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.36", "Microsoft.Extensions.Caching.Memory": "6.0.3", "Microsoft.Extensions.DependencyInjection": "6.0.2", "Microsoft.Extensions.Logging": "6.0.1", "System.Collections.Immutable": "6.0.1", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.36": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.36": {}, "Microsoft.EntityFrameworkCore.Relational/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.36", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.Extensions.Caching.Abstractions/6.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/6.0.3": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/6.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {}, "Microsoft.Extensions.DependencyModel/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "Npgsql/8.0.5": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "StackExchange.Redis/2.8.16": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.8.16.12844"}}}, "System.Collections.Immutable/6.0.1": {}, "System.Diagnostics.DiagnosticSource/8.0.1": {}, "System.Diagnostics.EventLog/6.0.0": {}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "System.IO.Pipelines/5.0.1": {}, "UAParser/3.1.47": {"runtime": {"lib/netcoreapp2.0/UAParser.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Fancyx.Core/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "Autofac": "8.2.0", "Autofac.Extensions.DependencyInjection": "10.0.0", "Autofac.Extras.DynamicProxy": "7.1.0", "Castle.Core": "5.2.1", "Castle.Core.AsyncInterceptor": "2.1.0", "IP2Region.Net": "2.0.2", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.6", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.14", "Microsoft.Extensions.DependencyModel": "9.0.3", "Newtonsoft.Json": "13.0.3", "UAParser": "3.1.47"}, "runtime": {"Fancyx.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Fancyx.Cap/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Autofac/8.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-T+4+W4byzyUOarCbIcFRbxpYKC+cndfQm/+VeWpB60P2MCN0JMsewUhZqvH5Ooe936HQjn5uHvEY9tq6BfbiIw==", "path": "autofac/8.2.0", "hashPath": "autofac.8.2.0.nupkg.sha512"}, "Autofac.Extensions.DependencyInjection/10.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZjR/onUlP7BzQ7VBBigQepWLAyAzi3VRGX3pP6sBqkPRiT61fsTZqbTpRUKxo30TMgbs1o3y6bpLbETix4SJog==", "path": "autofac.extensions.dependencyinjection/10.0.0", "hashPath": "autofac.extensions.dependencyinjection.10.0.0.nupkg.sha512"}, "Autofac.Extras.DynamicProxy/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Da6Szv7A1LK/cTdeoyqS45zb/BC5vep8+86f6C1oh2UhZaYtiijlNfLWamp3lxe0uUQ33kFe1dDCjsvfwJWzLg==", "path": "autofac.extras.dynamicproxy/7.1.0", "hashPath": "autofac.extras.dynamicproxy.7.1.0.nupkg.sha512"}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "Castle.Core/5.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-wHARzQA695jwwKreOzNsq54KiGqKP38tv8hi8e2FXDEC/sA6BtrX90tVPDkOfVu13PbEzr00TCV8coikl+D1Iw==", "path": "castle.core/5.2.1", "hashPath": "castle.core.5.2.1.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "path": "castle.core.asyncinterceptor/2.1.0", "hashPath": "castle.core.asyncinterceptor.2.1.0.nupkg.sha512"}, "Consul/********": {"type": "package", "serviceable": true, "sha512": "sha512-0+vCXo9I0sSJJfFafBN1VGtu/Ya1Sw26fmmGDX3TdYZRq7ScXjZC8tvMIcrxNpxwyWN+e7sxn/aZ9XiZHS4LCw==", "path": "consul/********", "hashPath": "consul.********.nupkg.sha512"}, "DotNetCore.CAP/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-e6Nhb+zsCjui43ssSzg0zbuHKCL8CVpHUj6ZHaRpngPZ9hE9nytIInFLfkvfgCFB24VPTDX0wdaM1+HJWFpAAA==", "path": "dotnetcore.cap/8.3.4", "hashPath": "dotnetcore.cap.8.3.4.nupkg.sha512"}, "DotNetCore.CAP.Dashboard/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-LBR3ea5D7vwmZ9d2iii4ZtVa/1X+BOCM5kyWXwdK0bHknPCf0VPvVRR5+7VbxQNK4/F1yw67I11A2Ea6Zsn2Ng==", "path": "dotnetcore.cap.dashboard/8.3.4", "hashPath": "dotnetcore.cap.dashboard.8.3.4.nupkg.sha512"}, "DotNetCore.CAP.PostgreSql/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-m1ha4L8LfDEmTvRxPmbQ4umwdoHb+BUY9NkFPNuRhXXxHf6m1ZAwkLyekLO1aO+/MaxkbRLjdai7oXKLO0KLTA==", "path": "dotnetcore.cap.postgresql/8.3.4", "hashPath": "dotnetcore.cap.postgresql.8.3.4.nupkg.sha512"}, "DotNetCore.CAP.RedisStreams/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-Gx7BcpBU+RYpKxzJf6Od7ZBSG0PSCcLOKABHwUAlaiwzDj9WVeX9HA0YDZQhg+rHP6tz17jq3b5x6QOUsbk+EA==", "path": "dotnetcore.cap.redisstreams/8.3.4", "hashPath": "dotnetcore.cap.redisstreams.8.3.4.nupkg.sha512"}, "IP2Region.Net/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-vkxITIPkUdju0+vPm9FYU4+sBvvOkoM7GvPw8R81RYlbkKDwKAkO9P4vZvzeRJEC8SrYtMLcsDJizx+e/JnRmA==", "path": "ip2region.net/2.0.2", "hashPath": "ip2region.net.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-devoUZd8YqikCheBVYpIyvp9psM2Y2ZhOvq1zL2YSjIoq3FUQH8LpLkGak+8oAi/5DGqX8KWyLpZJSUXtOAVCw==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.6", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/8.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-S0ZPiwLvRCYWYz7WTxaTZIyBEYOTPR+Rujdiwz4gUGauWpAhXu846I29xjOTBPX1d5HUcBNlJ46cgDDAev94Gg==", "path": "microsoft.aspnetcore.jsonpatch/8.0.14", "hashPath": "microsoft.aspnetcore.jsonpatch.8.0.14.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-322+FAPeMslfd320h8sASJSJ3h5s4dVr6W0xypr28Vz/cbMFjVCLHOou418cc2Xh0kXRzMy3irD3jBzSA+EiDA==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/8.0.14", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.8.0.14.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-0i1BBvJBrqdmIdTqCmL+/J74HucYqc5eck3J5trKe6AN2fvdE1lICto6HBwNhbtPniZO7bhW36FnIjTK0FamXg==", "path": "microsoft.entityframeworkcore/6.0.36", "hashPath": "microsoft.entityframeworkcore.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-nrR4lfe9izQK1eerKW/ECHuJV8xXtuvoj/APrwwOjX4+Ne2SMXBpetctPcYNVc3KyiKuUHJSLywWtsqoXE5ElA==", "path": "microsoft.entityframeworkcore.abstractions/6.0.36", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON>wloBaAJcLwgSW4hvGdAWjKnuIwgQ2PKTNkvG80PW/WFgedwKomY9wuO5BPewIHlX6huGyP//StQQRQOWr+Q==", "path": "microsoft.entityframeworkcore.analyzers/6.0.36", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-Vq9UQEFNU1t42AOSgz6bUe0fpMa1g4wO8Y7EfhYPX6VSriRSRB4ImTC2TEZjOUeyGcZyUI2Kyd6//RfUPUR+Pw==", "path": "microsoft.entityframeworkcore.relational/6.0.36", "hashPath": "microsoft.entityframeworkcore.relational.6.0.36.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-zEtKmOJ3sZ9YxRgvgLtoQsqO069Kqp0aC6Ai+DkyE5uahtu1ynvuoDVFQgyyhVcJWnxCzZHax/3AodvAx2mhjA==", "path": "microsoft.extensions.caching.abstractions/6.0.1", "hashPath": "microsoft.extensions.caching.abstractions.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-GVNNcHoPDEUn4OQiBBGs5mE6nX7BA+LeQId9NeA+gB8xcbDUmFPAl8Er2ixNLbn4ffFr8t5jfMwdxgFG66k7BA==", "path": "microsoft.extensions.caching.memory/6.0.3", "hashPath": "microsoft.extensions.caching.memory.6.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-gWUfUZ2ZDvwiVCxsOMComAhG43xstNWWVjV2takUZYRuDSJjO9Q5/b3tfOSkl5mcVwZAL3RZviRj5ZilxHghlw==", "path": "microsoft.extensions.dependencyinjection/6.0.2", "hashPath": "microsoft.extensions.dependencyinjection.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-194P+NOekDXrPHmM2R8678T4bRfZ2isQXDDAsXKE5qI0QLUnXbwB0upljAkyxk+Kkt1DV1tV+9tHOtHEEh3ksw==", "path": "microsoft.extensions.dependencymodel/9.0.3", "hashPath": "microsoft.extensions.dependencymodel.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-k6tbYaHrqY9kq7p5FfpPbddY1OImPCpXQ/PGcED6N9s5ULRp8n1PdmMzsIwIzCnhIS5bs06G/lO9LfNVpUj8jg==", "path": "microsoft.extensions.logging/6.0.1", "hashPath": "microsoft.extensions.logging.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "path": "microsoft.identitymodel.abstractions/7.1.2", "hashPath": "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "path": "microsoft.identitymodel.logging/7.1.2", "hashPath": "microsoft.identitymodel.logging.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "path": "microsoft.identitymodel.tokens/7.1.2", "hashPath": "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Npgsql/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-zRG5V8cyeZLpzJlKzFKjEwkRMYIYnHWJvEor2lWXeccS2E1G2nIWYYhnukB51iz5XsWSVEtqg3AxTWM0QJ6vfg==", "path": "npgsql/8.0.5", "hashPath": "npgsql.8.0.5.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "StackExchange.Redis/2.8.16": {"type": "package", "serviceable": true, "sha512": "sha512-WaoulkOqOC9jHepca3JZKFTqndCWab5uYS7qCzmiQDlrTkFaDN7eLSlEfHycBxipRnQY9ppZM7QSsWAwUEGblw==", "path": "stackexchange.redis/2.8.16", "hashPath": "stackexchange.redis.2.8.16.nupkg.sha512"}, "System.Collections.Immutable/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Cc+SfP+jyCmA1TnRnDA7cRZy5jbLyWodfuUJKDJ+PJZBwWkv8szz+ztSCHAonqnL01DRaHaS2ptc4bYIEdgvWw==", "path": "system.collections.immutable/6.0.1", "hashPath": "system.collections.immutable.6.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "path": "system.identitymodel.tokens.jwt/7.1.2", "hashPath": "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "UAParser/3.1.47": {"type": "package", "serviceable": true, "sha512": "sha512-I68Jl/Vs5RQZdz9BbmYtnXgujg0jVd61LhKbyNZOCm9lBxZFGxLbiQo6yFj21VYi7DzPvEvrVOmeC6v41AoLfw==", "path": "uaparser/3.1.47", "hashPath": "uaparser.3.1.47.nupkg.sha512"}, "Fancyx.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}