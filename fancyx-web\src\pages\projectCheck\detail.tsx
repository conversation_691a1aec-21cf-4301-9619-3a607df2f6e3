// @ts-nocheck
import React, { useEffect, useState, useRef } from 'react';
import { Descriptions, Button, Card, Table, Modal, Upload, message, Space, Dropdown, Statistic, Tag } from 'antd';
import { 
  CameraOutlined, PlusOutlined, EditOutlined, DeleteOutlined, 
  FileWordOutlined, FilePdfOutlined, DownloadOutlined, BarChartOutlined 
} from '@ant-design/icons';
import { getProjectCheckById } from '@/api/projectCheck';
import { getHiddenDangersByCheckId, deleteHiddenDanger, confirmHiddenDanger, aiRecognitionHiddenDanger } from '@/api/hiddenDanger';
import { useParams, useNavigate } from 'react-router-dom';

const ProjectCheckDetail: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [checkData, setCheckData] = useState<any>({});
  const [hiddenDangers, setHiddenDangers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [photoModalVisible, setPhotoModalVisible] = useState(false);
  const [uploading, setUploading] = useState(false);

  // 获取检查任务详情
  const fetchCheckDetail = async () => {
    if (id) {
      const res = await getProjectCheckById(Number(id));
      setCheckData(res.data || {});
    }
  };

  // 获取隐患明细列表
  const fetchHiddenDangers = async () => {
    if (id) {
      setLoading(true);
      const res = await getHiddenDangersByCheckId(id);
      setHiddenDangers(res.data || []);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCheckDetail();
    fetchHiddenDangers();
  }, [id]);

  // 添加隐患明细
  const handleAddHiddenDanger = () => {
    navigate(`/hidden-danger/edit?projectCheckId=${id}&checkInfo=${encodeURIComponent(checkData.projectName + ' - ' + checkData.checkType)}`);
  };

  // 编辑隐患明细
  const handleEditHiddenDanger = (record: any) => {
    navigate(`/hidden-danger/edit/${record.id}`);
  };

  // 删除隐患明细
  const handleDeleteHiddenDanger = (record: any) => {
    Modal.confirm({
      title: '确认删除该隐患明细？',
      onOk: async () => {
        await deleteHiddenDanger(record.id);
        message.success('删除成功');
        fetchHiddenDangers();
      },
    });
  };

  // 确认隐患明细
  const handleConfirmHiddenDanger = (record: any) => {
    Modal.confirm({
      title: '确认该隐患明细？',
      onOk: async () => {
        await confirmHiddenDanger(record.id);
        message.success('确认成功');
        fetchHiddenDangers();
      },
    });
  };

  // 拍照生成隐患明细
  const handlePhotoUpload = async (file: any) => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('photo', file);
      formData.append('projectCheckId', id);
      
      // 调用后端AI识别接口
      await aiRecognitionHiddenDanger(formData);
      
      message.success('照片上传成功，正在生成隐患明细...');
      setPhotoModalVisible(false);
      
      // 刷新隐患明细列表
      setTimeout(() => {
        fetchHiddenDangers();
        message.success('隐患明细生成完成');
      }, 2000);
    } catch (error) {
      message.error('照片处理失败，请重试');
    } finally {
      setUploading(false);
    }
    return false; // 阻止默认上传行为
  };

  // 生成检查报告
  const handleGenerateReport = async (format: 'word' | 'pdf') => {
    try {
      setLoading(true);
      
      // 准备报告数据
      const reportData = {
        checkInfo: checkData,
        hiddenDangers: hiddenDangers,
        statistics: getStatistics(),
        generateTime: new Date().toLocaleString()
      };

      // 调用报告生成API（需要后端实现）
      const response = await fetch(`/api/projectCheck/${id}/report`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: format,
          data: reportData
        })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `检查报告_${checkData.projectName}_${checkData.checkDate}.${format === 'word' ? 'docx' : 'pdf'}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        message.success(`${format.toUpperCase()}报告生成成功`);
      } else {
        throw new Error('报告生成失败');
      }
    } catch (error) {
      message.error('报告生成失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const getStatistics = () => {
    const totalCount = hiddenDangers.length;
    const confirmedCount = hiddenDangers.filter(item => item.status === 1).length;
    const pendingCount = hiddenDangers.filter(item => item.status === 0).length;
    const majorDangers = hiddenDangers.filter(item => item.problemLevel === '重大隐患').length;
    const moderateDangers = hiddenDangers.filter(item => item.problemLevel === '较大隐患').length;
    const minorDangers = hiddenDangers.filter(item => item.problemLevel === '一般隐患').length;
    const needReviewCount = hiddenDangers.filter(item => item.needReview).length;

    return {
      totalCount,
      confirmedCount,
      pendingCount,
      majorDangers,
      moderateDangers,
      minorDangers,
      needReviewCount
    };
  };

  // 报告下载菜单项
  const reportMenuItems = [
    {
      key: 'word',
      icon: <FileWordOutlined />,
      label: 'Word报告',
      onClick: () => handleGenerateReport('word')
    },
    {
      key: 'pdf', 
      icon: <FilePdfOutlined />,
      label: 'PDF报告',
      onClick: () => handleGenerateReport('pdf')
    }
  ];

  // 隐患明细表格列定义
  const hiddenDangerColumns = [
    { title: '隐患大类', dataIndex: 'dangerCategory', key: 'dangerCategory' },
    { title: '问题等级', dataIndex: 'problemLevel', key: 'problemLevel' },
    { title: '安全隐患', dataIndex: 'safetyHazard', key: 'safetyHazard' },
    { title: '具体描述', dataIndex: 'description', key: 'description', ellipsis: true },
    { 
      title: '是否需要复查', 
      dataIndex: 'needReview', 
      key: 'needReview',
      render: (needReview: boolean) => needReview ? '是' : '否'
    },
    { 
      title: '状态', 
      dataIndex: 'status', 
      key: 'status',
      render: (status: number) => status === 0 ? '待确认' : '正式记录'
    },
    { title: '创建时间', dataIndex: 'creationTime', key: 'creationTime' },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space>
          <Button type="link" size="small" icon={<EditOutlined />} onClick={() => handleEditHiddenDanger(record)}>
            编辑
          </Button>
          {record.status === 0 && (
            <Button type="link" size="small" onClick={() => handleConfirmHiddenDanger(record)}>
              确认
            </Button>
          )}
          <Button type="link" size="small" danger icon={<DeleteOutlined />} onClick={() => handleDeleteHiddenDanger(record)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 检查任务基本信息 */}
      <Card title="检查任务详情" style={{ marginBottom: '24px' }}>
        <Descriptions bordered column={2}>
          <Descriptions.Item label="项目名称">{checkData.projectName}</Descriptions.Item>
          <Descriptions.Item label="检查类型">{checkData.checkType}</Descriptions.Item>
          <Descriptions.Item label="二级分类">{checkData.secondaryCategory}</Descriptions.Item>
          <Descriptions.Item label="检查日期">{checkData.checkDate}</Descriptions.Item>
          <Descriptions.Item label="委托单位">{checkData.clientUnit}</Descriptions.Item>
          <Descriptions.Item label="检查组长">{checkData.checkLeader}</Descriptions.Item>
          <Descriptions.Item label="检查组员">{checkData.checkMembers}</Descriptions.Item>
          <Descriptions.Item label="工程进度">{checkData.projectProgress}</Descriptions.Item>
          <Descriptions.Item label="检查内容" span={2}>{checkData.checkContent}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{checkData.creationTime}</Descriptions.Item>
          <Descriptions.Item label="创建人">{checkData.creatorName}</Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 隐患统计信息 */}
      {hiddenDangers.length > 0 && (
        <Card title="隐患统计" style={{ marginBottom: '24px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ display: 'flex', gap: '24px', flex: 1 }}>
              <Statistic 
                title="隐患总数" 
                value={getStatistics().totalCount} 
                prefix={<BarChartOutlined />}
              />
              <Statistic 
                title="已确认" 
                value={getStatistics().confirmedCount} 
                valueStyle={{ color: '#52c41a' }}
              />
              <Statistic 
                title="待确认" 
                value={getStatistics().pendingCount} 
                valueStyle={{ color: '#faad14' }}
              />
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <div style={{ fontSize: '14px', color: '#666' }}>隐患等级</div>
                <Space>
                  <Tag color="red">重大 {getStatistics().majorDangers}</Tag>
                  <Tag color="orange">较大 {getStatistics().moderateDangers}</Tag>
                  <Tag color="blue">一般 {getStatistics().minorDangers}</Tag>
                </Space>
              </div>
              <Statistic 
                title="需要复查" 
                value={getStatistics().needReviewCount} 
                valueStyle={{ color: '#1890ff' }}
              />
            </div>
            <div>
              <Dropdown.Button
                type="primary"
                icon={<DownloadOutlined />}
                menu={{ items: reportMenuItems }}
                loading={loading}
                onClick={() => handleGenerateReport('word')}
              >
                生成检查报告
              </Dropdown.Button>
            </div>
          </div>
        </Card>
      )}

      {/* 隐患明细列表 */}
      <Card 
        title="隐患明细列表" 
        extra={
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddHiddenDanger}>
              添加隐患明细
            </Button>
            <Button icon={<CameraOutlined />} onClick={() => setPhotoModalVisible(true)}>
              拍照生成隐患明细
            </Button>
          </Space>
        }
      >
        <Table
          columns={hiddenDangerColumns}
          dataSource={hiddenDangers}
          loading={loading}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* 拍照上传弹窗 */}
      <Modal
        title="拍照生成隐患明细"
        open={photoModalVisible}
        onCancel={() => setPhotoModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          accept="image/*"
          beforeUpload={handlePhotoUpload}
          showUploadList={false}
          disabled={uploading}
        >
          <p className="ant-upload-drag-icon">
            <CameraOutlined />
          </p>
          <p className="ant-upload-text">
            {uploading ? '正在处理照片...' : '点击或拖拽上传照片'}
          </p>
          <p className="ant-upload-hint">
            支持单张照片上传，系统将自动识别并生成隐患明细
          </p>
        </Upload.Dragger>
      </Modal>

      {/* 返回按钮 */}
      <div style={{ marginTop: '24px', textAlign: 'center' }}>
        <Button onClick={() => navigate('/project-check')}>返回列表</Button>
      </div>
    </div>
  );
};

export default ProjectCheckDetail; 