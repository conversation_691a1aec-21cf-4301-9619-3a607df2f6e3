// 报告生成相关API
import { httpClient } from '@/utils/httpClient';

// 生成检查报告
export const generateCheckReport = (checkId: number, format: 'word' | 'pdf', data: any) => {
  return httpClient.post(`/projectCheck/${checkId}/report`, {
    format,
    data
  }, {
    responseType: 'blob'
  });
};

// 获取报告模板列表
export const getReportTemplates = () => {
  return httpClient.get('/report/templates');
};

// 预览报告内容
export const previewReport = (checkId: number, templateId?: string) => {
  return httpClient.post(`/projectCheck/${checkId}/preview`, {
    templateId
  });
};

// 导出隐患明细Excel
export const exportHiddenDangersExcel = (checkId: number) => {
  return httpClient.get(`/projectCheck/${checkId}/export-excel`, {
    responseType: 'blob'
  });
};

// 批量导出检查报告
export const batchExportReports = (checkIds: number[], format: 'word' | 'pdf') => {
  return httpClient.post('/projectCheck/batch-export', {
    checkIds,
    format
  }, {
    responseType: 'blob'
  });
};