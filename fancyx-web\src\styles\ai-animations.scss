// 导入主题变量
@use './ai-theme.scss' as *;

// AI科技风格动画效果

// 粒子动画
@keyframes ai-particle-float {
  0% {
    transform: translateY(100vh) translateX(-10px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(10px);
    opacity: 0;
  }
}

// 发光脉冲动画
@keyframes ai-glow-pulse {
  0%, 100% {
    box-shadow: $ai-glow-primary;
  }
  50% {
    box-shadow: 0 0 30px rgba(126, 87, 194, 0.6);
  }
}

// 霓虹闪烁动画
@keyframes ai-neon-flicker {
  0%, 100% {
    text-shadow: 
      0 0 5px $ai-neon-blue,
      0 0 10px $ai-neon-blue,
      0 0 15px $ai-neon-blue;
  }
  50% {
    text-shadow: 
      0 0 2px $ai-neon-blue,
      0 0 5px $ai-neon-blue,
      0 0 8px $ai-neon-blue;
  }
}

// 渐变移动动画
@keyframes ai-gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 淡入动画
@keyframes ai-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 滑入动画
@keyframes ai-slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes ai-slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 缩放动画
@keyframes ai-scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 旋转动画
@keyframes ai-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 波纹扩散动画
@keyframes ai-ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

// 数据流动画
@keyframes ai-data-flow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

// 动画类定义
.ai-animate-fade-in {
  animation: ai-fade-in 0.6s ease-out;
}

.ai-animate-slide-in-left {
  animation: ai-slide-in-left 0.8s ease-out;
}

.ai-animate-slide-in-right {
  animation: ai-slide-in-right 0.8s ease-out;
}

.ai-animate-scale-in {
  animation: ai-scale-in 0.5s ease-out;
}

.ai-animate-glow-pulse {
  animation: ai-glow-pulse 2s ease-in-out infinite;
}

.ai-animate-neon-flicker {
  animation: ai-neon-flicker 3s ease-in-out infinite;
}

.ai-animate-gradient-shift {
  background-size: 200% 200%;
  animation: ai-gradient-shift 4s ease infinite;
}

.ai-animate-rotate {
  animation: ai-rotate 10s linear infinite;
}

// 悬停效果
.ai-hover-glow {
  transition: box-shadow $ai-transition-normal;
  
  &:hover {
    box-shadow: $ai-glow-primary;
  }
}

.ai-hover-scale {
  transition: transform $ai-transition-normal;
  
  &:hover {
    transform: scale(1.05);
  }
}

.ai-hover-lift {
  transition: transform $ai-transition-normal, box-shadow $ai-transition-normal;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $ai-shadow-lg;
  }
}