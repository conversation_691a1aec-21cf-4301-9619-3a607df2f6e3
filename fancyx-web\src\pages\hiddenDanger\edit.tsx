// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Form, Input, Button, message, Select, Switch, Upload, Card, Row, Col, Tag, Divider } from 'antd';
import { UploadOutlined, CameraOutlined, SoundOutlined } from '@ant-design/icons';
import { createHiddenDanger, updateHiddenDanger, getHiddenDangerById } from '@/api/hiddenDanger';
import { getProjectCheckList } from '@/api/projectCheck';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

const { Option } = Select;
const { TextArea } = Input;

// 隐患大类标准分类
const DANGER_CATEGORIES = [
  { value: '高处作业', label: '高处作业', color: 'red' },
  { value: '临时用电', label: '临时用电', color: 'orange' },
  { value: '脚手架', label: '脚手架', color: 'gold' },
  { value: '起重机械', label: '起重机械', color: 'green' },
  { value: '模板支撑', label: '模板支撑', color: 'cyan' },
  { value: '基坑支护', label: '基坑支护', color: 'blue' },
  { value: '消防安全', label: '消防安全', color: 'purple' },
  { value: '文明施工', label: '文明施工', color: 'magenta' },
  { value: '施工机具', label: '施工机具', color: 'volcano' },
  { value: '个人防护', label: '个人防护', color: 'geekblue' },
  { value: '材料堆放', label: '材料堆放', color: 'lime' },
  { value: '环境保护', label: '环境保护', color: 'green' }
];

// 问题等级选项
const PROBLEM_LEVELS = [
  { value: '一般隐患', label: '一般隐患', color: 'blue' },
  { value: '较大隐患', label: '较大隐患', color: 'orange' },
  { value: '重大隐患', label: '重大隐患', color: 'red' }
];

// 常见隐患描述模板
const DANGER_TEMPLATES = {
  '高处作业': [
    '未佩戴安全帽和安全带',
    '脚手架防护不到位',
    '临边洞口未设防护',
    '安全网设置不规范'
  ],
  '临时用电': [
    '电线私拉乱接',
    '配电箱未加锁',
    '接地保护不到位',
    '电缆破损未及时更换'
  ],
  '脚手架': [
    '脚手架搭设不规范',
    '连墙件设置不足',
    '脚手架基础不牢固',
    '防护栏杆高度不足'
  ],
  '消防安全': [
    '消防器材配置不足',
    '消防通道堵塞',
    '易燃物品管理不当',
    '现场吸烟行为'
  ]
};

const HiddenDangerEdit: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const isEdit = !!id;
  const [checkOptions, setCheckOptions] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [isRecording, setIsRecording] = useState(false);

  // 隐患分类改变时的处理
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    // 可以根据分类自动填充相关的标准规范等信息
  };

  // 快速填充隐患描述模板
  const handleTemplateSelect = (template: string) => {
    const currentDescription = form.getFieldValue('description') || '';
    const newDescription = currentDescription ? `${currentDescription}\n${template}` : template;
    form.setFieldsValue({ description: newDescription });
  };

  // 语音输入功能（示例）
  const handleVoiceInput = () => {
    if (!isRecording) {
      setIsRecording(true);
      // 这里可以集成语音识别API
      message.info('语音输入功能开发中...');
      setTimeout(() => {
        setIsRecording(false);
      }, 2000);
    }
  };

  useEffect(() => {
    // 获取检查任务列表用于下拉选择
    getProjectCheckList({ pageSize: 1000 }).then(res => {
      const options = res.data?.items?.map((item: any) => ({
        label: `${item.projectName} - ${item.checkType}`,
        value: item.id,
      })) || [];
      setCheckOptions(options);
    });

    if (isEdit) {
      getHiddenDangerById(id).then(res => {
        const data = res.data;
        form.setFieldsValue({
          ...data,
          photos: data.photos?.map((url: string, index: number) => ({
            uid: index,
            name: `photo-${index}`,
            status: 'done',
            url,
          })) || [],
        });
      });
    } else {
      // 从URL参数获取检查任务信息并自动填入
      const projectCheckId = searchParams.get('projectCheckId');
      const checkInfo = searchParams.get('checkInfo');
      if (projectCheckId) {
        form.setFieldsValue({
          projectCheckId: Number(projectCheckId),
          checkInfo: checkInfo ? decodeURIComponent(checkInfo) : '',
        });
      }
    }
  }, [id, isEdit, form, searchParams]);

  const onFinish = async (values: any) => {
    const submitData = {
      ...values,
      photos: values.photos?.map((file: any) => file.url || file.response?.url) || [],
    };
    if (isEdit) {
      await updateHiddenDanger(id, submitData);
      message.success('编辑成功');
    } else {
      await createHiddenDanger(submitData);
      message.success('新增成功');
    }
    navigate('/hidden-danger');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title={isEdit ? '编辑隐患明细' : '新增隐患明细'} bordered={false}>
        <Form form={form} layout="vertical" onFinish={onFinish}>
          {/* 基本信息 */}
          <Card type="inner" title="基本信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="projectCheckId" label="关联检查任务" rules={[{ required: true, message: '请选择检查任务' }]}>
                  <Select 
                    placeholder="请选择检查任务" 
                    options={checkOptions} 
                    showSearch
                    filterOption={(input, option) =>
                      option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="dangerCategory" label="隐患大类" rules={[{ required: true, message: '请选择隐患大类' }]}>
                  <Select 
                    placeholder="请选择隐患大类" 
                    onChange={handleCategoryChange}
                    showSearch
                  >
                    {DANGER_CATEGORIES.map(category => (
                      <Option key={category.value} value={category.value}>
                        <Tag color={category.color} style={{ marginRight: 8 }}>
                          {category.label}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="problemLevel" label="问题等级" rules={[{ required: true, message: '请选择问题等级' }]}>
                  <Select placeholder="请选择问题等级">
                    {PROBLEM_LEVELS.map(level => (
                      <Option key={level.value} value={level.value}>
                        <Tag color={level.color}>
                          {level.label}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            {/* 快速模板选择 */}
            {selectedCategory && DANGER_TEMPLATES[selectedCategory] && (
              <div style={{ marginBottom: 16 }}>
                <div style={{ marginBottom: 8, fontWeight: 500 }}>常见隐患模板：</div>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                  {DANGER_TEMPLATES[selectedCategory].map((template, index) => (
                    <Tag 
                      key={index}
                      style={{ cursor: 'pointer', marginBottom: 4 }}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      + {template}
                    </Tag>
                  ))}
                </div>
              </div>
            )}
          </Card>

          {/* 隐患描述 */}
          <Card type="inner" title="隐患描述" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="safetyHazard" label="安全隐患" rules={[{ required: true, message: '请输入安全隐患' }]}>
                  <Input placeholder="请简要描述安全隐患" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="description" label="具体描述" rules={[{ required: true, message: '请输入具体描述' }]}>
                  <div style={{ position: 'relative' }}>
                    <TextArea 
                      rows={4} 
                      placeholder="请详细描述隐患情况，可点击语音输入..."
                      maxLength={500}
                      showCount
                    />
                    <Button
                      type="text"
                      icon={<SoundOutlined />}
                      size="small"
                      loading={isRecording}
                      onClick={handleVoiceInput}
                      style={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        color: isRecording ? '#1890ff' : '#666'
                      }}
                      title="点击语音输入"
                    />
                  </div>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 标准规范和法规 */}
          <Card type="inner" title="标准规范" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="standardId" label="标准规范ID">
                  <Input placeholder="请输入标准ID" />
                </Form.Item>
              </Col>
              <Col span={16}>
                <Form.Item name="standardName" label="标准规范名称">
                  <Input placeholder="请输入标准规范名称" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="regulationClause" label="相关法规条款">
                  <TextArea 
                    rows={2} 
                    placeholder="请输入相关法规条款"
                    maxLength={200}
                    showCount
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 整改要求 */}
          <Card type="inner" title="整改要求" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="rectificationRequirement" label="整改要求" rules={[{ required: true, message: '请输入整改要求' }]}>
                  <TextArea 
                    rows={3} 
                    placeholder="请输入具体的整改要求"
                    maxLength={300}
                    showCount
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="possibleConsequences" label="可能产生后果">
                  <TextArea 
                    rows={2} 
                    placeholder="请描述不及时整改可能产生的后果"
                    maxLength={200}
                    showCount
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="rectificationSuggestion" label="整改建议">
                  <TextArea 
                    rows={3} 
                    placeholder="请提供整改建议和措施"
                    maxLength={300}
                    showCount
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 现场照片 */}
          <Card type="inner" title="现场照片" style={{ marginBottom: 16 }}>
            <Form.Item name="photos" label="隐患现场照片">
              <Upload
                multiple
                listType="picture-card"
                beforeUpload={() => false}
                maxCount={9}
              >
                <div style={{ textAlign: 'center' }}>
                  <CameraOutlined style={{ fontSize: 24, marginBottom: 8, color: '#666' }} />
                  <div>上传照片</div>
                  <div style={{ fontSize: '12px', color: '#999' }}>最多9张</div>
                </div>
              </Upload>
            </Form.Item>
            <div style={{ color: '#666', fontSize: '12px', marginTop: 8 }}>
              💡 提示：建议上传清晰的现场照片，有助于问题识别和整改验证
            </div>
          </Card>

          {/* 其他设置 */}
          <Card type="inner" title="其他设置" style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="needReview" label="是否需要复查" valuePropName="checked">
                  <Switch checkedChildren="需要" unCheckedChildren="不需要" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="status" label="记录状态" initialValue={0}>
                  <Select>
                    <Option value={0}>
                      <Tag color="orange">待确认</Tag>
                    </Option>
                    <Option value={1}>
                      <Tag color="green">正式记录</Tag>
                    </Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 操作按钮 */}
          <Form.Item style={{ textAlign: 'center' }}>
            <Button type="primary" htmlType="submit" size="large" style={{ marginRight: 16 }}>
              {isEdit ? '保存修改' : '创建隐患记录'}
            </Button>
            <Button size="large" onClick={() => navigate('/hidden-danger')}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default HiddenDangerEdit; 