using Fancyx.Repository.BaseEntity;
using FreeSql.DataAnnotations;
using System.ComponentModel.DataAnnotations;
using Fancyx.Core.Interfaces;

namespace Fancyx.Admin.Entities.Project
{
    [Table(Name = "sys_project_check")]
    public class ProjectCheckDO : FullAuditedEntity, ITenant
    {
        /// <summary>
        /// 主键ID - 重写为long类型以匹配业务需求
        /// </summary>
        [Column(IsPrimary = true)]
        public new long Id { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        [Required]
        public long ProjectId { get; set; }

        /// <summary>
        /// 检查类型（生产安全、质量安全）
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string CheckType { get; set; } = string.Empty;

        /// <summary>
        /// 二级分类
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string SecondaryCategory { get; set; } = string.Empty;

        /// <summary>
        /// 检查日期
        /// </summary>
        [Required]
        public DateTime CheckDate { get; set; }

        /// <summary>
        /// 委托单位
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string ClientUnit { get; set; } = string.Empty;

        /// <summary>
        /// 检查组长
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string CheckLeader { get; set; } = string.Empty;

        /// <summary>
        /// 检查组员
        /// </summary>
        [MaxLength(500)]
        public string? CheckMembers { get; set; }

        /// <summary>
        /// 工程进度
        /// </summary>
        [MaxLength(100)]
        public string? ProjectProgress { get; set; }

        /// <summary>
        /// 检查内容
        /// </summary>
        [MaxLength(2000)]
        public string? CheckContent { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        public string? TenantId { get; set; }
    }
}