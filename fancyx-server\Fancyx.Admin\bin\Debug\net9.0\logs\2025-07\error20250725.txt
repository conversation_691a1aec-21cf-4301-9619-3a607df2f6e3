2025-07-25 22:01:26.310 +08:00 [ERR] 全局捕获异常
Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.TimeoutException: Timeout during reading attempt
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.AuthenticateSASL(List`1 mechanisms, String username, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.Authenticate(String username, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|213_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at FreeSql.Internal.CommonProvider.DbConnectionStringPool.Get(Nullable`1 timeout)
   at FreeSql.PostgreSQL.PostgreSQLDbFirst.get_ServerVersion()
   at FreeSql.PostgreSQL.PostgreSQLDbFirst.get_IsPg10()
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.Select0Provider`2..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.Internal.CommonProvider.Select1Provider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.Curd.PostgreSQLSelect`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateSelectProvider[T1](Object dywhere)
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Select[T1]()
   at FreeSql.DbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelectInternal(Object dywhere)
   at FreeSql.BaseRepository`1.get_Select()
   at FreeSql.BaseRepository`1.WhereIf(Boolean condition, Expression`1 exp)
   at Fancyx.Admin.Service.System.MenuService.GetMenuListAsync(MenuQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Service\System\MenuService.cs:line 63
   at Castle.DynamicProxy.AsyncInterceptorBase.ProceedAsynchronous[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo)
   at Fancyx.Core.AutoInject.AsyncAopAttributeInterceptor.InterceptAsync[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo, Func`3 proceed) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Core\AutoInject\AsyncAopAttributeInterceptor.cs:line 68
   at Fancyx.Admin.Controllers.System.MenuController.GetMenuListAsync(MenuQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Controllers\System\MenuController.cs:line 49
   at lambda_method677(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-07-25 22:27:34.026 +08:00 [ERR] NotificationJob发生错误
System.Exception: Exception while reading from stream
 ---> Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.TimeoutException: Timeout during reading attempt
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteScalar(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteScalar()
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   --- End of inner exception stack trace ---
   at FreeSql.Internal.CommonProvider.AdoProvider.LoggerException(IObjectPool`1 pool, PrepareCommandResult pc, Exception ex, DateTime dt, StringBuilder logtxt, Boolean isThrowException)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(CommandType cmdType, String cmdText, DbParameter[] cmdParms)
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.Select0Provider`2..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.Internal.CommonProvider.Select1Provider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.Curd.PostgreSQLSelect`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateSelectProvider[T1](Object dywhere)
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Select[T1]()
   at FreeSql.DbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelectInternal(Object dywhere)
   at FreeSql.BaseRepository`1.get_Select()
   at FreeSql.BaseRepository`1.Where(Expression`1 exp)
   at Fancyx.Admin.Jobs.NotificationJob.Invoke() in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Jobs\NotificationJob.cs:line 41
2025-07-25 22:31:53.426 +08:00 [ERR] 全局捕获异常
System.Exception: Exception while reading from stream
 ---> Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.TimeoutException: Timeout during reading attempt
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteScalar(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteScalar()
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   --- End of inner exception stack trace ---
   at FreeSql.Internal.CommonProvider.AdoProvider.LoggerException(IObjectPool`1 pool, PrepareCommandResult pc, Exception ex, DateTime dt, StringBuilder logtxt, Boolean isThrowException)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(CommandType cmdType, String cmdText, DbParameter[] cmdParms)
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.Select0Provider`2..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.Internal.CommonProvider.Select1Provider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.Curd.PostgreSQLSelect`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateSelectProvider[T1](Object dywhere)
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Select[T1]()
   at FreeSql.DbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelectInternal(Object dywhere)
   at FreeSql.BaseRepository`1.get_Select()
   at Fancyx.Admin.Service.System.UserService.GetUserListAsync(UserQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Service\System\UserService.cs:line 96
   at Castle.DynamicProxy.AsyncInterceptorBase.ProceedAsynchronous[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo)
   at Fancyx.Core.AutoInject.AsyncAopAttributeInterceptor.InterceptAsync[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo, Func`3 proceed) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Core\AutoInject\AsyncAopAttributeInterceptor.cs:line 68
   at Fancyx.Admin.Controllers.System.UserController.GetUserListAsync(UserQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Controllers\System\UserController.cs:line 50
   at lambda_method793(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-07-25 22:32:51.323 +08:00 [ERR] API访问日志收集失败
Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.TimeoutException: Timeout during reading attempt
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteNonQuery(Boolean async, CancellationToken cancellationToken)
   at DotNetCore.CAP.PostgreSql.DbConnectionExtensions.ExecuteNonQueryAsync(DbConnection connection, String sql, DbTransaction transaction, Object[] sqlParams)
   at DotNetCore.CAP.PostgreSql.DbConnectionExtensions.ExecuteNonQueryAsync(DbConnection connection, String sql, DbTransaction transaction, Object[] sqlParams)
   at DotNetCore.CAP.PostgreSql.PostgreSqlDataStorage.StoreMessageAsync(String name, Message content, Object transaction)
   at DotNetCore.CAP.PostgreSql.PostgreSqlDataStorage.StoreMessageAsync(String name, Message content, Object transaction)
   at DotNetCore.CAP.Internal.CapPublisher.PublishInternalAsync[T](String name, T value, IDictionary`2 headers, Nullable`1 delayTime, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.CapPublisher.PublishAsync[T](String name, T value, IDictionary`2 headers, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.CapPublisher.PublishAsync[T](String name, T value, String callbackName, CancellationToken cancellationToken)
   at Fancyx.Logger.ApiAccessLogAttribute.PushAsync(HttpContext httpContext, ApiAccessLogMessage msg) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Logger\ApiAccessLogAttribute.cs:line 100
   at Fancyx.Logger.ApiAccessLogAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Logger\ApiAccessLogAttribute.cs:line 64
2025-07-25 22:33:28.016 +08:00 [ERR] 全局捕获异常
System.Exception: Exception while reading from stream
 ---> Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.TimeoutException: Timeout during reading attempt
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteScalar(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteScalar()
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   --- End of inner exception stack trace ---
   at FreeSql.Internal.CommonProvider.AdoProvider.LoggerException(IObjectPool`1 pool, PrepareCommandResult pc, Exception ex, DateTime dt, StringBuilder logtxt, Boolean isThrowException)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(CommandType cmdType, String cmdText, DbParameter[] cmdParms)
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.Select0Provider`2..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.Internal.CommonProvider.Select1Provider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.Curd.PostgreSQLSelect`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateSelectProvider[T1](Object dywhere)
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Select[T1]()
   at FreeSql.DbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelectInternal(Object dywhere)
   at FreeSql.BaseRepository`1.get_Select()
   at Fancyx.Admin.Service.System.UserService.GetUserListAsync(UserQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Service\System\UserService.cs:line 96
   at Castle.DynamicProxy.AsyncInterceptorBase.ProceedAsynchronous[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo)
   at Fancyx.Core.AutoInject.AsyncAopAttributeInterceptor.InterceptAsync[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo, Func`3 proceed) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Core\AutoInject\AsyncAopAttributeInterceptor.cs:line 68
   at Fancyx.Admin.Controllers.System.UserController.GetUserListAsync(UserQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Controllers\System\UserController.cs:line 50
   at lambda_method793(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-07-25 22:33:42.735 +08:00 [ERR] An exception occurred while executing the subscription method. Topic:api_access_log_event, Id:920637389123887283, Instance: LAPTOP-C2UDCSVM
DotNetCore.CAP.Internal.SubscriberExecutionFailedException: The operation has timed out
 ---> Npgsql.NpgsqlException (0x80004005): The operation has timed out
 ---> System.TimeoutException: The operation has timed out.
   at Npgsql.ThrowHelper.ThrowNpgsqlExceptionWithInnerTimeoutException(String message)
   at Npgsql.Util.NpgsqlTimeout.Check()
   at Npgsql.Util.NpgsqlTimeout.CheckAndGetTimeLeft()
   at Npgsql.Util.NpgsqlTimeout.CheckAndApply(NpgsqlConnector connector)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|213_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at FreeSql.Internal.CommonProvider.DbConnectionStringPool.Get(Nullable`1 timeout)
   at FreeSql.PostgreSQL.PostgreSQLDbFirst.get_ServerVersion()
   at FreeSql.PostgreSQL.PostgreSQLDbFirst.get_IsPg10()
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.InsertProvider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression)
   at FreeSql.PostgreSQL.Curd.PostgreSQLInsert`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateInsertProvider[T1]()
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Insert[T1]()
   at FreeSql.DbContextScopedFreeSql.Insert[T1]()
   at FreeSql.DbContextScopedFreeSql.Insert[T1](T1 source)
   at Fancyx.Logger.LoggerCapSubscriber.ApiAccessLog(ApiAccessLogMessage message) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Logger\LoggerCapSubscriber.cs:line 64
   at lambda_method688(Closure, Object)
   at Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable.Awaiter.GetResult()
   at DotNetCore.CAP.Internal.SubscribeInvoker.ExecuteWithParameterAsync(ObjectMethodExecutor executor, Object class, Object[] parameter)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeExecutor.InvokeConsumerMethodAsync(MediumMessage message, ConsumerExecutorDescriptor descriptor, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Npgsql.ThrowHelper.ThrowNpgsqlExceptionWithInnerTimeoutException(String message)
   at Npgsql.Util.NpgsqlTimeout.Check()
   at Npgsql.Util.NpgsqlTimeout.CheckAndGetTimeLeft()
   at Npgsql.Util.NpgsqlTimeout.CheckAndApply(NpgsqlConnector connector)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|213_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at FreeSql.Internal.CommonProvider.DbConnectionStringPool.Get(Nullable`1 timeout)
   at FreeSql.PostgreSQL.PostgreSQLDbFirst.get_ServerVersion()
   at FreeSql.PostgreSQL.PostgreSQLDbFirst.get_IsPg10()
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.InsertProvider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression)
   at FreeSql.PostgreSQL.Curd.PostgreSQLInsert`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateInsertProvider[T1]()
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Insert[T1]()
   at FreeSql.DbContextScopedFreeSql.Insert[T1]()
   at FreeSql.DbContextScopedFreeSql.Insert[T1](T1 source)
   at Fancyx.Logger.LoggerCapSubscriber.ApiAccessLog(ApiAccessLogMessage message) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Logger\LoggerCapSubscriber.cs:line 64
   at lambda_method688(Closure, Object)
   at Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable.Awaiter.GetResult()
   at DotNetCore.CAP.Internal.SubscribeInvoker.ExecuteWithParameterAsync(ObjectMethodExecutor executor, Object class, Object[] parameter)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeExecutor.InvokeConsumerMethodAsync(MediumMessage message, ConsumerExecutorDescriptor descriptor, CancellationToken cancellationToken)
2025-07-25 22:34:43.575 +08:00 [ERR] An exception occurred while executing the subscription method. Topic:api_access_log_event, Id:920637389123887293, Instance: LAPTOP-C2UDCSVM
DotNetCore.CAP.Internal.SubscriberExecutionFailedException: Exception while reading from stream
 ---> System.Exception: Exception while reading from stream
 ---> Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.TimeoutException: Timeout during reading attempt
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteReaderMultiple(Int32 multipleResult, DbConnection connection, DbTransaction transaction, Action`2 fetchHandler, Action`2 schemaHandler, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   --- End of inner exception stack trace ---
   at FreeSql.Internal.CommonProvider.AdoProvider.LoggerException(IObjectPool`1 pool, PrepareCommandResult pc, Exception ex, DateTime dt, StringBuilder logtxt, Boolean isThrowException)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteReaderMultiple(Int32 multipleResult, DbConnection connection, DbTransaction transaction, Action`2 fetchHandler, Action`2 schemaHandler, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteReader(DbConnection connection, DbTransaction transaction, Action`1 fetchHandler, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteArray(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteArray(CommandType cmdType, String cmdText, DbParameter[] cmdParms)
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.InsertProvider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression)
   at FreeSql.PostgreSQL.Curd.PostgreSQLInsert`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateInsertProvider[T1]()
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Insert[T1]()
   at FreeSql.DbContextScopedFreeSql.Insert[T1]()
   at FreeSql.DbContextScopedFreeSql.Insert[T1](T1 source)
   at Fancyx.Logger.LoggerCapSubscriber.ApiAccessLog(ApiAccessLogMessage message) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Logger\LoggerCapSubscriber.cs:line 64
   at lambda_method688(Closure, Object)
   at Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable.Awaiter.GetResult()
   at DotNetCore.CAP.Internal.SubscribeInvoker.ExecuteWithParameterAsync(ObjectMethodExecutor executor, Object class, Object[] parameter)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeExecutor.InvokeConsumerMethodAsync(MediumMessage message, ConsumerExecutorDescriptor descriptor, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at FreeSql.Internal.CommonProvider.AdoProvider.LoggerException(IObjectPool`1 pool, PrepareCommandResult pc, Exception ex, DateTime dt, StringBuilder logtxt, Boolean isThrowException)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteReaderMultiple(Int32 multipleResult, DbConnection connection, DbTransaction transaction, Action`2 fetchHandler, Action`2 schemaHandler, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteReader(DbConnection connection, DbTransaction transaction, Action`1 fetchHandler, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteArray(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteArray(CommandType cmdType, String cmdText, DbParameter[] cmdParms)
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.InsertProvider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression)
   at FreeSql.PostgreSQL.Curd.PostgreSQLInsert`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateInsertProvider[T1]()
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Insert[T1]()
   at FreeSql.DbContextScopedFreeSql.Insert[T1]()
   at FreeSql.DbContextScopedFreeSql.Insert[T1](T1 source)
   at Fancyx.Logger.LoggerCapSubscriber.ApiAccessLog(ApiAccessLogMessage message) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Logger\LoggerCapSubscriber.cs:line 64
   at lambda_method688(Closure, Object)
   at Microsoft.Extensions.Internal.ObjectMethodExecutorAwaitable.Awaiter.GetResult()
   at DotNetCore.CAP.Internal.SubscribeInvoker.ExecuteWithParameterAsync(ObjectMethodExecutor executor, Object class, Object[] parameter)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeInvoker.InvokeAsync(ConsumerContext context, CancellationToken cancellationToken)
   at DotNetCore.CAP.Internal.SubscribeExecutor.InvokeConsumerMethodAsync(MediumMessage message, ConsumerExecutorDescriptor descriptor, CancellationToken cancellationToken)
2025-07-25 22:36:43.686 +08:00 [ERR] 全局捕获异常
System.Exception: Exception while reading from stream
 ---> Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.TimeoutException: Timeout during reading attempt
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(CommandBehavior behavior)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteReaderMultiple(Int32 multipleResult, DbConnection connection, DbTransaction transaction, Action`2 fetchHandler, Action`2 schemaHandler, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   --- End of inner exception stack trace ---
   at FreeSql.Internal.CommonProvider.AdoProvider.LoggerException(IObjectPool`1 pool, PrepareCommandResult pc, Exception ex, DateTime dt, StringBuilder logtxt, Boolean isThrowException)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteReaderMultiple(Int32 multipleResult, DbConnection connection, DbTransaction transaction, Action`2 fetchHandler, Action`2 schemaHandler, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteReader(DbConnection connection, DbTransaction transaction, Action`1 fetchHandler, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteArray(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteArray(CommandType cmdType, String cmdText, DbParameter[] cmdParms)
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.Select0Provider`2..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.Internal.CommonProvider.Select1Provider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.Curd.PostgreSQLSelect`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateSelectProvider[T1](Object dywhere)
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Select[T1]()
   at FreeSql.DbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelectInternal(Object dywhere)
   at FreeSql.BaseRepository`1.get_Select()
   at Fancyx.Admin.Service.System.UserService.GetUserListAsync(UserQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Service\System\UserService.cs:line 96
   at Castle.DynamicProxy.AsyncInterceptorBase.ProceedAsynchronous[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo)
   at Fancyx.Core.AutoInject.AsyncAopAttributeInterceptor.InterceptAsync[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo, Func`3 proceed) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Core\AutoInject\AsyncAopAttributeInterceptor.cs:line 68
   at Fancyx.Admin.Controllers.System.UserController.GetUserListAsync(UserQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Controllers\System\UserController.cs:line 50
   at lambda_method793(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-07-25 22:36:58.877 +08:00 [ERR] 全局捕获异常
Npgsql.NpgsqlException (0x80004005): The operation has timed out
 ---> System.TimeoutException: The operation has timed out.
   at Npgsql.ThrowHelper.ThrowNpgsqlExceptionWithInnerTimeoutException(String message)
   at Npgsql.Util.NpgsqlTimeout.Check()
   at Npgsql.Util.NpgsqlTimeout.CheckAndGetTimeLeft()
   at Npgsql.Util.NpgsqlTimeout.CheckAndApply(NpgsqlConnector connector)
   at Npgsql.Internal.NpgsqlConnector.RawOpen(SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|213_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.OpenNewConnector(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.PoolingDataSource.<Get>g__RentAsync|33_0(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at FreeSql.Internal.CommonProvider.DbConnectionStringPool.Get(Nullable`1 timeout)
   at FreeSql.PostgreSQL.PostgreSQLDbFirst.get_ServerVersion()
   at FreeSql.PostgreSQL.PostgreSQLDbFirst.get_IsPg10()
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.Select0Provider`2..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.Internal.CommonProvider.Select1Provider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.Curd.PostgreSQLSelect`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateSelectProvider[T1](Object dywhere)
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Select[T1]()
   at FreeSql.DbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelectInternal(Object dywhere)
   at FreeSql.BaseRepository`1.get_Select()
   at Fancyx.Admin.Service.System.UserService.GetUserListAsync(UserQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Service\System\UserService.cs:line 96
   at Castle.DynamicProxy.AsyncInterceptorBase.ProceedAsynchronous[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo)
   at Fancyx.Core.AutoInject.AsyncAopAttributeInterceptor.InterceptAsync[TResult](IInvocation invocation, IInvocationProceedInfo proceedInfo, Func`3 proceed) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Core\AutoInject\AsyncAopAttributeInterceptor.cs:line 68
   at Fancyx.Admin.Controllers.System.UserController.GetUserListAsync(UserQueryDto dto) in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Controllers\System\UserController.cs:line 50
   at lambda_method793(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-07-25 23:34:48.447 +08:00 [ERR] NotificationJob发生错误
System.Exception: Exception while reading from stream
 ---> Npgsql.NpgsqlException (0x80004005): Exception while reading from stream
 ---> System.TimeoutException: Timeout during reading attempt
   at Npgsql.Internal.NpgsqlReadBuffer.<Ensure>g__EnsureLong|55_0(NpgsqlReadBuffer buffer, Int32 count, Boolean async, Boolean readingNotifications)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteScalar(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteScalar()
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   --- End of inner exception stack trace ---
   at FreeSql.Internal.CommonProvider.AdoProvider.LoggerException(IObjectPool`1 pool, PrepareCommandResult pc, Exception ex, DateTime dt, StringBuilder logtxt, Boolean isThrowException)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(DbConnection connection, DbTransaction transaction, CommandType cmdType, String cmdText, Int32 cmdTimeout, DbParameter[] cmdParms)
   at FreeSql.Internal.CommonProvider.AdoProvider.ExecuteScalar(CommandType cmdType, String cmdText, DbParameter[] cmdParms)
   at FreeSql.PostgreSQL.PostgreSQLCodeFirst.GetComparisonDDLStatements(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure(TypeSchemaAndName[] objects)
   at FreeSql.Internal.CommonProvider.CodeFirstProvider.SyncStructure[TEntity]()
   at FreeSql.Internal.CommonProvider.Select0Provider`2..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.Internal.CommonProvider.Select1Provider`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.Curd.PostgreSQLSelect`1..ctor(IFreeSql orm, CommonUtils commonUtils, CommonExpression commonExpression, Object dywhere)
   at FreeSql.PostgreSQL.PostgreSQLProvider`1.CreateSelectProvider[T1](Object dywhere)
   at FreeSql.Internal.CommonProvider.BaseDbProvider.Select[T1]()
   at FreeSql.DbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelect(Object dywhere)
   at FreeSql.RepositoryDbSet`1.OrmSelectInternal(Object dywhere)
   at FreeSql.BaseRepository`1.get_Select()
   at FreeSql.BaseRepository`1.Where(Expression`1 exp)
   at Fancyx.Admin.Jobs.NotificationJob.Invoke() in D:\开发代码\Code\dotnet\fancyx-admin\fancyx-server\Fancyx.Admin\Jobs\NotificationJob.cs:line 41
