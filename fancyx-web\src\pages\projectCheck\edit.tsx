// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Form, Input, Button, message, DatePicker, Select, Card, Row, Col } from 'antd';
import { createProjectCheck, updateProjectCheck, getProjectCheckById } from '@/api/projectCheck';
import { getProjectList } from '@/api/project';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

const { Option } = Select;

// 检查类型配置
const CHECK_TYPES_CONFIG = {
  '生产安全': [
    '建筑施工', '道路交通', '消防安全', '特种设备',
    '危险化学品', '职业健康', '环境保护', '应急管理'
  ],
  '质量安全': [
    '结构质量', '材料质量', '工艺质量', '验收质量'
  ]
};

// 工程进度选项
const PROJECT_PROGRESS_OPTIONS = [
  '前期准备', '基础施工', '主体施工', '装饰装修', 
  '机电安装', '竣工验收', '已完工'
];

// 检查内容模板
const CHECK_CONTENT_TEMPLATES = {
  '建筑施工': '1. 安全防护设施检查\n2. 脚手架搭设检查\n3. 临时用电检查\n4. 施工现场管理检查',
  '消防安全': '1. 消防设施配置检查\n2. 消防通道检查\n3. 电气防火检查\n4. 易燃物品管理检查',
  '特种设备': '1. 起重机械检查\n2. 升降设备检查\n3. 压力容器检查\n4. 特种设备操作人员资质检查'
};

const ProjectCheckEdit: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const isEdit = !!id;
  const [projectOptions, setProjectOptions] = useState([]);
  const [secondaryCategories, setSecondaryCategories] = useState<string[]>([]);

  // 检查类型改变时，更新二级分类选项
  const handleCheckTypeChange = (checkType: string) => {
    const categories = CHECK_TYPES_CONFIG[checkType] || [];
    setSecondaryCategories(categories);
    form.setFieldsValue({ 
      secondaryCategory: undefined,
      checkContent: '' 
    });
  };

  // 二级分类改变时，自动填充检查内容模板
  const handleSecondaryCategoryChange = (category: string) => {
    const template = CHECK_CONTENT_TEMPLATES[category];
    if (template) {
      form.setFieldsValue({ checkContent: template });
    }
  };

  useEffect(() => {
    // 获取项目列表用于下拉选择
    getProjectList({ pageSize: 1000 }).then(res => {
      const options = res.data?.items?.map((item: any) => ({
        label: item.projectName,
        value: item.id,
      })) || [];
      setProjectOptions(options);
    });

    if (isEdit) {
      getProjectCheckById(Number(id)).then(res => {
        const data = res.data;
        // 编辑时需要根据已有的检查类型初始化二级分类选项
        if (data.checkType) {
          const categories = CHECK_TYPES_CONFIG[data.checkType] || [];
          setSecondaryCategories(categories);
        }
        form.setFieldsValue({
          ...data,
          checkDate: data.checkDate ? new Date(data.checkDate) : null,
        });
      });
    } else {
      // 从URL参数获取项目信息并自动填入
      const projectId = searchParams.get('projectId');
      const projectName = searchParams.get('projectName');
      if (projectId) {
        form.setFieldsValue({
          projectId: Number(projectId),
        });
      }
    }
  }, [id, isEdit, form, searchParams]);

  const onFinish = async (values: any) => {
    const submitData = {
      ...values,
      checkDate: values.checkDate ? values.checkDate.format('YYYY-MM-DD') : null,
    };
    if (isEdit) {
      await updateProjectCheck(Number(id), submitData);
      message.success('编辑成功');
    } else {
      await createProjectCheck(submitData);
      message.success('新增成功');
    }
    navigate('/project-check');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title={isEdit ? '编辑检查任务' : '新增检查任务'} bordered={false}>
        <Form form={form} layout="vertical" onFinish={onFinish}>
          {/* 基本信息 */}
          <Card type="inner" title="基本信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="projectId" label="关联项目" rules={[{ required: true, message: '请选择项目' }]}>
                  <Select 
                    placeholder="请选择项目" 
                    options={projectOptions} 
                    showSearch
                    filterOption={(input, option) =>
                      option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="checkType" label="检查类型" rules={[{ required: true, message: '请选择检查类型' }]}>
                  <Select 
                    placeholder="请选择检查类型" 
                    onChange={handleCheckTypeChange}
                  >
                    {Object.keys(CHECK_TYPES_CONFIG).map(type => (
                      <Option key={type} value={type}>{type}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="secondaryCategory" label="二级分类" rules={[{ required: true, message: '请选择二级分类' }]}>
                  <Select 
                    placeholder="请先选择检查类型" 
                    disabled={!secondaryCategories.length}
                    onChange={handleSecondaryCategoryChange}
                  >
                    {secondaryCategories.map(category => (
                      <Option key={category} value={category}>{category}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="checkDate" label="检查日期" rules={[{ required: true, message: '请选择检查日期' }]}>
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 检查组信息 */}
          <Card type="inner" title="检查组信息" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="checkLeader" label="检查组长" rules={[{ required: true, message: '请输入检查组长' }]}>
                  <Input placeholder="请输入检查组长姓名" />
                </Form.Item>
              </Col>
              <Col span={16}>
                <Form.Item name="checkMembers" label="检查组员" rules={[{ required: true, message: '请输入检查组员' }]}>
                  <Input placeholder="请输入检查组员，多人用逗号分隔" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="clientUnit" label="委托单位">
                  <Input placeholder="请输入委托单位名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="projectProgress" label="工程进度" rules={[{ required: true, message: '请选择工程进度' }]}>
                  <Select placeholder="请选择当前工程进度">
                    {PROJECT_PROGRESS_OPTIONS.map(progress => (
                      <Option key={progress} value={progress}>{progress}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 检查内容 */}
          <Card type="inner" title="检查内容" style={{ marginBottom: 24 }}>
            <Form.Item name="checkContent" label="具体检查内容" rules={[{ required: true, message: '请输入检查内容' }]}>
              <Input.TextArea 
                rows={6} 
                placeholder="请输入具体检查内容，选择二级分类后会自动填充模板内容"
                maxLength={1000}
                showCount
              />
            </Form.Item>
            <div style={{ color: '#666', fontSize: '12px', marginTop: 8 }}>
              💡 提示：选择二级分类后会自动填充检查内容模板，您可以根据实际情况进行修改
            </div>
          </Card>

          {/* 操作按钮 */}
          <Form.Item style={{ textAlign: 'center' }}>
            <Button type="primary" htmlType="submit" size="large" style={{ marginRight: 16 }}>
              {isEdit ? '保存修改' : '创建检查任务'}
            </Button>
            <Button size="large" onClick={() => navigate('/project-check')}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ProjectCheckEdit; 