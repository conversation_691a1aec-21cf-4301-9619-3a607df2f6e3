﻿using System.ComponentModel.DataAnnotations;

namespace Fancyx.Admin.IService.Mobile.Dtos;

/// <summary>
/// 移动端创建隐患明细DTO
/// </summary>
public class MobileCreateHiddenDangerDto
{
    /// <summary>
    /// 关联检查任务ID
    /// </summary>
    [Required(ErrorMessage = "请选择关联检查任务")]
    public long ProjectCheckId { get; set; }

    /// <summary>
    /// 隐患类别
    /// </summary>
    [Required(ErrorMessage = "隐患类别不能为空")]
    public string DangerCategory { get; set; } = string.Empty;

    /// <summary>
    /// 二级分类
    /// </summary>
    [Required(ErrorMessage = "二级分类不能为空")]
    public string SecondaryCategory { get; set; } = string.Empty;

    /// <summary>
    /// 问题级别（重大/严重/一般/轻微）
    /// </summary>
    [Required(ErrorMessage = "问题级别不能为空")]
    public string ProblemLevel { get; set; } = string.Empty;

    /// <summary>
    /// 问题描述（AI识别 + 人工补充）
    /// </summary>
    [Required(ErrorMessage = "问题描述不能为空")]
    [StringLength(2000, ErrorMessage = "问题描述长度不能超过2000字符")]
    public string ProblemDescription { get; set; } = string.Empty;

    /// <summary>
    /// AI识别原始结果
    /// </summary>
    public string? AiRecognitionResult { get; set; }

    /// <summary>
    /// AI置信度（0-1）
    /// </summary>
    public double? AiConfidence { get; set; }

    /// <summary>
    /// 现场位置描述
    /// </summary>
    [Required(ErrorMessage = "现场位置不能为空")]
    [StringLength(500, ErrorMessage = "现场位置描述长度不能超过500字符")]
    public string LocationDescription { get; set; } = string.Empty;

    /// <summary>
    /// GPS坐标信息
    /// </summary>
    [Required(ErrorMessage = "GPS位置信息不能为空")]
    public MobileLocationDto Location { get; set; } = new();

    /// <summary>
    /// 现场照片URL列表
    /// </summary>
    [Required(ErrorMessage = "请至少上传一张现场照片")]
    public List<MobilePhotoInfoDto> Photos { get; set; } = new();

    /// <summary>
    /// 语音记录URL列表
    /// </summary>
    public List<MobileAudioInfoDto> AudioRecords { get; set; } = new();

    /// <summary>
    /// 整改建议
    /// </summary>
    [Required(ErrorMessage = "整改建议不能为空")]
    [StringLength(1000, ErrorMessage = "整改建议长度不能超过1000字符")]
    public string RectificationSuggestion { get; set; } = string.Empty;

    /// <summary>
    /// 整改期限
    /// </summary>
    [Required(ErrorMessage = "整改期限不能为空")]
    public DateTime RectificationDeadline { get; set; }

    /// <summary>
    /// 责任部门
    /// </summary>
    [Required(ErrorMessage = "责任部门不能为空")]
    [StringLength(200, ErrorMessage = "责任部门长度不能超过200字符")]
    public string ResponsibleDepartment { get; set; } = string.Empty;

    /// <summary>
    /// 责任人
    /// </summary>
    [Required(ErrorMessage = "责任人不能为空")]
    [StringLength(50, ErrorMessage = "责任人姓名长度不能超过50字符")]
    public string ResponsiblePerson { get; set; } = string.Empty;

    /// <summary>
    /// 责任人联系方式
    /// </summary>
    [Phone(ErrorMessage = "请输入正确的联系方式")]
    public string? ResponsibleContact { get; set; }

    /// <summary>
    /// 发现时间
    /// </summary>
    [Required(ErrorMessage = "发现时间不能为空")]
    public DateTime DiscoveryTime { get; set; }

    /// <summary>
    /// 发现人
    /// </summary>
    [Required(ErrorMessage = "发现人不能为空")]
    [StringLength(50, ErrorMessage = "发现人姓名长度不能超过50字符")]
    public string DiscoveredBy { get; set; } = string.Empty;

    /// <summary>
    /// 影响范围
    /// </summary>
    [StringLength(500, ErrorMessage = "影响范围描述长度不能超过500字符")]
    public string? ImpactScope { get; set; }

    /// <summary>
    /// 可能后果
    /// </summary>
    [StringLength(500, ErrorMessage = "可能后果描述长度不能超过500字符")]
    public string? PotentialConsequences { get; set; }

    /// <summary>
    /// 临时措施
    /// </summary>
    [StringLength(500, ErrorMessage = "临时措施描述长度不能超过500字符")]
    public string? TemporaryMeasures { get; set; }

    /// <summary>
    /// 相关法规标准
    /// </summary>
    [StringLength(500, ErrorMessage = "相关法规标准长度不能超过500字符")]
    public string? RelatedRegulations { get; set; }

    /// <summary>
    /// 额外标签
    /// </summary>
    public List<string> Tags { get; set; } = new();



    /// <summary>
    /// 设备信息
    /// </summary>
    public MobileDeviceInfoDto DeviceInfo { get; set; } = new();

    /// <summary>
    /// 创建备注
    /// </summary>
    [StringLength(500, ErrorMessage = "创建备注长度不能超过500字符")]
    public string? CreationRemarks { get; set; }
}