using Fancyx.Shared.Models;

namespace Fancyx.Admin.IService.Project.Dtos
{
    /// <summary>
    /// 项目检查查询条件 DTO
    /// </summary>
    public class ProjectCheckQueryDto : PageSearch
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        public long? ProjectId { get; set; }

        /// <summary>
        /// 检查类型
        /// </summary>
        public string CheckType { get; set; }

        /// <summary>
        /// 二级分类
        /// </summary>
        public string SecondaryCategory { get; set; }

        /// <summary>
        /// 检查组长
        /// </summary>
        public string CheckLeader { get; set; }

        /// <summary>
        /// 委托单位
        /// </summary>
        public string ClientUnit { get; set; }

        /// <summary>
        /// 检查日期起始时间
        /// </summary>
        public DateTime? CheckDateStart { get; set; }

        /// <summary>
        /// 检查日期结束时间
        /// </summary>
        public DateTime? CheckDateEnd { get; set; }
    }
}